package com.cdkit.modules.plm.process.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/4/1
 */
@Data
@Schema(description = "工艺卡片")
public class RespProcessCardVO {
    /**主键ID*/
    @Schema(description = "主键ID")
    private String id;

    /**关联process_part零件ID*/
    @Schema(description = "关联process_part零件ID")
    private String partId;

    /**关联process_template模板ID*/
    @Schema(description = "关联process_template模板ID")
    private String templateId;

    /**模板名称*/
    @Schema(description = "模板名称")
    private String templateName;

    /**卡片数据（json）*/
    @Schema(description = "卡片数据（json）")
    private String cardContent;

    /**模板内容（json）*/
    @Schema(description = "模板内容（json）")
    private String templateContent;

    /**卡片名称*/
    @Schema(description = "卡片名称")
    private String cardName;

    /**创建时间*/
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;
}
