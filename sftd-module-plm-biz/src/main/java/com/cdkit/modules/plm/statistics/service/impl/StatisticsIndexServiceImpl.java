package com.cdkit.modules.plm.statistics.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.modules.plm.enums.DesignStatusEnum;
import com.cdkit.modules.plm.enums.IndexSummaryEnum;
import com.cdkit.modules.plm.enums.ProductTreeTypeEnum;
import com.cdkit.modules.plm.process.service.IProcessCardService;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.mapper.ProductTreeMapper;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.statistics.service.IStatisticsIndexService;
import com.cdkit.modules.plm.statistics.vo.resp.RespIndexSummaryVo;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2024/5/11
 */
@Service
public class StatisticsIndexServiceImpl implements IStatisticsIndexService {
    @Resource
    @Lazy
    private IProductTreeService productTreeService;
    @Resource
    private IProcessCardService processCardService;
    @Resource
    private ProductTreeMapper productTreeMapper;

    private ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("countIndexSummary-pool-%d").build();

    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 10, TimeUnit.SECONDS, new ArrayBlockingQueue<>(5), namedThreadFactory);

    /**
     * 首页顶部概览查询
     *
     * @return 返回结果
     */
    @Override
    public RespIndexSummaryVo getIndexSummary() {
        RespIndexSummaryVo vo = new RespIndexSummaryVo();
        vo.setCategoryNum(productTreeService.count(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getType, IndexSummaryEnum.PRODUCT_TYPE.getCode()).ne(ProductTree::getPid, 0)));
        vo.setProductNum(productTreeService.count(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getType, IndexSummaryEnum.PRODUCT_KIND.getCode())));
        vo.setAssemblyNum(productTreeMapper.countByTypeAndCode(IndexSummaryEnum.PRODUCT_FINAL_ASSEMBLE.getCode()));
        vo.setPartNum(productTreeMapper.countByTypeAndCode(IndexSummaryEnum.PRODUCT_PART.getCode()));
        vo.setCardNum(processCardService.count());
        vo.setDeliveryNum(productTreeService.count(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getStatus, DesignStatusEnum.OUTBOUND.getCode())));
        return vo;
    }

    class BusinessWorker implements Callable<Long> {
        private Integer indexSummary;

        private BusinessWorker(Integer indexSummary) {
            this.indexSummary = indexSummary;
        }

        @Override
        public Long call() throws Exception {
            if (ProductTreeTypeEnum.PRODUCT_TYPE.getCode().equals(indexSummary)) {
                // 查询分类数量，不包含pid为0，pid为0则是工厂名称
                return productTreeService.count(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getType, indexSummary).ne(ProductTree::getPid, 0));
            } else if (ProductTreeTypeEnum.PRODUCT_KIND.getCode().equals(indexSummary)) {
                // 查询产品数量
                return productTreeService.count(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getType, indexSummary));
            } else if (ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode().equals(indexSummary)
                    || ProductTreeTypeEnum.PRODUCT_PART.getCode().equals(indexSummary)) {
                // 查询总装或零件--需要根据代号分组查询，因为设计和工艺会使用同一代号的零件，只根据类型查会重复
                return productTreeMapper.countByTypeAndCode(indexSummary);

            } else if (IndexSummaryEnum.PROCESS_CARD.getCode().equals(indexSummary)) {
                // 查询工艺卡片数量
                return processCardService.count();
            } else if (IndexSummaryEnum.PRODUCT_DELIVERY.getCode().equals(indexSummary)) {
                // 查询出库数量
                return productTreeService.count(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getStatus, DesignStatusEnum.OUTBOUND.getCode()));
            }

            return null;
        }
    }
}
