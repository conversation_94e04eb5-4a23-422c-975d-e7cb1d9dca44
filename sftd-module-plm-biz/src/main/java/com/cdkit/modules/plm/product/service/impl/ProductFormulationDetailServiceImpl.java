package com.cdkit.modules.plm.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.md.api.IMdMaterialApi;
import com.cdkit.md.api.IMdMaterialTypeApi;
import com.cdkit.md.entity.MdMaterial;
import com.cdkit.md.entity.MdMaterialPage;
import com.cdkit.md.entity.MdMaterialType;
import com.cdkit.modules.plm.process.entity.ProductProcessRoute;
import com.cdkit.modules.plm.process.mapper.ProductProcessRouteMapper;
import com.cdkit.modules.plm.process.service.impl.PlmApiServiceImpl;
import com.cdkit.modules.plm.product.entity.ProductFormulationDetail;
import com.cdkit.modules.plm.product.entity.ProductPart;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.mapper.ProductFormulationDetailMapper;
import com.cdkit.modules.plm.product.mapper.ProductTreeMapper;
import com.cdkit.modules.plm.product.service.IProductFormulationDetailService;
import com.google.common.base.Joiner;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: product_formulation_detail
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
@Service
@Slf4j
public class ProductFormulationDetailServiceImpl extends ServiceImpl<ProductFormulationDetailMapper, ProductFormulationDetail> implements IProductFormulationDetailService {

	@Resource
	private ProductFormulationDetailMapper productFormulationDetailMapper;
	@Resource
	private ProductProcessRouteMapper productProcessRouteMapper;
	@Resource
	private ProductTreeMapper productTreeMapper;
	@Resource
	private IMdMaterialApi mdMaterialApi;
	@Resource
	private IMdMaterialTypeApi mdMaterialTypeApi;
	@Resource
	private PlmApiServiceImpl plmApiService;

	@Override
	public List<ProductFormulationDetail> selectByMainId(String mainId) {
		List<ProductFormulationDetail> list = productFormulationDetailMapper.selectByMainId(mainId);
		List<String> collect = list.stream().map(ProductFormulationDetail::getMaterialCode).collect(Collectors.toList());
		Map<String, MdMaterialPage> stringMdMaterialPageMap = mdMaterialApi.queryMdMaterialMap(Joiner.on(",").join(collect));

		// 3. 批量获取物料类型信息
		Set<String> materialTypeIdList = stringMdMaterialPageMap.values().stream()
				.map(MdMaterialPage::getMaterialTypeId)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());
		String materialTypeIds = String.join(",", materialTypeIdList);
		List<MdMaterialType> mdMaterialTypes = mdMaterialTypeApi.queryMaterialTypes(materialTypeIds);

		List<String> boomIds = list.stream().map(ProductFormulationDetail::getBoomId).collect(Collectors.toList());
		Map<String, ProductPart> partMap = plmApiService.queryPartMap(Joiner.on(",").join(boomIds));
		for(ProductFormulationDetail pfd:list){
			MdMaterialPage mdMaterial = stringMdMaterialPageMap.get(pfd.getMaterialCode());
			if(null!=mdMaterial) {
				pfd.setMaterialName(mdMaterial.getMaterialName());
				List<MdMaterialType> filteredList = mdMaterialTypes.stream()
						.filter(route -> Objects.equals(route.getId(), mdMaterial.getMaterialTypeId())) // 假设 productId 是 Long 类型
						.collect(Collectors.toList());
				if (!filteredList.isEmpty()) {
					pfd.setMaterialTypeName(filteredList.get(0).getTypeName());
				}
				pfd.setUnitName(mdMaterial.getBasicUnitId());
			}
			//ProductPart productPart = plmApiService.queryPart(pfd.getBoomId(), pfd.getMaterialCode());
			if(null!=partMap.get(pfd.getBoomId())) {
				ProductPart pp1 = partMap.get(pfd.getBoomId());
				if(null!=pp1) {
					pfd.setFungibleMaterialCodes(pp1.getFungibleMaterialCodes());
				}
			}

		}
		return list;
	}

	/**
	 * 根据工序查询物料绑定信息
	 *
	 * @param processId 工序ID
	 * @param productId 产成品ID(bomId)
	 * @return 物料组成ID
	 */
	@Override
	public List<String> processProductList(String processId, String productId) {
		ProductProcessRoute productProcessRoute = productProcessRouteMapper.selectOne(new LambdaQueryWrapper<ProductProcessRoute>()
				.eq(ProductProcessRoute::getCurrProductId, productId)
				.eq(ProductProcessRoute::getProcessId, processId)
				.eq(ProductProcessRoute::getIsNew, 1));
		log.info("工序查询物料绑定工艺路线信息:{}", JSON.toJSONString(productProcessRoute));
		if (productProcessRoute != null) {
			String outPutProductId = productProcessRoute.getProductId();
			List<ProductTree> productTrees = productTreeMapper.selectList(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getPid, outPutProductId));
			return productTrees.stream().map(ProductTree::getId).toList();
		}

		return List.of();
	}

	/**
	 * 根据工序查询绑定的产出物
	 *
	 * @param processId 工序ID
	 * @param productId 产成品ID(bomId)
	 * @return 产出物ID
	 */
	@Override
	public String processOutputProduct(String processId, String productId) {
		ProductProcessRoute productProcessRoute = productProcessRouteMapper.selectOne(new LambdaQueryWrapper<ProductProcessRoute>()
				.eq(ProductProcessRoute::getCurrProductId, productId)
				.eq(ProductProcessRoute::getProcessId, processId)
				.eq(ProductProcessRoute::getIsNew, 1));
		log.info("工序查询绑定的产出物工艺路线信息:{}", JSON.toJSONString(productProcessRoute));
		if (productProcessRoute != null) {
			String outPutProductId = productProcessRoute.getProductId();
			ProductTree productTree = productTreeMapper.selectById(outPutProductId);
			if (productTree != null && !productTree.getName().equals("虚拟件")) {
				return productTree.getId();
			}
		}

		return "";
	}
}
