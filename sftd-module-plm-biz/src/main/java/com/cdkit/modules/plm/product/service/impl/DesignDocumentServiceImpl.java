package com.cdkit.modules.plm.product.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdkit.modules.plm.product.entity.DesignDocument;
import com.cdkit.modules.plm.product.entity.DesignDocumentHistory;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.mapper.DesignDocumentMapper;
import com.cdkit.modules.plm.product.mapper.ProductTreeMapper;
import com.cdkit.modules.plm.product.service.DesignDocumentHistoryService;
import com.cdkit.modules.plm.product.service.IDesignDocumentService;
import com.cdkit.modules.plm.product.vo.req.ChangeWorkVersionReqVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: design_document
 * @Author: mc
 * @Date: 2024-03-26
 * @Version: V1.0
 */
@Service
@Slf4j
@AllArgsConstructor
public class DesignDocumentServiceImpl extends ServiceImpl<DesignDocumentMapper, DesignDocument> implements IDesignDocumentService {

    private final DesignDocumentHistoryService designDocumentHistoryService;
    private final ProductTreeMapper productTreeMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeWorkVersion(ChangeWorkVersionReqVO changeWorkVersionReqVO) {
        //历史
        DesignDocumentHistory historyDocument = designDocumentHistoryService.getById(changeWorkVersionReqVO.getHistoryId());
        //当前的
        ProductTree currProductTree = productTreeMapper.selectById(historyDocument.getDesignDocumentId());
        DesignDocument currDocument = this.getById(historyDocument.getDesignDocumentId());
        DesignDocument tmpCurrDocument = BeanUtil.copyProperties(currDocument, DesignDocument.class);

        BeanUtil.copyProperties(historyDocument, currDocument, "id");
        BeanUtil.copyProperties(tmpCurrDocument, historyDocument, "id");

        currProductTree.setName(currDocument.getName());

        this.updateById(currDocument);
        designDocumentHistoryService.updateById(historyDocument);
        productTreeMapper.updateById(currProductTree);


    }

}
