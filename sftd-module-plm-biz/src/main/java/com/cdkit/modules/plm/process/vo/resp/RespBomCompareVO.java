package com.cdkit.modules.plm.process.vo.resp;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/15
 */
@Data
public class RespBomCompareVO {
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;
    /**
     * 编码/代号
     */
    @Schema(description = "编码/代号")
    private String code;

    /**
     * 版本号
     */
    @Schema(description = "版本号")
    private String version;
    /**
     * 零件类型
     */
    @Schema(description = "零件类型")
    private String partType;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;
    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;
    /**
     * 材料
     */
    @Schema(description = "材料")
    private String materialName;
    /**
     * 处理类型
     */
    @Schema(description = "处理类型")
    private String handleType;
    /**
     * 生产类型
     */
    @Schema(description = "生产类型")
    private String manufactureType;
    /**
     * 规格
     */
    @Schema(description = "规格")
    private String specs;
    /**
     * 结构类型
     */
    @Schema(description = "结构类型")
    private String structType;
    /**
     * 重量
     */
    @Schema(description = "重量")
    private String weight;
    /**
     * 装配数量
     */
    @Schema(description = "装配数量")
    private BigDecimal assembleQuantity;
    /**
     * 装配序号
     */
    @Schema(description = "装配序号")
    private Integer assembleSort;
    /**
     * 装配单位
     */
    @Schema(description = "装配单位")
    private String assembleUnit;

    /**
     * 差异对比
     */
    @Schema(description = "BOM比较颜色说明：1红色:两个对象的属性不同 2蓝色：新增的对象 3绿色：被删除的对象")
    private String diff;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {return false;}
        RespBomCompareVO that = (RespBomCompareVO) o;
        return Objects.equals(name, that.name) &&
                Objects.equals(description, that.description) &&
                Objects.equals(code, that.code) &&
                Objects.equals(version, that.version) &&
                Objects.equals(partType, that.partType) &&
                Objects.equals(createBy, that.createBy) &&
                Objects.equals(updateBy, that.updateBy) &&
                Objects.equals(materialName, that.materialName) &&
                Objects.equals(handleType, that.handleType) &&
                Objects.equals(manufactureType, that.manufactureType) &&
                Objects.equals(specs, that.specs) &&
                Objects.equals(structType, that.structType) &&
                Objects.equals(weight, that.weight) &&
                Objects.equals(assembleQuantity, that.assembleQuantity) &&
                Objects.equals(assembleSort, that.assembleSort) &&
                Objects.equals(assembleUnit, that.assembleUnit);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, description, code, version, partType, createBy, updateBy, materialName, handleType, manufactureType, specs, structType, weight, assembleQuantity, assembleSort, assembleUnit);
    }
}
