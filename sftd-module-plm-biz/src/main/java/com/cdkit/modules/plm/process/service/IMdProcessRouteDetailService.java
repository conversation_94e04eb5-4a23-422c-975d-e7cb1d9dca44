package com.cdkit.modules.plm.process.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.entity.MdProcessRouteDetail;

import java.util.List;

/**
 * @Description: 工艺路线明细
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
public interface IMdProcessRouteDetailService extends IService<MdProcessRouteDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<MdProcessRouteDetail>
	 */
	public List<MdProcessRouteDetail> selectByMainId(String mainId);
}
