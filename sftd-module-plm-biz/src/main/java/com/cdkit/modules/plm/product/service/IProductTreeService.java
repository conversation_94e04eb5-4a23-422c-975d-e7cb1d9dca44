package com.cdkit.modules.plm.product.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletRequest;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.DesignDocument;
import com.cdkit.modules.plm.product.entity.ProductPartExtend;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.vo.req.*;
import com.cdkit.modules.plm.product.vo.resp.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * @Description: product_tree
 * @Author: zhao yang
 * @Date: 2024-03-26
 * @Version: V1.0
 */
public interface IProductTreeService extends IService<ProductTree> {

    /**
     * 根据根节点id查询树形
     *
     * @param rootIds
     * @param nodeType
     * @return
     */
    List<ProductTreeDTO> tree(List<String> rootIds, NodeTypeEnum nodeType,String materialCodeOrName);

    /**
     * 新增产品分类
     *
     * @param addProductTypeVO
     */
    void addProductType(AddProductTypeVO addProductTypeVO);

    /**
     * 编辑产品分类
     *
     * @param editProductTypeVO
     */
    void editProductType(EditProductTypeVO editProductTypeVO);

    /**
     * 新增产品种类
     *
     * @param addProductKindVO
     */
    void addProductKind(AddProductKindVO addProductKindVO);

    /**
     * 编辑产品种类
     *
     * @param editProductVO
     */
    void editProductKind(EditProductKindVO editProductVO);

    /**
     * （级联）删除产品类型
     *
     * @param id
     * @param cascade
     */
    void cascadeDeleteNode(String id, boolean cascade);

    /**
     * 新增总装/零部件
     *
     * @param addProductPartVO
     */
    void addProductPart(AddProductPartVO addProductPartVO);

    /**
     * 编辑总装/零部件
     *
     * @param editProductPartVO
     */
    void editProductPart(EditProductPartVO editProductPartVO);

    /**
     * 获取父节点下所有的文件
     *
     * @param pid
     * @return
     */
    Page<DesignDocumentVO> listAllDesignDocumentByPid(String pid, String queryCondition, String category, Integer pageNo, Integer pageSize, Integer nodeType);

    /**
     * 获取父节点下所有的文件
     *
     * @param pid
     * @return
     */
    public List<DesignDocumentVO> listAllDesignDocumentByPid(String pid, String queryCondition, String category, Integer nodeType);

    /**
     * 根据父节点下所有的子节点
     *
     * @param pid
     * @return
     */
    List<? extends ProductTree> getTileChildrenByPid(String pid);

    /**
     * 根据id获取产品总装/零部件信息
     *
     * @param id
     * @return
     */
    ProductPartVO queryProductPartById(String id);

    /**
     * 给指定节点下添加设计文件
     *
     * @param pid
     * @param files
     */
    void importBatch(String pid, MultipartFile[] files) throws Exception;

    /**
     * 新增借用零部件
     *
     * @param addBorrowPartVO
     */
    void addBorrowPart(AddBorrowPartVO addBorrowPartVO);

    /**
     * 置为原件
     *
     * @param productPartId 借用件id
     */
    void toOrigin(String productPartId);

    /**
     * 获取总装/零部件的借用信息
     *
     * @param productPartId
     * @return
     */
    Page<PartBorrowInfoVO> getBorrowInfoById(String productPartId, String queryCondition, Integer pageNo, Integer pageSize);

    /**
     * 查询设计文件详情
     *
     * @param id
     * @return
     */
    DesignDocumentVO queryDesignDocumentById(String id);

    /**
     * 发布、取消发布设计文档状态
     *
     * @param designDocumentPublishVO
     */
    void designDocumentPublish(DesignDocumentPublishVO designDocumentPublishVO,HttpServletRequest request);

    /**
     * 图纸出入库
     *
     * @param id
     */
    void designDocumentOutbound(String id, Boolean cancel);

    /**
     * 新增图纸借用
     *
     * @param addBorrowDocumentVO
     */
    void addBorrowDesignDocument(AddBorrowDocumentVO addBorrowDocumentVO);

    /**
     * 图纸置为源图纸
     *
     * @param targetId
     */
    void toDocumentOrigin(String targetId);

    /**
     * 获取图纸借用信息
     *
     * @param id
     * @return
     */
    List<DocumentBorrowInfoVO> getDocumentBorrowInfoById(String id);

    /**
     * 出库列表
     *
     * @param queryDesignDocumentVO
     * @return
     */
    Page<OutboundDocumentVO> outboundDocumentList(QueryDesignDocumentVO queryDesignDocumentVO);

    /**
     * 图纸导入
     *
     * @param pid
     * @param files
     * @param nodeType
     * @throws Exception
     */
    void importDocument(String pid, MultipartFile files, Integer nodeType) throws Exception;

    /**
     * 图纸入库
     *
     * @param id
     * @param file
     */
    void designDocumentInbound(String id, MultipartFile file) throws Exception;

    /**
     * 取消借用
     *
     * @param id
     */
    void cancelBorrow(String id);

    /**
     * 导入工艺路线
     *
     * @param productTreeId
     * @param file
     */
    void importProcessRoute(String productTreeId, MultipartFile file);

    /**
     * 通过id查询产品属性（来源主数据）
     *
     * @param id 主键ID
     * @return 结果返回
     */
    ProductAttributesVO queryAttributeById(String id);

    /**
     * 通过物料编码查询物料详情
     *
     * @param materialCode 物料编码
     * @return 结果返回
     */
    ProductAttributesVO queryAttributeByMaterialCode(String materialCode);

    /**
     * 查询产品定制属性
     *
     * @return 定制属性列表
     */
    List<ProductPartExtend> queryProductPartExtendForAdd();

    String copyTree(String id, String productOrderNum);

    String copyTreeEx(String id);

    List<ProductTree> getTreeList(String productId);

    /**
     * 懒加载树
     * @param rootIds 根节点
     * @param nodeTypeEnum 节点类型
     * @return 树
     */
    List<ProductTreeDTO> treeLazy(String rootIds, NodeTypeEnum nodeTypeEnum, String materialCodeOrName);

    /**
     * 图纸审核
     * @param designDocument
     * @param request
     * @return
     */
    String auditDocument(DesignDocument designDocument, HttpServletRequest request);

    /**
     * 自动签字
     * @param designDocument
     * @param request
     * @return
     */
    String writeSign(DesignDocument designDocument, HttpServletRequest request);

    /**
     * 审核驳回
     * @param designDocument
     * @param request
     * @return
     */
    String reject(DesignDocument designDocument, HttpServletRequest request);

    /**
     * 复用
     * @param pid designDocumentHistoryId
     * @param
     * @return
     */
    String reuseDocument(String pid, String designDocumentHistoryId);

}
