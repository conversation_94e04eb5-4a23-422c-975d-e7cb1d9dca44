<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.plm.product.mapper.DesignDocumentMapper">
    <select id="outboundDocumentList" resultMap="outboundDocumentResultMap">
        SELECT dd.id,pt.name, pt.path, dd.file_type, locked_by, locked_time,dd.doc_id
        FROM product_tree pt
                 INNER JOIN design_document dd ON pt.id = dd.id
        WHERE pt.node_type = 1
          and pt.type = 5
          and pt.del_flag = 0
        <if test="param.queryCondition != null and param.queryCondition != ''">
            and pt.name LIKE CONCAT('%', #{param.queryCondition}, '%')
        </if>
        <if test="param.startTime != null">
            and dd.locked_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            and dd.locked_time  <![CDATA[<=]]> #{param.endTime}
        </if>
        <if test="param.userId != null and param.userId != ''">
            AND dd.locked_by = #{param.userId}
        </if>
        and dd.locked_by is not null
    </select>

    <resultMap id="outboundDocumentResultMap" type="com.cdkit.modules.plm.product.vo.resp.OutboundDocumentVO">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="docId" column="doc_id"/>
        <result property="path" column="path"/>
        <result property="fileType" column="file_type"/>
        <result property="lockedBy" column="locked_by"/>
        <result property="lockedTime" column="locked_time"/>
    </resultMap>
</mapper>
