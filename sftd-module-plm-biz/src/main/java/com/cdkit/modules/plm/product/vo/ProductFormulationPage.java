package com.cdkit.modules.plm.product.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.cdkit.modules.plm.product.entity.ProductFormulationDetail;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;

import com.cdkitframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;


/**
 * @Description: product_formulation
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
@Data
@Schema(description="product_formulationPage对象")
public class ProductFormulationPage {

	/**主键*/
	@Schema(description = "主键")
    private String id;
	/**物料名称*/
	@Excel(name = "物料名称", width = 15)
	@Schema(description = "物料名称")
    private String materialName;
	/**物料编码*/
	@Excel(name = "物料编码", width = 15)
	@Schema(description = "物料编码")
    private String materialCode;
	/**产品类别 来自物料分类*/
	@Excel(name = "产品类别 来自物料分类", width = 15)
	@Schema(description = "产品类别 来自物料分类")
    private String materialTypeId;
	/**配方类别1-正式配方 2-临时配方*/
	@Excel(name = "配方类别1-正式配方 2-临时配方", width = 15)
	@Schema(description = "配方类别1-正式配方 2-临时配方")
    private String formulaType;
	/**配方编码*/
	@Excel(name = "配方编码", width = 15)
	@Schema(description = "配方编码")
    private String formulaCode;
	/**配方状态1-新建 2-审核中 3-已审核*/
	@Excel(name = "配方状态1-新建 2-审核中 3-已审核", width = 15)
	@Schema(description = "配方状态1-新建 2-审核中 3-已审核")
    private String formulaStatus;
	/**启用状态 1启用 0停用*/
	@Excel(name = "启用状态 1启用 0停用", width = 15)
	@Schema(description = "启用状态 1启用 0停用")
    private Integer enableStatus;
	/**料件类型(配方性质)*/
	@Excel(name = "料件类型(配方性质)", width = 15)
	@Schema(description = "料件类型(配方性质)")
    private String itemType;
	/**临时配方有效期开始时间*/
	@Excel(name = "临时配方有效期开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "临时配方有效期开始时间")
    private Date expirationDateStart;
	/**临时配方有效期结束时间*/
	@Excel(name = "临时配方有效期结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "临时配方有效期结束时间")
    private Date expirationDateEnd;
	/**工艺流程*/
	@Excel(name = "工艺流程", width = 15)
	@Schema(description = "工艺流程")
    private String process;
	/**关键工艺控制指标*/
	@Excel(name = "关键工艺控制指标", width = 15)
	@Schema(description = "关键工艺控制指标")
    private String keyIndicators;
	/**包装要求*/
	@Excel(name = "包装要求", width = 15)
	@Schema(description = "包装要求")
    private String packageRequirements;
	/**注意事项*/
	@Excel(name = "注意事项", width = 15)
	@Schema(description = "注意事项")
    private String notes;
	/**执行期限*/
	@Excel(name = "执行期限", width = 15)
	@Schema(description = "执行期限")
    private String executionDeadline;
	/**应用区域*/
	@Excel(name = "应用区域", width = 15)
	@Schema(description = "应用区域")
    private String applicationArea;
	/**备注*/
	@Excel(name = "备注", width = 15)
	@Schema(description = "备注")
    private String remark;
	/**创建人*/
	@Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
	@Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
	@Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
	@Schema(description = "是否删除")
    private Integer delFlag;
	/**流程实例wiid*/
	@Excel(name = "流程实例wiid", width = 15)
	@Schema(description = "流程实例wiid")
    private String wiid;
	/**工艺路线ID*/
	@Excel(name = "工艺路线ID", width = 15)
	@Schema(description = "工艺路线ID")
    private String processRouteId;
	/**boomid*/
	@Excel(name = "boomid", width = 15)
	@Schema(description = "boomid")
    private String boomId;

	@ExcelCollection(name="product_formulation_detail")
	@Schema(description = "product_formulation_detail")
	private List<ProductFormulationDetail> productFormulationDetailList;


	@Schema(description = "delete_formulation_detail")
	private List<ProductFormulationDetail> deleteFormulationDetailList;

	@Excel(name = "配方版本", width = 15)
	@Schema(description = "配方版本")
	private String formulaVersion;

	@Excel(name = "productFormulationMasterId", width = 15)
	@Schema(description = "productFormulationMasterId")
	private String productFormulationMasterId;


	private String fileUrlList;

	/**配方性质*/
	@Schema(description = "配方性质")
	private String formulaNature;

	@TableField(exist = false)
	private String formulaCodeUpdateFlag;

}
