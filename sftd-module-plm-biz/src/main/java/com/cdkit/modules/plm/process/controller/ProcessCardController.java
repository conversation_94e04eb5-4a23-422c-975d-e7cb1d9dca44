package com.cdkit.modules.plm.process.controller;

import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.aspect.annotation.AutoLog;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.process.entity.ProcessCard;
import com.cdkit.modules.plm.process.service.IProcessCardService;
import com.cdkit.modules.plm.process.vo.req.ReqProcessCardAddVO;
import com.cdkit.modules.plm.process.vo.resp.RespProcessCardVO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 工艺卡片
 * @Author: mc
 * @Date:   2024-04-01
 * @Version: V1.0
 */
@Tag(name="工艺卡片")
@RestController
@RequestMapping("/processCard")
@Slf4j
public class ProcessCardController extends CdkitController<ProcessCard, IProcessCardService> {
	@Autowired
	private IProcessCardService processCardService;

	/**
	 *  保存工艺卡片
	 *
	 * @param reqProcessCardAddVO 请求参数
	 * @return 返回结果
	 */
	@AutoLog(value = "保存工艺卡片")
	@Operation(summary="保存工艺卡片", description="保存工艺卡片")
	//@RequiresPermissions("process:processCard:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody @Validated List<ReqProcessCardAddVO> reqProcessCardAddVO) {
		processCardService.saveCard(reqProcessCardAddVO);
		return Result.OK("添加成功！");
	}

	/**
	 *  通过id删除
	 *
	 * @param id 主键ID
	 * @return 返回结果
	 */
	@AutoLog(value = "通过id删除")
	@Operation(summary="通过id删除", description="通过id删除")
	//@RequiresPermissions("process:processCard:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id") String id) {
		processCardService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 通过零件ID查询工艺卡片
	 *
	 * @param partId 零件ID
	 * @return 返回结果
	 */
	@Operation(summary="通过零件ID查询工艺卡片", description="通过零件ID查询工艺卡片")
	@GetMapping(value = "/listCardByPartId")
	public Result<List<RespProcessCardVO>> listCardByPartId(@RequestParam(name="partId") String partId) {
		List<RespProcessCardVO> list = processCardService.listCardByPartId(partId);
		if(list == null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(list);
	}

	 /**
	  * 通过卡片ID查询工艺卡片
	  *
	  * @param id 主键ID
	  * @return 返回结果
	  */
	 @Operation(summary="通过卡片ID查询工艺卡片", description="通过卡片ID查询工艺卡片")
	 @GetMapping(value = "/queryById")
	 public Result<RespProcessCardVO> queryById(@RequestParam(name="id") String id) {
		 RespProcessCardVO processCard = processCardService.queryById(id);

		 if(processCard == null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(processCard);
	 }
}
