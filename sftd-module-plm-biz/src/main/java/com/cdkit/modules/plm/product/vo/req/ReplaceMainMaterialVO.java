package com.cdkit.modules.plm.product.vo.req;


import com.cdkit.modules.plm.product.vo.resp.FormulationReplaceVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author：mr
 * @name：ReplaceMainMaterialVO
 * @Date：2024/4/16 18:00
 */
@Data
@Schema(description ="替换主辅料vo")
public class ReplaceMainMaterialVO {
    @Schema(description ="新物料编码")
    private String newMaterialCode;
    @Schema(description ="列表配方数据")
    private List<FormulationReplaceVo> formulationReplaceVo;
    //是否将原主料替换成辅料 1-是 2-否
    private int replaceFlag;
}
