package com.cdkit.modules.plm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * BOM转换对比
 * <AUTHOR>
 * @date 2024/4/1
 */
@Getter
@AllArgsConstructor
public enum BomConvertDiffEnum {
    /**
     * 橙色代表当前层级下未找到相同零件
     */
    ORANGE(1,"ORANGE"),
    /**
     * 黄色表示零件相同，但属性不同
     */
    YELLOW(2,"YELLOW"),
    /**
     * 蓝色表示图纸有差异
     */
    BLUE(3,"BLUE"),
    /**
     * 白色表示零件一致
     */
    WHITE(4,"WHITE");

    private final Integer code;
    private final String desc;
}
