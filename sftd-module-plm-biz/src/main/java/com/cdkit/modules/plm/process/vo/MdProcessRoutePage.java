package com.cdkit.modules.plm.process.vo;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import com.cdkit.modules.plm.process.entity.MdProcessRouteDetail;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkitframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;


/**
 * @Description: 工艺路线主数据
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Data
@Schema(description="md_process_routePage对象", name="工艺路线主数据")
public class MdProcessRoutePage {

	/**主键*/
	@Schema(description = "主键")
    private String id;
	/**状态*/
	@Excel(name = "状态", width = 15)
	@Schema(description = "状态")
    private String useStatus;
	/**工艺路线编码*/
	@Excel(name = "工艺路线编码", width = 15)
	@Schema(description = "工艺路线编码")
    private String processRouteCode;
	/**工艺路线名称*/
	@Excel(name = "工艺路线名称", width = 15)
	@Schema(description = "工艺路线名称")
    private String processRouteName;
	/**创建人*/
	@Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
	@Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
	@Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
	@Schema(description = "是否删除")
    private Integer delFlag;

	@ExcelCollection(name="工艺路线明细")
	@Schema(description = "工艺路线明细")
	private List<MdProcessRouteDetail> mdProcessRouteDetailList;

}
