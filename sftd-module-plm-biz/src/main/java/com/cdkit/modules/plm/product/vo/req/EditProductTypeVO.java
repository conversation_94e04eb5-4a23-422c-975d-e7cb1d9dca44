package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：UpdateProductVO
 * @Date：2024/3/26 16:40
 */
@Data
@Schema(description = "更新产品vo")
public class EditProductTypeVO {
    @NotBlank(message = "产品id不能为空")
    private String id;
    @Schema(description = "名称")
    private String name;

    @Schema(description = "编码/代号")
    private String code;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "父id，新增时候没指定父id则传null")
    private String pid;
}
