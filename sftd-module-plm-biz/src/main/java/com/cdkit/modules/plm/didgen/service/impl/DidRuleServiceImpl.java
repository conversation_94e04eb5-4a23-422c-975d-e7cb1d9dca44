package com.cdkit.modules.plm.didgen.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.plm.didgen.entity.DidGenHistory;
import com.cdkit.modules.plm.didgen.entity.DidRule;
import com.cdkit.modules.plm.didgen.mapper.DidRuleMapper;
import com.cdkit.modules.plm.didgen.service.DidGenHistoryService;
import com.cdkit.modules.plm.didgen.service.DidRuleService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class DidRuleServiceImpl extends ServiceImpl<DidRuleMapper, DidRule> implements DidRuleService {
    static RedisTemplate<String, Long> redisTemplate = SpringUtil.getBean("redisTemplate");
    static DidRuleMapper didRuleMapper = SpringUtil.getBean(DidRuleMapper.class);
    static ExecutorService executorService = SpringUtil.getBean(ExecutorService.class);
    static RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);


    static final ExpressionParser EXPRESSION_PARSER = new SpelExpressionParser();
    static final String REDISSON_PREFIX = "redisson:";
    static final String DID_PREFIX = "did:";
    static final Method SERIAL = ReflectUtil.getMethod(DidRuleServiceImpl.class, "serial", String.class, String.class, Integer.class);
    static final Method DATE_FORMAT = ReflectUtil.getMethod(DidRuleServiceImpl.class, "dateFormat", String.class);


    @Override
    public String getDidCode(String code, Map<String, Object> variables) {
        DidRule didRule = getDidRule(code);
        return parseExpression(didRule.getExpression(), variables);
    }

    public String parseExpression(String expression, Map<String, Object> variables) {
        StandardEvaluationContext evaluationContext = new StandardEvaluationContext();
        evaluationContext.registerFunction("serial", SERIAL);
        evaluationContext.registerFunction("date_format", DATE_FORMAT);
        variables.forEach(evaluationContext::setVariable);
        return EXPRESSION_PARSER.parseExpression(expression).getValue(evaluationContext, String.class);
    }

    //@Cacheable(key = "#code", cacheNames = "didRule")
    @Override
    public DidRule getDidRule(String code) {
        return didRuleMapper.selectOne(new LambdaQueryWrapper<DidRule>().eq(DidRule::getDidCode, code));
    }


    public static String dateFormat(String dateFormat) {
        return DateUtil.format(new Date(), dateFormat);
    }

    public static String serial(String code, String prefixKey, int len) {
        int retry = 3;
        String lockKey = REDISSON_PREFIX + prefixKey;
        String didKey = DID_PREFIX + prefixKey;
        while (retry-- > 0) {
            RLock lock = null;
            try {
                lock = redissonClient.getFairLock(lockKey);
                boolean tryLock = lock.tryLock(5, TimeUnit.MILLISECONDS);
                if (tryLock) {
                    Date date = new Date();
                    Long serial;
                    DidGenHistoryService didGenHistoryService = SpringUtil.getBean(DidGenHistoryService.class);
                    if (Boolean.FALSE.equals(redisTemplate.hasKey(didKey))) {
                        DidGenHistory didGenHistory = didGenHistoryService.getOne(new LambdaQueryWrapper<DidGenHistory>().eq(DidGenHistory::getPrefixKey, prefixKey));
                        if (didGenHistory != null) {
                            serial = didGenHistory.getCurrNum() + 1;
                        } else {
                            serial = 1L;
                        }
                        redisTemplate.opsForValue().set(didKey, serial);
                    } else {
                        serial = redisTemplate.opsForValue().increment(didKey);
                    }
                    //保存单号生成历史
                    executorService.submit(() -> saveDidGenHistory(code, prefixKey, serial, date, didGenHistoryService));
                    String serialNo = String.format(("%0" + len + "d"), serial);
                    String bizNo = prefixKey + serialNo;
                    log.debug("生成的业务单号，{}", bizNo);
                    return bizNo;
                }
                log.warn("未获取到锁，次数：{}", retry);
            } catch (InterruptedException e){
                Thread.currentThread().interrupt();
                log.error("生成单号报错，线程中断， code：{}，prefix：{}", code, prefixKey, e);
            } catch (Exception e) {
                log.error("生成单号报错，code：{}，prefix：{}", code, prefixKey, e);
            } finally {
                if (lock != null) {
                    lock.unlock();
                }
            }
        }
        throw new CdkitCloudException(String.format("生成业务单号异常，%s，retry：%s", prefixKey, retry));
    }

    public static void saveDidGenHistory(String code, String prefixKey, Long serial, Date date, DidGenHistoryService didGenHistoryService) {
        try {
            DidGenHistory didGenHistory = didGenHistoryService.getById(prefixKey);
            if (didGenHistory == null) {
                didGenHistory = new DidGenHistory();
                didGenHistory.setDidCode(code);
                didGenHistory.setPrefixKey(prefixKey);
                didGenHistory.setCurrNum(serial);
                didGenHistory.setGenTime(date);
                didGenHistoryService.save(didGenHistory);
            } else {
                didGenHistory.setCurrNum(serial);
                didGenHistory.setGenTime(date);
                UpdateWrapper<DidGenHistory> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("prefix_key", didGenHistory.getPrefixKey())
                        .lt("curr_num", serial)
                        .set("curr_num", serial)
                        .set("gen_time", date);
                didGenHistoryService.update(null, updateWrapper);
            }
        } catch (Exception e) {
            log.error("生成业务单号异常，code：{}，prefixKey：{}，serial：{}", code, prefixKey, serial, e);
        }
    }
}
