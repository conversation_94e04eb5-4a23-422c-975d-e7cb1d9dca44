package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.plm.enums.OperationTypeEnum;
import com.cdkit.modules.plm.operation.service.IOperationLogService;
import com.cdkit.modules.plm.process.entity.ProcessCard;
import com.cdkit.modules.plm.process.entity.ProcessTemplate;
import com.cdkit.modules.plm.process.mapper.ProcessCardMapper;
import com.cdkit.modules.plm.process.mapper.ProcessTemplateMapper;
import com.cdkit.modules.plm.process.service.IProcessCardService;
import com.cdkit.modules.plm.process.vo.req.ReqProcessCardAddVO;
import com.cdkit.modules.plm.process.vo.resp.RespProcessCardVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description: 工艺卡片业务实现
 * @Author: mc
 * @Date:   2024-04-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class ProcessCardServiceImpl extends ServiceImpl<ProcessCardMapper, ProcessCard> implements IProcessCardService {
    @Resource
    private ProcessCardMapper processCardMapper;
    @Resource
    private ProcessTemplateMapper processTemplateMapper;
    @Resource
    private IOperationLogService operationLogUtil;
    /**
     * 保存工艺卡片
     *
     * @param reqProcessCardAddVO 请求参数
     */
    @Override
    public void saveCard(List<ReqProcessCardAddVO> reqProcessCardAddVO) {
        log.info("保存工艺卡片请求参数：{}", JSON.toJSONString(reqProcessCardAddVO));
        for (ReqProcessCardAddVO item : reqProcessCardAddVO) {
            ProcessCard processCard = BeanUtil.toBean(item, ProcessCard.class);
            if (StringUtils.isEmpty(item.getId())) {
                ProcessCard processTemplateOrg = this.getOne(new LambdaQueryWrapper<ProcessCard>().eq(ProcessCard::getCardName, item.getCardName()));
                if (processTemplateOrg != null) {
                    throw new CdkitCloudException("工艺卡片【"+ item.getCardName()+"】已存在");
                }
                processCardMapper.insert(processCard);
            } else {
                ProcessCard processTemplateOrg = this.getById(item.getId());
                if (!processTemplateOrg.getCardName().equals(item.getCardName())) {
                    ProcessCard processCardOld = this.getOne(new LambdaQueryWrapper<ProcessCard>().eq(ProcessCard::getCardName, item.getCardName()));
                    if (processCardOld != null) {
                        throw new CdkitCloudException("工艺卡片【"+ item.getCardName()+"】已存在");
                    }
                }
                processCardMapper.updateById(processCard);
            }

            operationLogUtil.insertOperationLog(OperationTypeEnum.ADD.getType() + "工艺卡片", item.getCardName());
        }


    }

    /**
     * 根据零件ID查询工艺卡片
     *
     * @param partId 零件ID
     * @return 工艺卡片信息
     */
    @Override
    public List<RespProcessCardVO> listCardByPartId(String partId) {
        ArrayList<RespProcessCardVO> rsList = new ArrayList<>();
        List<ProcessCard> list = processCardMapper.selectList(new LambdaQueryWrapper<ProcessCard>().eq(ProcessCard::getPartId, partId));
        for (ProcessCard item : list) {
            RespProcessCardVO respProcessCardVO = BeanUtil.toBean(item, RespProcessCardVO.class);
            respProcessCardVO.setTemplateName(Optional.ofNullable(processTemplateMapper.selectById(respProcessCardVO.getTemplateId()))
                    .orElse(new ProcessTemplate()).getTemplateName());
            rsList.add(respProcessCardVO);
        }
        return rsList;
    }

    /**
     * 通过卡片ID查询工艺卡片
     *
     * @param id 主键ID
     * @return 返回结果
     */
    @Override
    public RespProcessCardVO queryById(String id) {
        ProcessCard processCard = processCardMapper.selectById(id);
        RespProcessCardVO respProcessCardVO = BeanUtil.toBean(processCard, RespProcessCardVO.class);
        ProcessTemplate processTemplate = Optional.ofNullable(processTemplateMapper.selectById(respProcessCardVO.getTemplateId()))
                .orElse(new ProcessTemplate());
        respProcessCardVO.setTemplateContent(processTemplate.getTemplateContent());
        respProcessCardVO.setTemplateName(processTemplate.getTemplateName());
        return respProcessCardVO;
    }
}
