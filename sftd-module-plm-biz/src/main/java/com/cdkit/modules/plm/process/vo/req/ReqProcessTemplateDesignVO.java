package com.cdkit.modules.plm.process.vo.req;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */
@Data
@Schema(description = "工艺模板设计")
public class ReqProcessTemplateDesignVO {
    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "主键ID不能为空")
    @Schema(description = "主键ID", required = true)
    private String id;

    /**模板内容（json）*/
    @NotBlank(message = "模板内容不能为空")
    @Schema(description = "模板内容（json）")
    private String templateContent;
}
