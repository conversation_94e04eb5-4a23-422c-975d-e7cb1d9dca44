package com.cdkit.modules.plm.process.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MdProcessVo implements Serializable {
    private static final long serialVersionUID = 5647207186927183254L;
    private String id;
    @Schema(description = "序号")
    @NotBlank(message = "序号不能为空！")
    private String processNumber;

    @NotBlank(message = "当前产品id不能为空！")
    private String currProductId;

//    @NotBlank(message = "工序内容不能为空！")
    private String processContent;

    @NotBlank(message = "产品id不能为空！")
    private String productId;
//    @NotBlank(message = "工艺路线id不能为空！")
    private String processRouteId;
}
