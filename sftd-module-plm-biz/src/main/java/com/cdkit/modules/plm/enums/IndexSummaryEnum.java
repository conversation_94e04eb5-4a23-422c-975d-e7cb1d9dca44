package com.cdkit.modules.plm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/5/11
 */
@AllArgsConstructor
@Getter
public enum IndexSummaryEnum {
    /**
     * 首页概览查询
     */
    PRODUCT_TYPE(1, "分类"),
    PRODUCT_KIND(2, "产品"),
    PRODUCT_FINAL_ASSEMBLE(3, "总装"),
    PRODUCT_PART(4, "零件"),
    PROCESS_CARD(5, "工艺卡片"),
    PRODUCT_DELIVERY(6, "出库");

    private final Integer code;
    private final String desc;
}
