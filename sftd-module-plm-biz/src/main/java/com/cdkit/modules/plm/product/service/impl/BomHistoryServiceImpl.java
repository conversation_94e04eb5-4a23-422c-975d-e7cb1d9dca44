package com.cdkit.modules.plm.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.plm.common.CommonUtil;
import com.cdkit.modules.plm.enums.BomCompareDiffEnum;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.enums.OperationTypeEnum;
import com.cdkit.modules.plm.enums.ProductTreeTypeEnum;
import com.cdkit.modules.plm.operation.service.IOperationLogService;
import com.cdkit.modules.plm.process.vo.resp.RespBomCompareVO;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.BomHistory;
import com.cdkit.modules.plm.product.entity.ProductPart;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.mapper.BomHistoryMapper;
import com.cdkit.modules.plm.product.service.IBomHistoryService;
import com.cdkit.modules.plm.product.service.IProductPartService;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.FrozenBomVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: bom_history
 * @Author: mc
 * @Date:   2024-03-28
 * @Version: V1.0
 */
@Service
@Slf4j
public class BomHistoryServiceImpl extends ServiceImpl<BomHistoryMapper, BomHistory> implements IBomHistoryService {
    @Resource @Lazy
    private IProductTreeService productTreeService;
    @Resource
    private IProductPartService productPartService;
    @Resource
    private IOperationLogService operationLogUtil;

    private static final ObjectMapper objectMapper = new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_EMPTY)
            .setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    /**
     * 固化bom
     *
     * @param frozenBomVO 固化信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void frozenBomVO(FrozenBomVO frozenBomVO,String materialCodeOrName) throws JsonProcessingException {
        ProductTree productTree = productTreeService.getById(frozenBomVO.getId());
        if (!ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode().equals(productTree.getType()) &&
                !ProductTreeTypeEnum.PRODUCT_PART.getCode().equals(productTree.getType())) {
            throw new CdkitCloudException("只有总装零部件可以进行固化");
        }
        List<BomHistory> bomHistories = this.list(new LambdaQueryWrapper<BomHistory>().eq(BomHistory::getProductTreeId,
                frozenBomVO.getId()).eq(BomHistory::getNodeType, frozenBomVO.getNodeType()));
        int maxVersion = bomHistories.stream()
                .mapToInt(BomHistory::getVersion)
                .max()
                .orElse(0);
        BomHistory bomHistory = new BomHistory();
        bomHistory.setProductTreeId(frozenBomVO.getId());
        bomHistory.setName(frozenBomVO.getName());
        bomHistory.setDescription(frozenBomVO.getDescription());
        bomHistory.setVersion(maxVersion + 1);
        bomHistory.setNodeType(frozenBomVO.getNodeType());
        //    获取当前节点的bom
        List<String> rootNodesIds = new ArrayList<>();
        rootNodesIds.add(frozenBomVO.getId());
        List<ProductTreeDTO> tree = productTreeService.tree(rootNodesIds, NodeTypeEnum.PRODUCT_TREE.getCode().equals(frozenBomVO.getNodeType()) ? NodeTypeEnum.PRODUCT_TREE : NodeTypeEnum.PROCESS_TREE,materialCodeOrName);
        bomHistory.setHistoryBomJson(objectMapper.writeValueAsString(tree));
        this.save(bomHistory);
        //    更新总装零部件版本号
        ProductPart productPart = productPartService.getById(frozenBomVO.getId());
        productPart.setVersion(maxVersion + 1);
        productPartService.updateById(productPart);

        operationLogUtil.insertOperationLog(OperationTypeEnum.BOM_FROZEN.getType(), frozenBomVO.getName());
    }

    /**
     * bom比较
     *
     * @param ids 节点ID
     * @param nodeTypeEnum 节点类型
     * @return 返回结果
     */
    @Override
    public HashMap<String, Object> compareBom(String ids, NodeTypeEnum nodeTypeEnum,String materialCodeOrName) {
        log.info("bom比较所选节点ID:{}", ids);
        List<String> idList = Arrays.asList(ids.split(","));
        if (idList.size() == 0 || idList.size() > 2) {
            throw new CdkitCloudException("请选中一个或两个历史版本");
        }
        HashMap<String, Object> map = new HashMap<>(16);
        // 第一种情况：选中一个BOM版本
        if (idList.size() == 1) {
            // 查询选中的历史BOM
            BomHistory bomHistory = this.getById(ids);
            // 查询当前树上的BOM信息
            List<ProductTreeDTO> tree = productTreeService.tree(Collections.singletonList(bomHistory.getProductTreeId()), nodeTypeEnum,materialCodeOrName);
            ProductTreeDTO currentTree = tree.get(0);
            List<RespBomCompareVO> currentBomList = new ArrayList<>();
            // 节点平铺
            treeToListCompare(currentTree, currentBomList);

            ProductTreeDTO historyBomTree = JSON.parseObject((JSON.parseArray(bomHistory.getHistoryBomJson()).getJSONObject(0)).toString(), ProductTreeDTO.class);
            setVersion(historyBomTree, bomHistory.getVersion());
            List<RespBomCompareVO> historyBomList = new ArrayList<>();
            // 节点平铺
            treeToListCompare(historyBomTree, historyBomList);

            // 属性不同或者新增对象
            List<String> difference = currentBomList.stream()
                    .filter(element -> !historyBomList.contains(element)).map(RespBomCompareVO::getCode)
                    .collect(Collectors.toList());
            setDiff(currentTree, BomCompareDiffEnum.RED.getCode().toString(), difference);
            setDiff(historyBomTree, BomCompareDiffEnum.RED.getCode().toString(), difference);

            // 判断是否是新增对象
            List<String> currentCodeList = currentBomList.stream().map(RespBomCompareVO::getCode).collect(Collectors.toList());
            List<String> historyCodeList = historyBomList.stream().map(RespBomCompareVO::getCode).collect(Collectors.toList());
            List<String> add = currentCodeList.stream().filter(element -> !historyCodeList.contains(element)).collect(Collectors.toList());
            setDiff(currentTree, BomCompareDiffEnum.BLUE.getCode().toString(), add);

            // 判断是否有删除对象
            List<String> delete = historyCodeList.stream().filter(element -> !currentCodeList.contains(element)).collect(Collectors.toList());
            setDiff(historyBomTree, BomCompareDiffEnum.GREEN.getCode().toString(), delete);


            map.put("currentTree", currentTree);
            map.put("compareTree", historyBomTree);
        } else {
            // 第一种情况：选择两个BOM版本比较
            // 查询选中的第一个历史BOM
            BomHistory bomHistoryFirst = this.getById(idList.get(0));
            ProductTreeDTO bomHistoryFirstTree = JSON.parseObject((JSON.parseArray(bomHistoryFirst.getHistoryBomJson()).getJSONObject(0)).toString(), ProductTreeDTO.class);
            setVersion(bomHistoryFirstTree, bomHistoryFirst.getVersion());
            List<RespBomCompareVO> bomHistoryFirstList = new ArrayList<>();
            // 节点平铺
            treeToListCompare(bomHistoryFirstTree, bomHistoryFirstList);


            // 查询选中的第二个历史BOM
            BomHistory bomHistorySecond = this.getById(idList.get(1));
            ProductTreeDTO bomHistorySecondTree = JSON.parseObject((JSON.parseArray(bomHistorySecond.getHistoryBomJson()).getJSONObject(0)).toString(), ProductTreeDTO.class);
            setVersion(bomHistorySecondTree, bomHistorySecond.getVersion());
            List<RespBomCompareVO> bomHistorySecondList = new ArrayList<>();
            // 节点平铺
            treeToListCompare(bomHistorySecondTree, bomHistorySecondList);

            // 属性不同或者新增对象
            List<String> difference = bomHistoryFirstList.stream()
                    .filter(element -> !bomHistorySecondList.contains(element)).map(RespBomCompareVO::getCode)
                    .collect(Collectors.toList());
            setDiff(bomHistoryFirstTree, BomCompareDiffEnum.RED.getCode().toString(), difference);
            setDiff(bomHistorySecondTree, BomCompareDiffEnum.RED.getCode().toString(), difference);

            // 判断是否是新增对象
            List<String> firstCodeList = bomHistoryFirstList.stream().map(RespBomCompareVO::getCode).collect(Collectors.toList());
            List<String> secondCodeList = bomHistorySecondList.stream().map(RespBomCompareVO::getCode).collect(Collectors.toList());
            List<String> add = firstCodeList.stream().filter(element -> !secondCodeList.contains(element)).collect(Collectors.toList());
            setDiff(bomHistoryFirstTree, BomCompareDiffEnum.BLUE.getCode().toString(), add);

            // 判断是否有删除对象
            List<String> delete = secondCodeList.stream().filter(element -> !firstCodeList.contains(element)).collect(Collectors.toList());
            setDiff(bomHistorySecondTree, BomCompareDiffEnum.GREEN.getCode().toString(), delete);
            map.put("currentTree", bomHistoryFirstTree);
            map.put("compareTree", bomHistorySecondTree);
        }
        return map;
    }

    /**
     * 产品树结构转为列表结构
     * @param productTreeDTO 树结构
     * @param respBomCompareVO 列表结构
     */
    private void treeToListCompare(ProductTreeDTO productTreeDTO,List<RespBomCompareVO> respBomCompareVO) {
        RespBomCompareVO vo = new RespBomCompareVO();
        BeanUtil.copyProperties(productTreeDTO, vo);
        if (!StringUtils.isEmpty(productTreeDTO.getVersion())) {
            vo.setVersion(CommonUtil.numberToLetter(Integer.parseInt(productTreeDTO.getVersion())));
        }
        respBomCompareVO.add(vo);
        if (productTreeDTO.getChildren() != null && productTreeDTO.getChildren().size() > 0) {
            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
                treeToListCompare(item, respBomCompareVO);
            }
        }
    }
    /**
     * 零件详细属性对比
     * @param productTreeDTO 树结构
     * @param diff 对比标识
     * @param difference 零件属性不一致
     */
    private void setDiff(ProductTreeDTO productTreeDTO, String diff, List<String> difference) {

        if (difference.contains(productTreeDTO.getCode())) {
            productTreeDTO.setDiff(diff);
        }
        if (productTreeDTO.getChildren() != null && productTreeDTO.getChildren().size() > 0) {
            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
                setDiff(item, diff, difference);
            }
        }
    }
    /**
     * 对历史bom设置版本
     * @param productTreeDTO 历史bom
     * @param version 版本
     */
    private void setVersion(ProductTreeDTO productTreeDTO, Integer version) {
        if (version != null) {
            productTreeDTO.setVersion(String.valueOf(version));
        }
//        if (productTreeDTO.getChildren() != null && productTreeDTO.getChildren().size() > 0) {
//            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
//                setVersion(item, version);
//            }
//        }
    }
}
