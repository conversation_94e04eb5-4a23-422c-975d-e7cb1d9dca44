package com.cdkit.modules.plm.process.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;

import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 工艺卡片
 * @Author: mc
 * @Date:   2024-04-01
 * @Version: V1.0
 */
@Data
@TableName("process_card")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="process_card对象", name="process_card")
public class ProcessCard implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
	/**卡片名称*/
    @Excel(name = "卡片名称D", width = 15)
    @Schema(description = "卡片名称")
    private String cardName;
	/**关联process_part零件ID*/
	@Excel(name = "关联process_part零件ID", width = 15)
    @Schema(description = "关联process_part零件ID")
    private String partId;
	/**关联process_template模板ID*/
	@Excel(name = "关联process_template模板ID", width = 15)
    @Schema(description = "关联process_template模板ID")
    private String templateId;
	/**模板编号*/
	@Excel(name = "模板编号", width = 15)
    @Schema(description = "模板编号")
    private String templateCode;
	/**卡片数据（json）*/
	@Excel(name = "卡片数据（json）", width = 15)
    @Schema(description = "卡片数据（json）")
    private String cardContent;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private String tenantId;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
}
