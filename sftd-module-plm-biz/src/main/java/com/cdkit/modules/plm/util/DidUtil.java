package com.cdkit.modules.plm.util;

import cn.hutool.extra.spring.SpringUtil;
import com.cdkit.modules.plm.didgen.service.DidRuleService;

import java.util.Map;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：DidUtl
 * @Date：2024/6/3 17:21
 */
public class DidUtil {
    static DidRuleService ruleService = SpringUtil.getBean(DidRuleService.class);

    public static String getDid(String code, Map<String, Object> variables) {
        return ruleService.getDidCode(code, variables);
    }


}
