package com.cdkit.modules.plm.product.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.md.entity.MdMaterial;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.IProductPartService;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.AddBorrowPartVO;
import com.cdkit.modules.plm.product.vo.req.AddProductPartVO;
import com.cdkit.modules.plm.product.vo.req.EditProductPartVO;
import com.cdkit.modules.plm.product.vo.req.ToOriginVO;
import com.cdkit.modules.plm.product.vo.resp.PartBorrowInfoVO;
import com.cdkit.modules.plm.product.vo.resp.ProductAttributesVO;
import com.cdkit.modules.plm.product.vo.resp.ProductPartVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: product_tree
 * @Author: zhao yang
 * @Date: 2024-03-26
 * @Version: V1.0
 */
@Tag(name = "设计bom-产品总装零部件")
@RestController
@RequestMapping("productPart")
@Slf4j
@AllArgsConstructor
public class ProductPartController extends CdkitController<ProductTree, IProductTreeService> {

    private final IProductTreeService productTreeService;

    @Resource
    private IProductPartService productPartService;


    /**
     * 添加
     *
     * @param addProductPartVO
     * @return
     */
    @Operation(summary = "添加产品总装/零部件", description = "添加产品总装/零部件")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody @Validated AddProductPartVO addProductPartVO) {
        productTreeService.addProductPart(addProductPartVO);
        return Result.OK("操作成功！");
    }

    /**
     * 编辑
     *
     * @param editProductPartVO
     * @return
     */
    @Operation(summary = "编辑产品总装/零部件", description = "编辑产品总装/零部件")
    @PostMapping(value = "/edit")
    public Result<String> edit(@RequestBody @Validated EditProductPartVO editProductPartVO) {
        editProductPartVO.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        productTreeService.editProductPart(editProductPartVO);
        return Result.OK("操作成功！");
    }


    /**
     * 通过id删除产品总装/零部件
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id删除产品总装/零部件", description = "通过id删除产品总装/零部件")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id, @RequestParam(name = "cascade", required = false) boolean cascade) {
        productTreeService.cascadeDeleteNode(id, cascade);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询产品总装/零部件
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id查询产品总装/零部件", description = "通过id查询产品总装/零部件")
    @GetMapping(value = "queryById")
    public Result<ProductPartVO> queryById(@RequestParam(name = "id") String id) {
        ProductPartVO productPartVO = productTreeService.queryProductPartById(id);
        if (productPartVO == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(productPartVO);
    }

    /**
     * 通过物料编码查询物料详情
     *
     * @param materialCode 物料编码
     * @return 结果返回
     */
    @Operation(summary = "通过物料编码查询物料详情", description = "通过物料编码查询物料详情")
    @GetMapping(value = "queryAttributeByMaterialCode")
    public Result<ProductAttributesVO> queryAttributeByMaterialCode(@RequestParam(name = "materialCode") String materialCode) {
        ProductAttributesVO productAttributesVO = productTreeService.queryAttributeByMaterialCode(materialCode);
        if (productAttributesVO == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(productAttributesVO);
    }

    /**
     * 通过id查询产品属性（来源主数据）
     *
     * @param id 主键ID
     * @return 结果返回
     */
    @Operation(summary = "通过id查询产品属性（来源主数据）", description = "通过id查询产品属性（来源主数据）")
    @GetMapping(value = "queryAttributeById")
    public Result<ProductAttributesVO> queryAttributeById(@RequestParam(name = "id") String id) {
        ProductAttributesVO productAttributesVO = productTreeService.queryAttributeById(id);
        if (productAttributesVO == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(productAttributesVO);
    }

    /**
     * 通过id查询产品可替代物
     *
     * @param id 主键ID
     * @return 结果返回
     */
    @Operation(summary = "通过id查询产品可替代物", description = "通过id查询产品可替代物")
    @GetMapping(value = "queryFungibleMaterialCodes")
    public Result<List<MdMaterial>> queryFungibleMaterialCodes(@RequestParam(name = "id") String id) {
        return Result.OK(productPartService.queryFungibleMaterialCodes(id));
    }

    /**
     * 通过id编辑产品可替代物
     *
     * @param id 主键ID
     * @return 结果返回
     */
    @Operation(summary = "通过id编辑产品可替代物", description = "通过id编辑产品可替代物")
    @GetMapping(value = "editFungibleMaterialCodes")
    public Result editFungibleMaterialCodes(@RequestParam(name = "id") String id, @RequestParam(name = "materialCodes") String materialCodes) {
        productPartService.editFungibleMaterialCodes(id, materialCodes);
        return Result.OK();
    }

    @PostMapping("addBorrowPart")
    @Operation(summary = "新增借用零部件", description = "新增借用零部件")
    public Result<String> addBorrowPart(@RequestBody @Validated AddBorrowPartVO addBorrowPartVO) {
        productTreeService.addBorrowPart(addBorrowPartVO);
        return Result.OK("操作成功");
    }

    /**
     * 置为原件
     */
    @PostMapping("toOrigin")
    @Operation(summary = "置为原件", description = "置为原件（总装零部件可以复制，复制后的变成借用件，名称和编码在原有属性上+时间戳进行区分，其他属性不变，borrowId对应原件的值）")
    public Result<String> toOrigin(@RequestBody ToOriginVO toOriginVO) {
        productTreeService.toOrigin(toOriginVO.getTargetId());
        return Result.OK("操作成功");
    }

    /**
     * 获取总装/零部件的借用信息
     *
     * @param productPartId
     * @return
     */
    @GetMapping("getBorrowInfoById")
    @Operation(summary = "获取总装/零部件的借用信息", description = "获取总装/零部件的借用信息")
    public Result<Page<PartBorrowInfoVO>> getBorrowInfoById(
            @RequestParam String productPartId,
            @RequestParam(name = "queryCondition", required = false) String queryCondition,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize

    ) {
        Page<PartBorrowInfoVO> ret = productTreeService.getBorrowInfoById(productPartId, queryCondition, pageNo, pageSize);
        return Result.OK(ret);
    }

}
