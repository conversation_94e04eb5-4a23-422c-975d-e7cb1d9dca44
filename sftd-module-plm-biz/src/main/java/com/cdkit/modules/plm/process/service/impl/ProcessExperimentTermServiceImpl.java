package com.cdkit.modules.plm.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import com.cdkit.modules.plm.process.entity.ProcessExperimentTerm;
import com.cdkit.modules.plm.process.mapper.ProcessExperimentTermMapper;
import com.cdkit.modules.plm.process.service.IProcessExperimentTermService;
import com.cdkit.modules.plm.process.vo.req.ReqProcessExperimentTermVO;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 工艺实验项
 * @Author: cdkit-boot
 * @Date: 2024-09-06
 * @Version: V1.0
 */
@Service
@Slf4j
public class ProcessExperimentTermServiceImpl extends ServiceImpl<ProcessExperimentTermMapper, ProcessExperimentTerm> implements IProcessExperimentTermService {
    @Resource
    private ProcessExperimentTermMapper processExperimentTermMapper;

    /**
     * 批量保存实验项
     *
     * @param reqProcessExperimentTermVO 实验项列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(ReqProcessExperimentTermVO reqProcessExperimentTermVO) {
        log.info("批量保存实验项:");
        String refKey = reqProcessExperimentTermVO.getRefKey();
        // 删除实验项
        processExperimentTermMapper.deleteByRefKey(refKey);
        Collection<ProcessExperimentTerm> itemList = reqProcessExperimentTermVO.getProcessExperimentTermList();
        if (itemList.size() > 0) {
//            for (ProcessExperimentTerm it : itemList) {
//                if (TargetTypeEnum.Range.toString().equals(it.getTargetType())) {
//                    if (it.getMinValue() != null && it.getMaxValue() != null) {
//                        it.setTargetValue("[" + it.getMinValue() + "," + it.getMaxValue() + "]");
//                    } else if (it.getMinValue() != null) {
//                        it.setTargetValue("[" + it.getMinValue() + ",+∞]");
//                    } else if (it.getMaxValue() != null) {
//                        it.setTargetValue("[-∞," + it.getMinValue() + "]");
//                    } else {
//                        it.setTargetValue("[-∞,+∞]");
//                    }
//                } else if (TargetTypeEnum.Fix.toString().equals(it.getTargetType())) {
//                    it.setTargetValue(it.getFixValue() == null ? "" : it.getFixValue().toString());
//                } else {
//                    it.setTargetValue(it.getMethodValue());
//                }
//            }
            this.saveBatch(itemList);
        }
    }

    /**
     * 根据refKey查询检测项
     * @param refKey refKey
     * @return 检测项
     */
    @Override
    public List<ProcessExperimentTerm> listTermByRefKey(String refKey) {
        return processExperimentTermMapper.selectList(new LambdaQueryWrapper<ProcessExperimentTerm>()
                .eq(ProcessExperimentTerm::getRefKey, refKey));
    }
}
