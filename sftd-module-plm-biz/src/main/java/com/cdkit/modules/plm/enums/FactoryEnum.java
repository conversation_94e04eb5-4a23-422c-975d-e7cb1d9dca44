package com.cdkit.modules.plm.enums;

import cn.hutool.core.lang.Assert;

import java.util.Arrays;

/**
 * 工厂枚举
 * <AUTHOR>
 * @date 2024/10/31
 */
public enum FactoryEnum {
    /**
     * 工厂枚举
     */
    CZY(1001, "常州院", FactoryEnum.CZY_BEAN_NAME),

    GJS(1002, "贵金属", FactoryEnum.GJS_BEAN_NAME),
    JXCY(1003, "机械采油", FactoryEnum.JXCY_BEAN_NAME),
    JGZZ(1004, "加工制造", FactoryEnum.JGZZ_BEAN_NAME),
    YHC(1005, "油化场", FactoryEnum.YHC_BEAN_NAME),
    DEFAULT(999, "默认",  FactoryEnum.DEFAULT_BEAN_NAME);

    public Integer type;

    public String name;

    public String beanName;

    /**
     * 自定义的beanName
     */
    public static final String CZY_BEAN_NAME = "czy";
    public static final String GJS_BEAN_NAME = "gjs";
    public static final String JXCY_BEAN_NAME = "jxcy";
    public static final String JGZZ_BEAN_NAME = "jgzz";
    public static final String DEFAULT_BEAN_NAME = "default";
    public static final String YHC_BEAN_NAME = "yhc";

    /** <p>根据类型获取beanName<p>
     * @param type type
     * @return {@link String}
     * @since 2023/10/13
     * <AUTHOR>
     **/
    public static String getBeanName(Integer type) {
        FactoryEnum factoryEnum = Arrays.stream(values())
                .filter(p -> p.getType().equals(type))
                .findAny().orElse(FactoryEnum.DEFAULT);
        Assert.notNull(factoryEnum, "暂不支持的策略模式！");
        return factoryEnum.getBeanName();
    }

    FactoryEnum(Integer type, String name, String beanName) {
        this.type = type;
        this.name = name;
        this.beanName = beanName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBeanName() {
        return beanName;
    }

    public void setBeanName(String beanName) {
        this.beanName = beanName;
    }
}
