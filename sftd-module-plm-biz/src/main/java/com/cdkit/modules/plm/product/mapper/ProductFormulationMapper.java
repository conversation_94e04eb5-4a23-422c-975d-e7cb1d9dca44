package com.cdkit.modules.plm.product.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.plm.product.entity.ProductFormulation;
import com.cdkit.modules.plm.product.vo.resp.FormulationReplaceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: product_formulation
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
public interface ProductFormulationMapper extends BaseMapper<ProductFormulation> {

    IPage<ProductFormulation> queryFormulationList(Page<ProductFormulation> page, @Param("productFormulation") ProductFormulation productFormulation);

    IPage<FormulationReplaceVo> queryByMaterialName(Page<FormulationReplaceVo> page,@Param("materialName") String materialName);
}
