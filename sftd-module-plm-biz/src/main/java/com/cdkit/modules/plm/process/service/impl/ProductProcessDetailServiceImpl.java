package com.cdkit.modules.plm.process.service.impl;

import com.cdkit.modules.plm.process.entity.ProductProcessDetail;
import com.cdkit.modules.plm.process.mapper.ProductProcessDetailMapper;
import com.cdkit.modules.plm.process.service.IProductProcessDetailService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 产品工序步骤
 * @Author: cdkit-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Service
public class ProductProcessDetailServiceImpl extends ServiceImpl<ProductProcessDetailMapper, ProductProcessDetail> implements IProductProcessDetailService {

}
