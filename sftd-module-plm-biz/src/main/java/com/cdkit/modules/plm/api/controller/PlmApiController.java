package com.cdkit.modules.plm.api.controller;

import io.swagger.v3.oas.annotations.Hidden;
import com.cdkit.modules.plm.process.entity.MdProcess;
import com.cdkit.modules.plm.process.entity.MdProcessDetail;
import com.cdkit.modules.plm.process.entity.MdProcessRouteDetail;
import com.cdkit.modules.plm.process.entity.ProcessExperimentTerm;
import com.cdkit.modules.plm.process.service.impl.PlmApiServiceImpl;
import com.cdkit.modules.plm.process.vo.resp.RespProcessVO;
import com.cdkit.modules.plm.process.vo.resp.ScheduleProcessRouteResp;
import com.cdkit.modules.plm.product.entity.ProductPart;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.vo.resp.ProductFileVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * plm模块对外接口请求类
 * <AUTHOR>
 * @date 2024/8/6
 */
@Hidden
@Slf4j
@RestController
@RequestMapping("/plm/api")
public class PlmApiController {
    @Resource
    private PlmApiServiceImpl plmApiService;

    @Hidden
    @GetMapping(value = "/getProductFile")
    public ProductFileVO getProductFile(@RequestParam(name="code") String code) {
        return plmApiService.getProductFile(code);
    }

    @Hidden
    @GetMapping(value = "/getProductFileById")
    public ProductFileVO getProductFileById(@RequestParam(name="id") String id) {
        return plmApiService.getProductFileById(id);
    }

    @Hidden
    @GetMapping(value = "/getProductFileByCode")
    public ProductFileVO getProductFileByCode(@RequestParam(name="code") String code) {
        return plmApiService.getProductFileByCode(code);
    }

    @Hidden
    @GetMapping(value = "/getProductFileList")
    public List<ProductTree> getProductFileList() {
        return plmApiService.getProductFileList();
    }

    @Hidden
    @GetMapping(value = "/mdProcess/list")
    public List<MdProcess> queryMdProcessList(@RequestParam(name="processIds") String processIds) {
        return plmApiService.queryMdProcessList(processIds);
    }

    @Hidden
    @GetMapping(value = "/mdProcessDetail/list")
    public List<MdProcessDetail> queryMdProcessDetailList(@RequestParam(name="processIds") String processIds) {
        List<MdProcessDetail> mdProcessDetails = plmApiService.queryMdProcessDetailList(processIds);
        return plmApiService.queryMdProcessDetailList(processIds);
    }
    @Hidden
    @GetMapping(value = "/getProcessDetail")
    public RespProcessVO getProcessDetail(@RequestParam(name = "productId") String productId) {
        return plmApiService.getProcessDetail(productId);
    }

    @Hidden
    @GetMapping(value = "/getProcess")
    public MdProcess getProcess(@RequestParam(name = "processSerialNum") String processSerialNum) {
        return plmApiService.getProcess(processSerialNum);
    }

    @Hidden
    @PostMapping(value = "/getProcessRouteDetail")
    public MdProcessRouteDetail getProcess(@RequestBody MdProcessRouteDetail processRouteDetail) {
        return plmApiService.getProcessRouteDetail(processRouteDetail);
    }

    @Hidden
    @GetMapping(value = "/experimentTerm/list")
    public Map<String, List<ProcessExperimentTerm>> queryExperimentTermList(@RequestParam(name="refKeys") String refKeys) {
        return plmApiService.queryExperimentTermList(refKeys);
    }

    @Hidden
    @GetMapping(value = "/getWorkHour")
    public BigDecimal getWorkHour(@RequestParam(name = "processRouteDetailId") String processRouteDetailId) {
        return plmApiService.getWorkHour(processRouteDetailId);
    }

    @Hidden
    @GetMapping(value = "/queryProcess")
    public MdProcess queryProcess(@RequestParam(name = "id") String id) {
        return plmApiService.queryProcess(id);
    }

    /**
     * 通过产线编码查询物料编码
     * 智能排程调用
     * @param productionLineCode 产线编码
     * @return 返回结果
     */
    @Hidden
    @GetMapping(value = "/schedule/materialCode/list")
    public List<ScheduleProcessRouteResp> getMaterialCodeByproductLineCode(@RequestParam(name = "productionLineCode") String productionLineCode) {
        return plmApiService.materialCodeList(productionLineCode);
    }

    /**
     * 根据物料编码查询件次号继承的物料编码
     *
     * @param id 物料编码
     */
    @Hidden
    @GetMapping(value = "/getExtendMaterialCode")
    public String getExtendMaterialCode(@RequestParam(name = "id") String id) {
        return plmApiService.getExtendMaterialCode(id);
    }



    /**
     * 根据物料编码,boomcode 查询无聊可替代物
     *
     * @param boomDataId boom编码 materialCode 物料编码
     */
    @Hidden
    @GetMapping(value = "/queryFungibleByCode")
    public String queryFungibleByCode(@RequestParam(name = "boomDataId") String boomDataId, @RequestParam(name = "materialCode") String materialCode) {
        return plmApiService.queryFungibleByCode(boomDataId,materialCode);
    }


    /**
     * 根据物料编码,boompartid,查询part信息
     */
    @Hidden
    @GetMapping(value = "/queryPart")
    public ProductPart queryPart(@RequestParam(name = "boomDataId") String boomDataId, @RequestParam(name = "materialCode") String materialCode) {
        return plmApiService.queryPart(boomDataId,materialCode);
    }

}
