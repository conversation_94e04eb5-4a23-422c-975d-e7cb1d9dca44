package com.cdkit.modules.plm.process.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 知识库
 */
@Schema(description = "知识库")
@Data
@TableName(value = "base_knowledge")
public class BaseKnowledge implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    @Size(max = 36, message = "主键ID最大长度要小于 36")
    private String id;

    /**
     * 父级ID
     */
    @TableField(value = "pid")
    @Schema(description = "父级ID")
    @Size(max = 36, message = "父级ID最大长度要小于 36")
    private String pid;

    /**
     * 知识名称
     */
    @TableField(value = "`name`")
    @Schema(description = "知识名称")
    @Size(max = 255, message = "知识名称最大长度要小于 255")
    private String name;

    /**
     * 知识编码
     */
    @TableField(value = "code")
    @Schema(description = "知识编码")
    @Size(max = 255, message = "知识编码最大长度要小于 255")
    private String code;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description = "描述")
    @Size(max = 800, message = "描述最大长度要小于 800")
    private String description;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    @Schema(description = "租户号")
    @Size(max = 32, message = "租户号最大长度要小于 32")
    private String tenantId;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @Schema(description = "创建人")
    @Size(max = 32, message = "创建人最大长度要小于 32")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    @Schema(description = "更新人")
    @Size(max = 32, message = "更新人最大长度要小于 32")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
