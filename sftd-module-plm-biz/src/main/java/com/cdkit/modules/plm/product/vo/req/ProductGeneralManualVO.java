package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author：mr
 * @name：TechnicalRequirementsVO
 * @Date：2024/4/16 18:00
 */
@Data
@Schema(description ="产品通用说明书")
public class ProductGeneralManualVO {
    //配方id
    private String id;
    @Schema(description ="基本性质")
    private String basicProperties;
    @Schema(description ="产品概述")
    private String productOverview;
    //产品主要技术特点
    private String productSpecification;
    //适用范围
    private String scopeApplication;
    //安全注意事项（安全特性）
    private String safetyPrecautions;
    //安全注意事项（包装）
    private String safetyPacket;
    //应用油田案例
    private String oilfieldCases;

    //是否更新 1-新增 2-更新
    private String updateFlag;
}
