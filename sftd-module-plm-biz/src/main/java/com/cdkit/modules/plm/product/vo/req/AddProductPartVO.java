package com.cdkit.modules.plm.product.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import com.cdkit.md.entity.MdMaterialExtend;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：AddProductPartVO
 * @Date：2024/3/27 18:01
 */
@Data
@Schema(description = "新增产品零部件vo")
public class AddProductPartVO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "编码/代号")
    private String code;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "父id")
    private String pid;

    @Schema(description = "物料编码")
    private String materialCode;

    @Schema(description = "材料")
    private String materialName;

    @Schema(description = "生产类型")
    private String manufactureType;

    @Schema(description = "零件类型")
    private String partType;

    @Schema(description = "处理类型")
    private String handleType;

    @Schema(description = "结构类型")
    private String structType;

    @Schema(description = "重量")
    private String weight;

    @Schema(description = "规格")
    private String specs;

    @Schema(description = "序号")
    private String sort;

    @Schema(description = "装配数量")
    private BigDecimal assembleQuantity;

    @Schema(description = "装配序号")
    private Integer assembleSort;

    @Schema(description = "装配单位")
    private String assembleUnit;

    @Schema(description = "成品总损耗比例")
    private BigDecimal lossRatio;

    @Schema(description = "件次号继承来源产品id")
    private String extendProductId;

    @Schema(description = "件次号继承来源产品name")
    private String extendProductName;

    /**
     * 扩展属性
     */
    private List<MdMaterialExtend> productPartExtendList;

    /**
     * 定制属性
     */
    private List<MdMaterialExtend> productPartCustomizedList;

    @Schema(description = "是否主材")
    private Integer mainMaterialsFlag;

    @Schema(description = "执行标准")
    private String executionStandards;

    @Schema(description = "制版条件")
    private String plateMakingConditions;

}
