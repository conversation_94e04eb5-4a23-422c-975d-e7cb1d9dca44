package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import com.cdkit.md.api.IMdProductionLineApi;
import com.cdkit.modules.plm.process.entity.MdProcess;
import com.cdkit.modules.plm.process.entity.MdProcessDetail;
import com.cdkit.modules.plm.process.entity.MdProcessRouteDetail;
import com.cdkit.modules.plm.process.entity.ProcessExperimentTerm;
import com.cdkit.modules.plm.process.mapper.*;
import com.cdkit.modules.plm.process.service.IProductProcessRouteService;
import com.cdkit.modules.plm.process.vo.resp.RespProcessVO;
import com.cdkit.modules.plm.process.vo.resp.ScheduleProcessRouteResp;
import com.cdkit.modules.plm.product.entity.ProductPart;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.mapper.ProductPartMapper;
import com.cdkit.modules.plm.product.mapper.ProductTreeMapper;
import com.cdkit.modules.plm.product.vo.resp.ProductFileVO;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/6
 */
@Service
public class PlmApiServiceImpl {
    @Lazy
    @Resource
    private IProductProcessRouteService productProcessRouteService;
    @Resource
    private MdProcessMapper mdProcessMapper;
    @Resource
    private MdProcessDetailMapper mdProcessDetailMapper;
    @Resource
    private MdProcessRouteDetailMapper processRouteDetailMapper;
    @Resource
    private ProcessExperimentTermMapper processExperimentTermMapper;
    @Resource
    private IMdProductionLineApi mdProductionLineApi;
    @Resource
    private ProductProcessRouteMapper productProcessRouteMapper;
    @Resource
    private ProductPartMapper productPartMapper;
    @Resource
    private ProductTreeMapper productTreeMapper;

    public ProductFileVO getProductFile(String code) {
        return productProcessRouteService.getProductFile(code);
    }

    public List<MdProcess> queryMdProcessList(String processIds) {
        if (StringUtils.isEmpty(processIds)) {
            return mdProcessMapper.selectList(new LambdaQueryWrapper<>());
        } else {
            List<String> processList = Arrays.asList(processIds.split(","));
            return mdProcessMapper.selectList(new LambdaQueryWrapper<MdProcess>().in(MdProcess::getId, processList));
        }

    }

    public List<MdProcessDetail> queryMdProcessDetailList(String processIds) {
        if (StringUtils.isEmpty(processIds)) {
            return mdProcessDetailMapper.selectList(new LambdaQueryWrapper<>());
        } else {
            List<String> processList = Arrays.asList(processIds.split(","));
            return mdProcessDetailMapper.selectList(new LambdaQueryWrapper<MdProcessDetail>().in(MdProcessDetail::getProcessId, processList).orderByAsc(MdProcessDetail::getNumber));
        }
    }

    public RespProcessVO getProcessDetail(String productId) {
        return productProcessRouteService.getProcessDetail(productId);
    }

    public MdProcess getProcess(String processSerialNum) {
        return mdProcessMapper.selectOne(new LambdaQueryWrapper<MdProcess>().eq(MdProcess::getProcessCode, processSerialNum));
    }

    public MdProcessRouteDetail getProcessRouteDetail(MdProcessRouteDetail processRouteDetail) {
        LambdaQueryWrapper<MdProcessRouteDetail> query = new LambdaQueryWrapper<>();
        query.eq(MdProcessRouteDetail::getProcessRouteId,processRouteDetail.getProcessRouteId());
        query.eq(MdProcessRouteDetail::getProcessNumber,processRouteDetail.getProcessNumber());
        query.eq(MdProcessRouteDetail::getProcessId,processRouteDetail.getProcessId());
        MdProcessRouteDetail mdProcessRouteDetail = processRouteDetailMapper.selectOne(query);
        return mdProcessRouteDetail;
    }

    public Map<String, List<ProcessExperimentTerm>> queryExperimentTermList(String refKeys) {
        List<String> refKeyList = Arrays.asList(refKeys.split(","));
        List<ProcessExperimentTerm> processExperimentTerms = processExperimentTermMapper.selectList(new LambdaQueryWrapper<ProcessExperimentTerm>().in(ProcessExperimentTerm::getRefKey, refKeyList));
        return processExperimentTerms.stream().collect(Collectors.groupingBy(ProcessExperimentTerm::getRefKey));

    }

    public ProductFileVO getProductFileById(String id) {
        return productProcessRouteService.getProductFileById(id);
    }

    public List<ProductTree> getProductFileList() {
        return productProcessRouteService.getProductFileList();
    }

    public BigDecimal getWorkHour(String processRouteDetailId) {
        return productProcessRouteService.getById(processRouteDetailId).getWorkHour();
    }

    public MdProcess queryProcess(String id) {
        return mdProcessMapper.selectById(id);
    }

    /**
     * 通过产线编码查询物料编码
     * @param productionLineCode 产线编码
     * @return
     */
    public List<ScheduleProcessRouteResp> materialCodeList(String productionLineCode) {
        List<ScheduleProcessRouteResp> res = CollectionUtil.newArrayList();
        // 通过产线编码查询工序编码
        List<String> processCodeList = mdProductionLineApi.queryMdProcessCode(productionLineCode);
        if (CollectionUtil.isNotEmpty(processCodeList)) {
            res = productProcessRouteMapper.selectProcessRouteDetails(processCodeList);
            res.forEach(e -> e.setResourceGroupCode(productionLineCode));
        }
        return res;
    }


    /**
     * 根据物料编码查询件次号继承的物料编码
     *
     * @param id
     * @return
     */
    public String getExtendMaterialCode(String id) {
        ProductPart productPart = productPartMapper.selectById(id);
        if (productPart != null) {
            return Optional.ofNullable(productPartMapper.selectById(productPart.getExtendProductId())).map(ProductPart::getMaterialCode).orElse(null);
        }
        return null;
    }

    public String queryFungibleByCode(String boomDataId, String materialCode) {

        List<ProductTree> productTreeList = productTreeMapper.selectList(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getMaterialCode, materialCode)
                .eq(ProductTree::getPid, boomDataId)
                .eq(ProductTree::getNodeType, 2));
        if(!productTreeList.isEmpty()){
            ProductPart productPart = productPartMapper.selectById(productTreeList.get(0).getId());
            if(null!=productPart) {
                return productPart.getFungibleMaterialCodes();
            }
        }
        return null;
    }

    public ProductPart queryPart(String boomDataId, String materialCode) {
        ProductPart productPart = new ProductPart();
        List<ProductPart> list = productPartMapper.selectList(new LambdaQueryWrapper<ProductPart>().eq(ProductPart::getId, boomDataId)
                .eq(ProductPart::getMaterialCode, materialCode));
        if(!list.isEmpty()){
            productPart=list.get(0);
            return productPart;
        }
        return productPart;
    }

    public ProductFileVO getProductFileByCode(String code) {
        return productProcessRouteService.getProductFileByCode(code);
    }



    public Map<String,ProductPart> queryPartMap(String boomDataIds) {
        Map<String, ProductPart> returnMap = new HashMap<>();
        List<String> boomDataIdList = Arrays.asList(boomDataIds.split(","));
        //return mdProcessMapper.selectList(new LambdaQueryWrapper<MdProcess>().in(MdProcess::getId, processList));
        ProductPart productPart = new ProductPart();
        List<ProductPart> list = productPartMapper.selectList(new LambdaQueryWrapper<ProductPart>().in(ProductPart::getId, boomDataIdList));
        if(!list.isEmpty()){
            for(ProductPart pp:list){
                returnMap.put(pp.getId(),pp);
            }
        }
        return returnMap;
    }
}
