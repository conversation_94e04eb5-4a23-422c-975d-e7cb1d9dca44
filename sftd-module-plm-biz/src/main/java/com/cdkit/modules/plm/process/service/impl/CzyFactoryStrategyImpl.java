package com.cdkit.modules.plm.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.common.config.TenantContext;
import com.cdkit.md.api.IMdMaterialTypeApi;
import com.cdkit.md.entity.MdMaterialType;
import com.cdkit.modules.plm.common.Constants;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.product.entity.ProductFormulation;
import com.cdkit.modules.plm.product.entity.ProductFormulationMaster;
import com.cdkit.modules.plm.product.enums.EnableStatusEnum;
import com.cdkit.modules.plm.product.enums.FormulaStatusEnum;
import com.cdkit.modules.plm.product.mapper.ProductFormulationMapper;
import com.cdkit.modules.plm.product.mapper.ProductFormulationMasterMapper;
import com.cdkit.modules.plm.product.mapper.ProductTreeMapper;
import com.cdkit.modules.plm.product.service.IProductPartService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import com.cdkit.modules.plm.enums.FactoryEnum;
import com.cdkit.modules.plm.enums.ProductTreeTypeEnum;
import com.cdkit.modules.plm.process.service.IFactoryStrategy;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/31
 */
@Component(FactoryEnum.CZY_BEAN_NAME)
@Slf4j
public class CzyFactoryStrategyImpl implements IFactoryStrategy {

    @Resource
    @Lazy
    private IProductTreeService productTreeService;

    @Resource
    private IMdMaterialTypeApi mdMaterialTypeApi;
    @Resource
    private ProductFormulationMapper productFormulationMapper;
    @Resource
    private ProductTreeMapper productTreeMapper;
    @Resource
    private ProductFormulationMasterMapper productFormulationMasterMapper;
    /**
     * 查询产品档案列表
     *
     * @return 返回结果
     */
    @Override
    public List<ProductTree> getProductFileList(String materialCodeOrName) {
        return productTreeService.list(new LambdaQueryWrapper<ProductTree>()
                .eq(ProductTree::getConverted, 1)
                .eq(ProductTree::getType, ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode())
                .eq(ProductTree::getEnable,0));
    }

    @Override
    public void createTree(ProductFormulation productFormulation) {
        String Fname = FactoryEnum.YHC.getName();
        //1-1创建产品分类
        ProductTree productTree = new ProductTree();
        productTree.setName(Fname);
        productTree.setType(1);
        productTree.setPid("0");
        productTree.setCode(FactoryEnum.CZY_BEAN_NAME);
        productTree.setSort(String.valueOf(1));
        productTree.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        productTree.setConverted(1);
        productTree.setDelFlag(0);
        productTree.setEnable(0);
        productTreeService.save(productTree);
        productTree.setPath("0"+ Constants.PATH_SPILT+ productTree.getId() + Constants.PATH_SPILT);
        productTree.setSort("1");
        productTreeService.updateById(productTree);

        //1-2创建产品种类

        ProductTree pt = new ProductTree();
        MdMaterialType mdMaterialType = mdMaterialTypeApi.queryMaterialType(productFormulation.getMaterialTypeId());
        if(null!=mdMaterialType) {
            pt.setName(mdMaterialType.getTypeName());
            pt.setCode(mdMaterialType.getId());
        }else{
            pt.setName(TenantContext.getTenant()+"分类");
            pt.setCode(TenantContext.getTenant()+"001");
        }
        //pt.setName(mdMaterialType.getTypeName());
        pt.setType(2);
        pt.setPid(productTree.getId());
        //pt.setCode(mdMaterialType.getId());
        pt.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        pt.setConverted(1);
        pt.setDelFlag(0);
        pt.setEnable(0);
        productTreeService.save(pt);
        pt.setPath(productTree.getPath()+ pt.getId()+ Constants.PATH_SPILT);
        pt.setSort(pt.getId());
        productTreeService.updateById(pt);

    }

    @Override
    public BigDecimal getAssembleQuantity() {
        return BigDecimal.valueOf(100);
    }

    @Override
    public void createApproval(String productFormulationId, HttpServletRequest request) {
        List<ProductTree> updateList = new ArrayList<>();
        ProductFormulation pf = productFormulationMapper.selectById(productFormulationId);
        pf.setFormulaStatus(FormulaStatusEnum.AUDITED.getCode());
        pf.setEnableStatus(EnableStatusEnum.PASS.getCode());
        productFormulationMapper.updateById(pf);
        //1-3将主配方下boom置空
        List<ProductFormulation> pfList = productFormulationMapper.selectList(new LambdaQueryWrapper<ProductFormulation>().eq(ProductFormulation::getProductFormulationMasterId, pf.getProductFormulationMasterId()));
        for(ProductFormulation pfl:pfList){
            ProductTree productTree = productTreeMapper.selectById(pfl.getBoomId());
            productTree.setEnable(1);
            updateList.add(productTree);
        }
        productTreeService.updateBatchById(updateList);
        //将当前配方tree状态变为可用
        ProductTree productTree = productTreeMapper.selectById(pf.getBoomId());
        productTree.setEnable(0);
        productTreeMapper.updateById(productTree);
        //更新上一版本配方id
        ProductFormulationMaster productFormulationMaster = productFormulationMasterMapper.selectById(pf.getProductFormulationMasterId());
        productFormulationMaster.setProductFormulationId(pf.getId());
        productFormulationMaster.setBeforeFormulationId(pf.getId());
        productFormulationMasterMapper.updateById(productFormulationMaster);
    }
}
