package com.cdkit.modules.plm.product.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.plm.product.entity.ProductFormulationDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: product_formulation_detail
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
public interface ProductFormulationDetailMapper extends BaseMapper<ProductFormulationDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<ProductFormulationDetail>
   */
	public List<ProductFormulationDetail> selectByMainId(@Param("mainId") String mainId);
}
