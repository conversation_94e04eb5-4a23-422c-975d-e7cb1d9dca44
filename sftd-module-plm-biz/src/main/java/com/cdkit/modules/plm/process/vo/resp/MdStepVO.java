package com.cdkit.modules.plm.process.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkitframework.poi.excel.annotation.ExcelCollection;

import java.util.List;

/**
 * @Description: boom模板
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Data
@Schema(name="boom模板对象", description="boom模板对象")
public class MdStepVO {

	/**工序名称*/
	@Excel(name = "工序名称", width = 15)
	@Schema(description = "工序名称")
    private String stepName;
	/**工序内容*/
	@Excel(name = "工序内容", width = 15)
	@Schema(description = "工序内容")
    private String stepContext;


}
