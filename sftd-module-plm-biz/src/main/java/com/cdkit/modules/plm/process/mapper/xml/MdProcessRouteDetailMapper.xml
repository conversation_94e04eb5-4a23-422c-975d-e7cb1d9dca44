<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.plm.process.mapper.MdProcessRouteDetailMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE
		FROM  md_process_route_detail
		WHERE
			 process_route_id = #{mainId} 	</delete>

	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.cdkit.modules.plm.process.entity.MdProcessRouteDetail">
		SELECT *
		FROM  md_process_route_detail
		WHERE
			 process_route_id = #{mainId} order by process_number asc 	</select>
</mapper>
