package com.cdkit.modules.plm.process.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.entity.MdProcess;
import com.cdkit.modules.plm.process.entity.MdProcessDetail;
import com.cdkit.modules.plm.process.vo.resp.MdBoomPage;
import com.cdkit.modules.plm.process.vo.resp.MdProcessRemarkPage;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 工序定义
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
public interface IMdProcessService extends IService<MdProcess> {

	/**
	 * 添加一对多
	 *
	 * @param mdProcess
	 * @param mdProcessDetailList
	 */
	public void saveMain(MdProcess mdProcess, List<MdProcessDetail> mdProcessDetailList) ;

	/**
	 * 修改一对多
	 *
   * @param mdProcess
   * @param mdProcessDetailList
	 */
	public void updateMain(MdProcess mdProcess, List<MdProcessDetail> mdProcessDetailList);

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain(String id);

	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain(Collection<? extends Serializable> idList);

	/**
	 * 通过code查询
	 *
	 * @param processCode 工序编码
	 * @return 结果返回
	 */
	MdProcess queryByProcessCode(String processCode);

	void autoInsert(String flagText,String workShopId);

    void createData(String boomId, List<MdBoomPage> list);

    void importProcessRemark(List<MdProcessRemarkPage> list);
}
