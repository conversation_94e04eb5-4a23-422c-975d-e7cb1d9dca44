package com.cdkit.modules.plm.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.util.MinioUtil;
import com.cdkit.modules.plm.common.Constants;
import com.cdkit.modules.plm.product.dto.MockMultipartFile;
import com.cdkit.modules.plm.product.dto.WriteSignDTO;
import com.cdkit.modules.plm.product.entity.DesignDocument;
import com.cdkit.modules.plm.product.entity.Signature;
import com.cdkit.modules.plm.product.service.CadHandleService;
import com.cdkit.modules.plm.product.service.IDesignDocumentService;
import com.cdkit.modules.plm.product.service.SignatureService;
import com.cdkit.modules.plm.product.thirdparty.zwcad.CadFeign;
import com.cdkit.modules.plm.product.thirdparty.zwcad.dto.*;
import com.cdkit.modules.plm.util.CustomMinioUtil;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author：zhao yang
 * @name：CadHandleServiceImpl
 * @Date：2024/4/25 16:02
 */
@Service
@AllArgsConstructor
@Slf4j
public class CadHandleServiceImpl implements CadHandleService {

    private final IDesignDocumentService designDocumentService;
    private final CadFeign cadFeign;
    private final SignatureService signatureService;

    public static final String transPdfReqJson = "{\"functionType\": 27, \"docId\": \"%s\", \"handleId\": \"%s\", \"ptType\": 0, \"plotSettings\": {\"plotType\": 1, \"minPoint\": [0, 0, 0], \"maxPoint\": [0, 0, 0], \"centered\": true, \"fullScale\": true, \"denominator\": 1, \"plotRotation\": 1, \"pageIndex\": 63, \"xCoordinate\": 0, \"yCoordinate\": 0}, \"canvasWidth\": \"1076\", \"canvasHeight\": \"784\", \"lineWeights\": true, \"openElecSignSeal\": 1}";
    public static final String formatTransferJson = "{\"docIds\":[%s],\"type\":\"png\",\"toUpload\":false,\"plotSettings\":{\"plotType\":1,\"minPoint\":[0.0,0.0],\"maxPoint\":[0.0,0.0],\"centered\":\"true\",\"fullScale\":\"true\",\"plotRotation\":0,\"printPageId\":\"123456\",\"printStyleId\":\"12345\"}}";
    public static final String MATERIAL_CODE = "设计_物料编码";
    public static final String PART_NAME = "设计_零部件名称";
    public static final String PART_CODE = "设计_零部件编码";


    @SneakyThrows
    @Override
    public Map<String, Object> parseCadProperties(MultipartFile file, List<String> strTags) {
        //提交dwg转json的任务
        CadResultDTO<UploadDwgRespDTO> uploadDwgRespDTOCadRet = cadFeign.submitTransJsonTask(file);
        if (!uploadDwgRespDTOCadRet.isSuccess()) {
            throw new CdkitCloudException("解析dwg文件报错");
        }
        String taskId = uploadDwgRespDTOCadRet.getData().getDocId();
        Thread.sleep(3000);
        // 设置轮询的次数和间隔，可根据实际情况调整
        int pollTimes = 10;
        int intervalMs = 2000;
        byte[] bytes = new byte[0];
        for (int i = 0; i < pollTimes; i++) {
            try {
                // 获取任务结果
                bytes = cadFeign.getProperties(taskId);
                if (bytes.length > 0) {
                    // 任务已完成
                    break;
                }
                // 任务还在处理中，等待一段时间后再次检查
                Thread.sleep(intervalMs);
            } catch (Exception e) {
                log.error("获取任务状态失败，重拾次数：{}", i, e);
            }
        }
        if (bytes.length == 0) {
            throw new CdkitCloudException("解析dwg文件报错");
        }

        DwgJsonRespDTO dwgJsonRespDTO = JSONUtil.toBean(new String(bytes), DwgJsonRespDTO.class);
        return dwgJsonRespDTO.getEntityList().stream()
                .filter(blockEntity -> StrUtil.isNotBlank(blockEntity.getBlockName()) && blockEntity.getBlockName().startsWith("标准图框_"))
                .flatMap(blockEntity -> blockEntity.getAttributes().stream())
                .filter(attribute -> strTags.contains(attribute.getStrTag()))
                .collect(Collectors.toMap(DwgJsonRespDTO.Attribute::getStrTag, DwgJsonRespDTO.Attribute::getStrText));
    }

    @SneakyThrows
    @Override
    public Map<String, Object> parseCadProperties(String id, List<String> strTags) {
        //组装multipartFile
        DesignDocument designDocument = designDocumentService.getById(id);
        InputStream inputStream = MinioUtil.getMinioFile(MinioUtil.getBucketName(), designDocument.getFilePath());
        MockMultipartFile mockMultipartFile = new MockMultipartFile(designDocument.getName(), designDocument.getName(), MediaType.APPLICATION_OCTET_STREAM_VALUE, inputStream);
        return parseCadProperties(mockMultipartFile, strTags);
    }


    @SneakyThrows
    @Override
    public void writeCadSign(String id, List<WriteSignDTO> writeSignDTOList) {
        if (CollUtil.isEmpty(writeSignDTOList) || StrUtil.isEmpty(id)) {
            throw new CdkitCloudException("参数不能为空");
        }
        DesignDocument designDocument = designDocumentService.getById(id);
        if (!Constants.DRAWING.equals(designDocument.getCategory())) {
            log.error("当前只有图纸写入签章,{}", id);
            throw new CdkitCloudException("当前只有图纸写入签章");
        }
        InputStream inputStream = MinioUtil.getMinioFile(MinioUtil.getBucketName(), designDocument.getFilePath());
        MockMultipartFile mockMultipartFile = new MockMultipartFile(designDocument.getName(), designDocument.getName(), MediaType.APPLICATION_OCTET_STREAM_VALUE, inputStream);
        String docId = designDocument.getDocId();
        if (StrUtil.isEmpty(docId)) {
            log.warn("当前图纸的docId为空,{}", id);
            log.info("同步图纸到cad sdk服务，{}", id);
            CadResultDTO<UploadRespDTO> ret = cadFeign.upload("0", mockMultipartFile);
            if (!ret.isSuccess()) {
                log.error("图纸上传到cad sdk服务器报错,id:{}", id);
                throw new CdkitCloudException(String.format("图纸上传到cad sdk服务器报错,id:%s", id));
            }
            docId = ret.getData().getDocId();
        }
        String dwgPath = docId + ".dwg";

        Thread.sleep(3000);
        // 设置轮询的次数和间隔，可根据实际情况调整
        int pollTimes = 5;
        int intervalMs = 2000;
        CadResultDTO<List<BlockDataDTO>> propertiesRet = new CadResultDTO<>();
        for (int i = 0; i < pollTimes; i++) {
            try {
                //获取图纸的签章属性
                propertiesRet = cadFeign.getProperties(dwgPath, docId);
                if (!propertiesRet.isSuccess()) {
                    log.error("调用中望cad获取图纸属性接口报错,id:{}", id);
                    throw new CdkitCloudException(String.format("获取图纸属性报错,id:%s", id));
                }
                if (propertiesRet.getData().size() > 0) {
                    // 任务已完成
                    break;
                }
                // 任务还在处理中，等待一段时间后再次检查
                Thread.sleep(intervalMs);
            } catch (Exception e) {
                log.error("获取图纸属性失败，重试次数：{}", i, e);
            }
        }
        ArrayList<SignSaveReqVO.SignReqVO> signReqVOS = new ArrayList<>();
        List<BlockDataDTO> blocks = propertiesRet.getData();
        for (BlockDataDTO block : blocks) {
            int blockRefHandle = block.getBlockRefHandle();
            List<BlockDataDTO.BlockData> blockDataS = block.getBlockData();
            for (BlockDataDTO.BlockData blockData : blockDataS) {
                List<BlockDataDTO.RefData> refDataS = blockData.getRefData();
                for (BlockDataDTO.RefData refData : refDataS) {
                    String attrName = refData.getAttrName();
                    Optional<WriteSignDTO> first = writeSignDTOList.stream()
                            .filter(x -> StrUtil.equals(attrName, x.getSignKey()))
                            .findFirst();
                    if (first.isPresent()) {
                        WriteSignDTO x = first.get();
                        SignSaveReqVO.SignReqVO signReqVO = new SignSaveReqVO.SignReqVO();
                        if (x.getSignType() == 1 || x.getSignType() == 3) {
                            //    签章图 此时根据username查签名
                            Signature signature = signatureService.getOne(new LambdaQueryWrapper<Signature>().eq(Signature::getUsername, x.getValue()));
                            if (signature == null) {
                                log.error("{}用户没签名，无法进行签章操作", x.getValue());
                                throw new CdkitCloudException(String.format("%s用户没签名，无法进行签章操作", x.getValue()));
                            }
                            String filePath = signature.getFilePath();
                            String bucketName = filePath.substring(0, filePath.indexOf("/"));
                            filePath = filePath.substring(filePath.indexOf("/") + 1);
                            InputStream is = MinioUtil.getMinioFile(bucketName, filePath);
                            MockMultipartFile file;
                            try {
                                file = new MockMultipartFile(signature.getFileName(), signature.getFileName(), MediaType.APPLICATION_OCTET_STREAM_VALUE, is);
                            } catch (IOException e) {
                                log.error(e.getMessage());
                                throw new CdkitCloudException("系统内部异常");
                            }
                            // 上传签章图到zw cad
                            CadResultDTO<UploadSignRespDTO> uploadedSignRet = cadFeign.uploadSign(dwgPath, docId, (int) (System.currentTimeMillis() / 1000), file);
                            if (!uploadedSignRet.isSuccess()) {
                                log.error("调用签章与图纸绑定异常，docId：{},签名图片：{}，异常信息：{}", docId, file.getOriginalFilename(), uploadedSignRet.getMsg());
                                throw new CdkitCloudException("签章与图纸绑定异常");
                            }
                            signReqVO.setSignFileId(uploadedSignRet.getData().getFileId());
                        } else {
                            signReqVO.setAttrValue(x.getValue());
                        }
                        signReqVO.setSignId(refData.getSignId());
                        signReqVO.setType(refData.getType());
                        signReqVO.setAttrHandle(refData.getAttrHandle());
                        signReqVO.setAttrName(attrName);
                        signReqVO.setBlockRefHandle(blockRefHandle);
                        signReqVOS.add(signReqVO);
                    }
                }
            }
        }
        if (CollUtil.isEmpty(signReqVOS)) {
            throw new CdkitCloudException("请稍后重试，获取图纸属性失败,只有使用了标准图框的图纸才能使用签章功能");
        }
        SignSaveReqVO signSaveReqVO = new SignSaveReqVO();
        signSaveReqVO.setDocId(docId);
        signSaveReqVO.setDwgPath(dwgPath);
        signSaveReqVO.setData(signReqVOS);
        log.info("开始写入签章，id：{}，signSaveReqVO： {}", id, JSONUtil.toJsonStr(signSaveReqVO));
        cadFeign.saveSign(signSaveReqVO);
        //CadResultDTO<Void> signResultVO = cadFeign.saveSign(signSaveReqVO);
        //if (!signResultVO.isSuccess()) {
        //    log.error("签章写入异常，请求参数： {}", JSONUtil.toJsonStr(signSaveReqVO));
        //    throw new CdkitCloudException("签章写入异常");
        //}
        log.info("签章写入完成，id：{}，signSaveReqVO： {}", id, JSONUtil.toJsonStr(signSaveReqVO));
    }

    @SneakyThrows
    @Override
    public MultipartFile transPdf(String id) {
        DesignDocument designDocument = designDocumentService.getById(id);
        if (!Constants.DRAWING.equals(designDocument.getCategory())) {
            log.error("当前只有图纸可以转换pdf,{}", id);
            throw new CdkitCloudException("当前只有图纸可以转换pdf");
        }
        String docId = designDocument.getDocId();
        //获取图纸属性
        //CadResultDTO<List<BlockDataDTO>> propertiesRet = cadFeign.getProperties(docId + ".dwg", docId);
        //if (!propertiesRet.isSuccess()) {
        //    log.error("调用中望cad获取图纸属性接口报错,id:{}", id);
        //    throw new SftdCloudException(String.format("获取图纸属性报错,id:%s", id));
        //}
        //int layoutHandle = propertiesRet.getData().get(0).getLayoutHandle();
        JSONObject jsonObject = JSONUtil.parseObj(String.format(transPdfReqJson, docId, -1));
        byte[] bytes;
        try {
            log.info("第一次预览 {}", docId);
            bytes = cadFeign.print(jsonObject);
            if (bytes.length == 0) {
                log.error("图纸转pdf失败，id：{}，docId：{}", id, docId);
                throw new CdkitCloudException("图纸转pdf失败");
            }
        } catch (Exception e) {
            try {
                log.info("第二次预览 {}", docId);
                bytes = cadFeign.print(jsonObject);
            } catch (Exception ex) {
                log.error("图纸转pdf失败，id：{}，docId：{}", id, docId);
                throw new CdkitCloudException("图纸转pdf失败");
            }
        }
        String name = StringUtils.getFilename(designDocument.getName()) + ".pdf";
        return new MockMultipartFile(name, name, MediaType.APPLICATION_OCTET_STREAM_VALUE, bytes);
    }

    @SneakyThrows
    @Override
    public MultipartFile getDwgFile(String id) {
        DesignDocument designDocument = designDocumentService.getById(id);
        String docId = designDocument.getDocId();
        if (docId == null) {
            log.error("docId为空，不能获取中望cad最新的图纸");
            throw new CdkitCloudException("docId为空");
        }
        byte[] bytes = cadFeign.download(docId);
        if (bytes.length == 0) {
            log.error("下载中望cad文件失败，id：{}，docId：{}", id, docId);
            throw new CdkitCloudException("dwg文件获取异常");
        }
        return new MockMultipartFile(designDocument.getName(), designDocument.getName(), MediaType.APPLICATION_OCTET_STREAM_VALUE, bytes);
    }

    @Override
    public MultipartFile dwg2Image(String signatureId) {
        Signature signature = signatureService.getById(signatureId);
        if (signature != null) {
            String docId = signature.getDocId();
            JSONObject jsonObject = JSONUtil.parseObj(String.format(formatTransferJson, docId));
            byte[] bytes = cadFeign.formatTransfer(jsonObject);
            return new MockMultipartFile(signature.getFileName(), signature.getFileName(), MediaType.IMAGE_PNG_VALUE, bytes);
        }
        return null;
    }

    @Override
    public void documentView(String id, HttpServletResponse response) {
        DesignDocument designDocument = designDocumentService.getById(id);
        try (InputStream inputStream = CustomMinioUtil.getFile(designDocument.getFilePath())) {
            String filename = StringUtils.getFilename(designDocument.getFilePath());
            response.setContentType(MediaTypeFactory.getMediaType(filename).orElse(MediaType.APPLICATION_OCTET_STREAM).toString());
            OutputStream outputStream = response.getOutputStream();
            IoUtil.copy(inputStream, outputStream);
            response.flushBuffer();
        } catch (Exception e) {
            log.error("预览文件失败", e);
            response.setStatus(404);
        }
    }
}
