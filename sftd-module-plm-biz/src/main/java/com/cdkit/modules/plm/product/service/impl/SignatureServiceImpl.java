package com.cdkit.modules.plm.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.plm.product.dto.MockMultipartFile;
import com.cdkit.modules.plm.product.entity.Signature;
import com.cdkit.modules.plm.product.event.DwgSyncEvent;
import com.cdkit.modules.plm.product.mapper.SignatureMapper;
import com.cdkit.modules.plm.product.service.SignatureService;
import com.cdkit.modules.plm.util.CustomMinioUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.InputStream;

import jakarta.annotation.Resource;

@Service
@Slf4j
public class SignatureServiceImpl extends ServiceImpl<SignatureMapper, Signature> implements SignatureService {

    @Value("${thirdparty.zwcad.sync:true}")
    private boolean sync;
    @Value("${thirdparty.zwcad.folderId:0}")
    private String folderId;

    @Resource
    ApplicationEventPublisher eventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upload(String username, String filePath) throws Exception {
        Signature one = this.getOne(new LambdaQueryWrapper<Signature>().eq(Signature::getUsername, username));
        if (one != null) {
            throw new CdkitCloudException("当前用户已存在签名，用户名：" + username);
        }
        //if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(".dwg")) {
        //    throw new CdkitCloudException("请上传DWG格式的签名");
        //}
        String filename = StringUtils.getFilename(filePath);
        Signature signature = new Signature();
        signature.setUsername(username);
        signature.setFileName(filename);
        signature.setFilePath(filePath);
        if (sync) {
            InputStream inputStream = CustomMinioUtil.getFile(filePath);
            MockMultipartFile mockMultipartFile = new MockMultipartFile(filename, filename, MediaType.APPLICATION_OCTET_STREAM_VALUE, inputStream);
            signature.setFileSize((int) mockMultipartFile.getSize());
            this.save(signature);
            eventPublisher.publishEvent(new DwgSyncEvent(this, 2, signature.getId(), mockMultipartFile));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(String id, String filePath) throws Exception {
        Signature signature = this.getById(id);
        if (signature == null) {
            throw new CdkitCloudException("找不到该签名数据");
        }
        String filename = StringUtils.getFilename(filePath);
        signature.setFileName(filename);
        InputStream inputStream = CustomMinioUtil.getFile(filePath);
        MockMultipartFile mockMultipartFile = new MockMultipartFile(filename, filename, MediaType.APPLICATION_OCTET_STREAM_VALUE, inputStream);
        signature.setFileSize((int) mockMultipartFile.getSize());
        signature.setFilePath(filePath);
        this.updateById(signature);
        if (sync) {
            eventPublisher.publishEvent(new DwgSyncEvent(this, 2, signature.getId(), mockMultipartFile));
        }
    }
}
