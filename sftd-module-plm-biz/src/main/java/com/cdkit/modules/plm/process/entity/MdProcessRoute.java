package com.cdkit.modules.plm.process.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.cdkit.common.aspect.annotation.Dict;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;


/**
 * @Description: 工艺路线主数据
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Schema(description="md_process_route对象", name="工艺路线主数据")
@Data
@TableName("md_process_route")
public class MdProcessRoute implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
    /**启用状态*/
    @Excel(name = "启用状态", width = 15)
    @Schema(description = "启用状态")
    @Dict(dicCode = "use_status")
    private String useStatus;
	/**工艺路线编码*/
	@Excel(name = "工艺路线编码", width = 15)
    @Schema(description = "工艺路线编码")
    private String processRouteCode;
	/**工艺路线名称*/
	@Excel(name = "工艺路线名称", width = 15)
    @Schema(description = "工艺路线名称")
    private String processRouteName;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
}
