package com.cdkit.modules.plm.common;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：CommonUtil
 * @Date：2024/4/11 10:19
 */
public class CommonUtil {
    /**
     * 12345转abcde
     *
     * @param num
     * @return
     */
    public static String numberToLetter(int num) {
        if (num > 26) {
            return num + "";
        } else {
            char letter = (char) ('a' + num - 1);
            return String.valueOf(letter);
        }
    }
    public static String numberToLetter(double num) {
        if (num > 26) {
            return num + "";
        } else {
            char letter = (char) ('a' + num - 1);
            return String.valueOf(letter);
        }
    }
}
