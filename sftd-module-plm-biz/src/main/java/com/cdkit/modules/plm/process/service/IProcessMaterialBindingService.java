package com.cdkit.modules.plm.process.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.entity.ProcessMaterialBinding;

import java.util.List;

/**
 * @Description: 工序与投入物绑定
 * @Author: cdkit-boot
 * @Date:   2025-08-11
 * @Version: V1.0
 */
public interface IProcessMaterialBindingService extends IService<ProcessMaterialBinding> {

    /**
     * 删除工序绑定的物料
     * @param productFormulationId 配方ID
     * @param processId 工序ID
     */
    void deleteByFormulationIdAndProcessId(String productFormulationId, String processId);

    /**
     * 查询工序绑定的物料
     * @param formulationId 配方ID
     * @param processId 工序ID
     * @return 物料
     */
    List<ProcessMaterialBinding> selectByFormulationIdAndProcessId(String formulationId, String processId);
}
