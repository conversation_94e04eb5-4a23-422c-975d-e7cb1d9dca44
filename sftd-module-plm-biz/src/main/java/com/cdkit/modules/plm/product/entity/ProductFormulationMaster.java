package com.cdkit.modules.plm.product.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkit.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: product_formulation_master
 * @Author: cdkit-boot
 * @Date:   2025-06-10
 * @Version: V1.0
 */
@Data
@TableName("product_formulation_master")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="product_formulation_master对象")
public class ProductFormulationMaster implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**配方版本号id*/
	@Excel(name = "配方版本号id", width = 15)
    @Schema(description = "配方版本号id")
    private String productFormulationId;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;

    /**上一配方版本号id*/
    @Excel(name = "上一配方版本号id", width = 15)
    @Schema(description = "上一配方版本号id")
    private String beforeFormulationId;
}
