package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：DesignDocumentInboundVO
 * @Date：2024/4/5 16:12
 */
@Data
@Schema(description = "图纸出入库vo")
public class DesignDocumentOutboundVO {

    @NotBlank
    @Schema(description = "图纸id")
    private String id;

    @NotNull
    @Schema(description = "出库")
    private Boolean outbound;
}
