package com.cdkit.modules.plm.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import com.cdkit.common.system.vo.LoginUser;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * @Author：z<PERSON> yang
 * @name：LoginUtil
 * @Date：2024/3/30 18:08
 */
@Slf4j
public class LoginUtil {
    public static LoginUser getCurrentUser() {
        LoginUser loginUser = null;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            log.error("通过shiro获取用户信息失败", e);
        }

        return loginUser;

    }
}
