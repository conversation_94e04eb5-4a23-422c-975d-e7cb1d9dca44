package com.cdkit.modules.plm.didgen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.didgen.entity.DidRule;

import java.util.Map;

public interface DidRuleService extends IService<DidRule> {

    /**
     * 获取业务编码
     *
     * @param code      规格编码，例如XSDD（销售订单）
     * @param variables 所需变量，例如station:SHZS（上海周山）
     * @return 业务编码
     */
    public String getDidCode(String code, Map<String, Object> variables);

    public DidRule getDidRule(String code);
}
