package com.cdkit.modules.plm.product.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：FileSyncEvent
 * @Date：2024/4/25 14:01
 */
public class DwgSyncEvent extends ApplicationEvent {
    /**
     * 主键id
     */
    @Getter
    private final String id;
    /**
     * 1:图纸  2:dwg签名
     */
    @Getter
    private int type;

    @Getter
    private final MultipartFile file;

    public DwgSyncEvent(Object source, int type, String id, MultipartFile file) {
        super(source);
        this.type = type;
        this.id = id;
        this.file = file;
    }

}
