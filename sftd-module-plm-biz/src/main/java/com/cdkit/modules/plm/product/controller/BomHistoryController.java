package com.cdkit.modules.plm.product.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.common.CommonUtil;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.product.entity.BomHistory;
import com.cdkit.modules.plm.product.service.IBomHistoryService;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.FrozenBomVO;
import com.cdkit.modules.plm.product.vo.resp.BomHistoryVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * @Description: bom_history  todo bom比较接口
 * @Author: mc
 * @Date: 2024-03-28
 * @Version: V1.0
 */
@Tag(name = "设计bom-历史bom")
@RestController
@RequestMapping("bomHistory")
@Slf4j
@AllArgsConstructor
public class BomHistoryController extends CdkitController<BomHistory, IBomHistoryService> {
    private final IBomHistoryService bomHistoryService;
    private final IProductTreeService productTreeService;

    /**
     * 分页列表查询
     *
     * @param pid
     * @return
     */
    @Operation(summary = "历史boom-分页列表查询", description = "历史boom-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<BomHistoryVO>> queryPageList(@RequestParam String pid,
                                                     @RequestParam(name = "queryCondition", required = false) String queryCondition,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        LambdaQueryWrapper<BomHistory> wrapper = new LambdaQueryWrapper<BomHistory>()
                .eq(BomHistory::getProductTreeId, pid)
                .like(StrUtil.isNotBlank(queryCondition), BomHistory::getName, queryCondition).orderByDesc(BomHistory::getCreateTime);
        Page<BomHistory> page = new Page<>(pageNo, pageSize);
        IPage<BomHistory> pageList = bomHistoryService.page(page, wrapper);
        IPage<BomHistoryVO> voPage = new Page<>(pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
        voPage.setRecords(pageList.getRecords().stream().map(this::do2vo).collect(Collectors.toList()));
        return Result.OK(voPage);
    }

    @PostMapping("frozenBOM")
    @Operation(summary = "固化BOM", description = "总装或者零部件节点可以进行固化BOM")
    public Result<String> frozenBOM(@RequestBody FrozenBomVO frozenBomVO,@RequestParam(required = false) String materialCodeOrName) throws JsonProcessingException {
        frozenBomVO.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        bomHistoryService.frozenBomVO(frozenBomVO,materialCodeOrName);
        return Result.OK("操作成功");
    }

    /**
     * BOM比较
     * @param ids 节点ID
     * @return 返回结果
     */
    @Operation(summary = "设计BOM比较", description = "一种选中一个bom版本同当前结构树上被固化的零件进行比较，另一种情况选中两个BOM版本进行比较")
    @GetMapping(value = "/compareBom")
    public Result<HashMap<String, Object>> compareBom(@RequestParam(name="ids") String ids,@RequestParam(required = false) String materialCodeOrName) {
        HashMap<String, Object> map = bomHistoryService.compareBom(ids, NodeTypeEnum.PROCESS_TREE,materialCodeOrName);
        if (map == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(map);
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @Operation(summary = "历史boom-通过id删除", description = "历史boom-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        bomHistoryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @Operation(summary = "历史boom-批量删除", description = "历史boom-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.bomHistoryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @Operation(summary = "历史boom-通过id查询", description = "历史boom-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<BomHistoryVO> queryById(@RequestParam(name = "id") String id) {
        BomHistory bomHistory = bomHistoryService.getById(id);
        if (bomHistory == null) {
            return Result.error("未找到对应数据");
        }
        BomHistoryVO bomHistoryVO = do2vo(bomHistory);
        return Result.OK(bomHistoryVO);
    }

    private BomHistoryVO do2vo(BomHistory bomHistory) {
        BomHistoryVO data = BeanUtil.copyProperties(bomHistory, BomHistoryVO.class, "version");
        data.setVersion(CommonUtil.numberToLetter(bomHistory.getVersion()));
        return data;
    }
}
