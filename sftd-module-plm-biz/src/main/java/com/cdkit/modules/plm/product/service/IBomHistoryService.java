package com.cdkit.modules.plm.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.product.entity.BomHistory;
import com.cdkit.modules.plm.product.vo.req.FrozenBomVO;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.HashMap;

/**
 * @Description: bom_history
 * @Author: mc
 * @Date:   2024-03-28
 * @Version: V1.0
 */
public interface IBomHistoryService extends IService<BomHistory> {

    /**
     * 固化bom
     *
     * @param frozenBomVO 固化信息
     * @throws JsonProcessingException 异常抛出
     */
    void frozenBomVO(FrozenBomVO frozenBomVO,String materialCodeOrName) throws JsonProcessingException;
    /**
     * bom比较
     * @param ids 节点ID
     * @param nodeTypeEnum 节点类型
     * @return 返回结果
     */
    HashMap<String, Object> compareBom(String ids, NodeTypeEnum nodeTypeEnum,String materialCodeOrName);
}
