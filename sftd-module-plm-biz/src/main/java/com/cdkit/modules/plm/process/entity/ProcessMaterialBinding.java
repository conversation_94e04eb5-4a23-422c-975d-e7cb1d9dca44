package com.cdkit.modules.plm.process.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkit.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 工序与投入物绑定
 * @Author: cdkit-boot
 * @Date:   2025-08-11
 * @Version: V1.0
 */
@Data
@TableName("process_material_binding")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="process_material_binding对象")
public class ProcessMaterialBinding implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**物料编码*/
	@Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;
	/**配方ID*/
	@Excel(name = "配方ID", width = 15)
    @Schema(description = "配方ID")
    private String formulationId;
	/**配方明细ID*/
	@Excel(name = "配方明细ID", width = 15)
    @Schema(description = "配方明细ID")
    private String formulationDetailId;
	/**工序ID*/
	@Excel(name = "工序ID", width = 15)
    @Schema(description = "工序ID")
    private String processId;
    /**产品ID*/
    @Excel(name = "产品ID", width = 15)
    @Schema(description = "产品ID")
    private String productId;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
}
