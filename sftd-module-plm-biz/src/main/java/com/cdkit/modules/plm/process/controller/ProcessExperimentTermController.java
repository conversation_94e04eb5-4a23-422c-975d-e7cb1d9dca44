package com.cdkit.modules.plm.process.controller;

import java.util.Arrays;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.query.QueryGenerator;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import com.cdkit.modules.plm.process.entity.ProcessExperimentTerm;
import com.cdkit.modules.plm.process.service.IProcessExperimentTermService;
import com.cdkit.modules.plm.process.vo.req.ReqProcessExperimentTermVO;
import com.cdkit.common.system.base.controller.CdkitController;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import com.cdkit.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 工艺实验项
 * @Author: cdkit-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
@Tag(name="工艺实验项")
@RestController
@RequestMapping("/process/processExperimentTerm")
@Slf4j
public class ProcessExperimentTermController extends CdkitController<ProcessExperimentTerm, IProcessExperimentTermService> {
	@Resource
	private IProcessExperimentTermService processExperimentTermService;

	/**
	 * 分页列表查询
	 *
	 * @param processExperimentTerm 查询参数
	 * @param pageNo 页码
	 * @param pageSize 每页条数
	 * @param req  HttpServletRequest
	 * @return 结果返回
	 */
	@Operation(summary="工艺实验项-分页列表查询", description="工艺实验项-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ProcessExperimentTerm>> queryPageList(ProcessExperimentTerm processExperimentTerm,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		if (StringUtils.isEmpty(processExperimentTerm.getRefKey())) {
			processExperimentTerm.setRefKey("1");
		}
		QueryWrapper<ProcessExperimentTerm> queryWrapper = QueryGenerator.initQueryWrapper(processExperimentTerm, req.getParameterMap());
		Page<ProcessExperimentTerm> page = new Page<>(pageNo, pageSize);
		IPage<ProcessExperimentTerm> pageList = processExperimentTermService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param processExperimentTerm 请求参数
	 * @return 结果返回
	 */
	@AutoLog(value = "工艺实验项-添加")
	@Operation(summary="工艺实验项-添加", description="工艺实验项-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ProcessExperimentTerm processExperimentTerm) {
		processExperimentTermService.save(processExperimentTerm);
		return Result.OK("添加成功！");
	}

	 /**
	  *  批量保存实验项
	  *
	  * @param reqProcessExperimentTermVO 请求参数
	  * @return 结果返回
	  */
	 @AutoLog(value = "批量保存实验项")
	 @Operation(summary="批量保存实验项", description="批量保存实验项")
	 @PostMapping(value = "/addBatch")
	 public Result<String> addBatch(@RequestBody ReqProcessExperimentTermVO reqProcessExperimentTermVO) {
		 processExperimentTermService.addBatch(reqProcessExperimentTermVO);
		 return Result.OK("添加成功！");
	 }

	/**
	 *  编辑
	 *
	 * @param processExperimentTerm 请求参数
	 * @return 结果返回
	 */
	@AutoLog(value = "工艺实验项-编辑")
	@Operation(summary="工艺实验项-编辑", description="工艺实验项-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ProcessExperimentTerm processExperimentTerm) {
		processExperimentTermService.updateById(processExperimentTerm);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id 主键ID
	 * @return 结果返回
	 */
	@AutoLog(value = "工艺实验项-通过id删除")
	@Operation(summary="工艺实验项-通过id删除", description="工艺实验项-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		processExperimentTermService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids 主键ID
	 * @return 结果返回
	 */
	@AutoLog(value = "工艺实验项-批量删除")
	@Operation(summary="工艺实验项-批量删除", description="工艺实验项-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.processExperimentTermService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id  主键ID
	 * @return 结果返回
	 */
	@Operation(summary="工艺实验项-通过id查询", description="工艺实验项-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ProcessExperimentTerm> queryById(@RequestParam(name="id") String id) {
		ProcessExperimentTerm processExperimentTerm = processExperimentTermService.getById(id);
		if(processExperimentTerm==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(processExperimentTerm);
	}

    /**
    * 导出excel
    *
    * @param request HttpServletRequest
    * @param processExperimentTerm  请求参数
    */
    @RequiresPermissions("process:process_experiment_term:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProcessExperimentTerm processExperimentTerm) {
        return super.exportXls(request, processExperimentTerm, ProcessExperimentTerm.class, "工艺实验项");
    }

    /**
      * 通过excel导入数据
    *
    * @param request HttpServletRequest
    * @param response HttpServletResponse
    * @return 结果返回
    */
	@Operation(summary="工艺实验项-导入", description="工艺实验项-导入")
    @RequiresPermissions("process:process_experiment_term:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProcessExperimentTerm.class);
    }

}
