package com.cdkit.modules.plm.product.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11
 */
@Data
@Schema(description = "工序与配方绑定入参信息")
public class ImportProcessRequestVO {
    /**
     * 产成品ID(bomId）
     */
    @Schema(description = "产成品ID(bomId)")
    private String productId;

    /**
     * 配方ID
     */
    @Schema(description = "配方ID")
    private String productFormulationId;

    /**
     * 工艺路线ID
     */
    @Schema(description = "工艺路线ID")
    private String processRouteId;

    /**
     * 操作标识
     */
    @Schema(description = "1：下一步 2：保存")
    private Integer flag;

    /**
     * 工序绑定相关信息
     */
    @Schema(description = "工序绑定相关信息")
    private List<ProcessInfoVO> processInfoList;
}
