package com.cdkit.modules.plm.process.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Schema(description = "工艺卡片新增")
public class ReqProcessCardAddVO {
    /**主键ID*/
    @Schema(description = "主键ID")
    private String id;

    @NotBlank(message = "卡片名称不能为空")
    @Schema(description = "卡片名称")
    private String cardName;

    /**关联process_part零件ID*/
    @NotBlank(message = "零件ID不能为空")
    @Schema(description = "关联process_part零件ID")
    private String partId;

    /**关联process_template模板ID*/
    @NotBlank(message = "模板ID不能为空")
    @Schema(description = "关联process_template模板ID")
    private String templateId;

    /**卡片数据（json）*/
    @Schema(description = "卡片数据（json）")
    @NotBlank(message = "卡片数据不能为空")
    private String cardContent;
}
