package com.cdkit.modules.plm.process.vo.resp;

import com.cdkitframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: boom模板
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Data
@Schema(name="boom模板对象", description="boom模板对象")
public class MdProcessRemarkPage {

	/**物料名称*/
	@Excel(name = "配方编码", width = 15)
	@Schema(description = "配方编码")
    private String formulaCode;
	/**物料编码*/
	@Excel(name = "工艺内容", width = 15)
	@Schema(description = "工艺内容")
	private String remark;


}
