package com.cdkit.modules.plm.operation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdkit.modules.plm.common.LoginUtil;
import com.cdkit.modules.plm.operation.entity.OperationLog;
import com.cdkit.modules.plm.operation.mapper.OperationLogMapper;
import com.cdkit.modules.plm.operation.service.IOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/5/14
 */
@Slf4j
@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements IOperationLogService {

    /**
     * 插入操作记录
     * @param type 操作类型
     * @param content 操作内容
     */
    @Override
    public void insertOperationLog(String type, String content) {
        OperationLog operationLog = new OperationLog();
        operationLog.setName(LoginUtil.getCurrentUser().getRealname());
        operationLog.setType(type);
        operationLog.setContent(content);

        try {
            this.save(operationLog);
        } catch (Exception e) {
            log.error("插入操作记录失败:{}", e);
        }
    }
}
