package com.cdkit.modules.plm.product.vo.req;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：EditDesignDocumentVO
 * @Date：2024/3/28 17:05
 */
@Data
@Schema(description = "编辑产品文档vo")
public class EditDesignDocumentVO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "文档分类，图纸/")
    private String category;

    @Schema(description = "文档后缀类型，exb")
    private String type;

    @Schema(description = "是否已出库")
    private Integer outbound;

    @Schema(description = "是否已红批")
    private Integer redBatch;

    @Schema(description = "是否已签名")
    private Integer sign;

    @Schema(description = "文件大小")
    private Integer fileSize;
}
