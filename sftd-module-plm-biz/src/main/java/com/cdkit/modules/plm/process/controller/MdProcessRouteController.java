package com.cdkit.modules.plm.process.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.cdkit.common.api.vo.Result;
import com.cdkit.common.aspect.annotation.AutoLog;
import com.cdkit.common.system.query.QueryGenerator;
import com.cdkit.common.system.vo.LoginUser;
import com.cdkit.common.util.oConvertUtils;
import com.cdkit.md.api.IMdDeviceApi;
import com.cdkit.md.api.IMdWorkshopApi;
import com.cdkit.md.entity.MdDevice;
import com.cdkit.md.entity.MdWorkshop;
import com.cdkit.modules.plm.process.entity.MdProcess;
import com.cdkit.modules.plm.process.entity.MdProcessRoute;
import com.cdkit.modules.plm.process.entity.MdProcessRouteDetail;
import com.cdkit.modules.plm.process.mapper.MdProcessMapper;
import com.cdkit.modules.plm.process.service.IMdProcessRouteDetailService;
import com.cdkit.modules.plm.process.service.IMdProcessRouteService;
import com.cdkit.modules.plm.process.vo.MdProcessRoutePage;
import com.cdkitframework.poi.excel.ExcelImportUtil;
import com.cdkitframework.poi.excel.def.NormalExcelConstants;
import com.cdkitframework.poi.excel.entity.ExportParams;
import com.cdkitframework.poi.excel.entity.ImportParams;
import com.cdkitframework.poi.excel.view.CdkitEntityExcelView;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;


 /**
 * @Description: 工艺路线主数据
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Tag(name="工艺路线主数据")
@RestController
@RequestMapping("/mdProcessRoute")
@Slf4j
public class MdProcessRouteController {
	@Autowired
	private IMdProcessRouteService mdProcessRouteService;
	@Autowired
	private IMdProcessRouteDetailService mdProcessRouteDetailService;
	@Autowired
	private MdProcessMapper mdProcessMapper;
	@Autowired
	private IMdWorkshopApi mdWorkshopApi;
	@Autowired
	private IMdDeviceApi mdDeviceApi;

	/**
	 * 分页列表查询
	 *
	 * @param mdProcessRoute
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "工艺路线主数据-分页列表查询")
	@Operation(summary="工艺路线主数据-分页列表查询", description="工艺路线主数据-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<MdProcessRoute>> queryPageList(MdProcessRoute mdProcessRoute,
                                                       @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                       @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                       HttpServletRequest req) {
		QueryWrapper<MdProcessRoute> queryWrapper = QueryGenerator.initQueryWrapper(mdProcessRoute, req.getParameterMap());
		Page<MdProcessRoute> page = new Page<MdProcessRoute>(pageNo, pageSize);
		IPage<MdProcessRoute> pageList = mdProcessRouteService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param mdProcessRoutePage
	 * @return
	 */
	@AutoLog(value = "工艺路线主数据-添加")
	@Operation(summary="工艺路线主数据-添加", description="工艺路线主数据-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody MdProcessRoutePage mdProcessRoutePage) {
		MdProcessRoute mdProcessRoute = new MdProcessRoute();
		BeanUtils.copyProperties(mdProcessRoutePage, mdProcessRoute);
		mdProcessRouteService.saveMain(mdProcessRoute, mdProcessRoutePage.getMdProcessRouteDetailList());
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param mdProcessRoutePage
	 * @return
	 */
	@AutoLog(value = "工艺路线主数据-编辑")
	@Operation(summary="工艺路线主数据-编辑", description="工艺路线主数据-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody MdProcessRoutePage mdProcessRoutePage) {
		MdProcessRoute mdProcessRoute = new MdProcessRoute();
		BeanUtils.copyProperties(mdProcessRoutePage, mdProcessRoute);
		MdProcessRoute mdProcessRouteEntity = mdProcessRouteService.getById(mdProcessRoute.getId());
		if(mdProcessRouteEntity==null) {
			return Result.error("未找到对应数据");
		}
		mdProcessRouteService.updateMain(mdProcessRoute, mdProcessRoutePage.getMdProcessRouteDetailList());
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "工艺路线主数据-通过id删除")
	@Operation(summary="工艺路线主数据-通过id删除", description="工艺路线主数据-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		mdProcessRouteService.delMain(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "工艺路线主数据-批量删除")
	@Operation(summary="工艺路线主数据-批量删除", description="工艺路线主数据-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.mdProcessRouteService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "工艺路线主数据-通过id查询")
	@Operation(summary="工艺路线主数据-通过id查询", description="工艺路线主数据-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<MdProcessRoute> queryById(@RequestParam(name="id",required=true) String id) {
		MdProcessRoute mdProcessRoute = mdProcessRouteService.getById(id);
		if(mdProcessRoute==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(mdProcessRoute);

	}

	 /**
	  * 查询全部工艺路线
	  *
	  */
	 @Operation(summary="查询全部工艺路线", description="查询全部工艺路线")
	 @GetMapping(value = "/queryAll")
	 public Result<List<MdProcessRoute>> queryAll() {
		 List<MdProcessRoute> list = mdProcessRouteService.list();
		 return Result.OK(list);

	 }

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "工艺路线明细通过主表ID查询")
	@Operation(summary="工艺路线明细主表ID查询", description="工艺路线明细-通主表ID查询")
	@GetMapping(value = "/queryMdProcessRouteDetailByMainId")
	public Result<List<MdProcessRouteDetail>> queryMdProcessRouteDetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<MdProcessRouteDetail> mdProcessRouteDetailList = mdProcessRouteDetailService.selectByMainId(id);
		return Result.OK(mdProcessRouteDetailList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param mdProcessRoute
    */
    @RequiresPermissions("base:md_process_route:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MdProcessRoute mdProcessRoute) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<MdProcessRoute> queryWrapper = QueryGenerator.initQueryWrapper(mdProcessRoute, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
         List<String> selectionList = Arrays.asList(selections.split(","));
         queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<MdProcessRoute> mdProcessRouteList = mdProcessRouteService.list(queryWrapper);

	  // 查询工序列表
		List<MdProcess> mdProcesses = mdProcessMapper.selectList(null);
		Map<String, String> processNameMap = mdProcesses.stream().collect(
				Collectors.toMap(MdProcess::getId, MdProcess::getProcessName));

		// 查询车间列表
		List<MdWorkshop> mdWorkshops = mdWorkshopApi.queryAllWorkshopDataList();
		Map<String, String> workShopMap = mdWorkshops.stream().collect(
				Collectors.toMap(MdWorkshop::getId, MdWorkshop::getWorkshopName));
		// 查询申报列表
		List<MdDevice> mdDevices = mdDeviceApi.queryMdDeviceList();
		Map<String, String> deviceMap = mdDevices.stream().collect(
				Collectors.toMap(MdDevice::getId, MdDevice::getDeviceName));

		// Step.3 组装pageList
      List<MdProcessRoutePage> pageList = new ArrayList<MdProcessRoutePage>();
      for (MdProcessRoute main : mdProcessRouteList) {
          MdProcessRoutePage vo = new MdProcessRoutePage();
          BeanUtils.copyProperties(main, vo);
          List<MdProcessRouteDetail> mdProcessRouteDetailList = mdProcessRouteDetailService.selectByMainId(main.getId());
		  for (MdProcessRouteDetail mdProcessRouteDetail : mdProcessRouteDetailList) {
			  mdProcessRouteDetail.setProcessName(processNameMap.get(mdProcessRouteDetail.getProcessId()));
			  mdProcessRouteDetail.setWorkshopName(workShopMap.get(mdProcessRouteDetail.getWorkshopId()));
			  mdProcessRouteDetail.setDeviceName(deviceMap.get(mdProcessRouteDetail.getDeviceId()));
		  }
          vo.setMdProcessRouteDetailList(mdProcessRouteDetailList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "工艺路线主数据列表");
      mv.addObject(NormalExcelConstants.CLASS, MdProcessRoutePage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("工艺路线主数据数据", "导出人:"+sysUser.getRealname(), "工艺路线主数据"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
	@Operation(summary="工艺路线-导入", description="工艺路线-导入")
    @RequiresPermissions("base:md_process_route:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<MdProcessRoutePage> list = ExcelImportUtil.importExcel(file.getInputStream(), MdProcessRoutePage.class, params);
              for (MdProcessRoutePage page : list) {
                  MdProcessRoute po = new MdProcessRoute();
                  BeanUtils.copyProperties(page, po);
                  mdProcessRouteService.saveMain(po, page.getMdProcessRouteDetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
