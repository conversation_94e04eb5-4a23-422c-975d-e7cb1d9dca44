package com.cdkit.modules.plm.process.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.entity.ProcessType;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTypeAddVO;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTypeEditVO;

import java.util.Collection;

/**
 * @Description: process_type
 * @Author: mc
 * @Date:   2024-03-27
 * @Version: V1.0
 */
public interface IProcessTypeService extends IService<ProcessType> {
    /**
     * 删除工艺类型
     * @param id 主键ID
     */
    void deleteById(String id);

    /**
     * 批量删除工艺类型
     * @param ids 主键ID
     */
    void deleteByIds(Collection<String> ids);

    /**
     * 新增工艺类型
     * @param reqProcessTypeAddVO 工艺类型
     */
    void add(ReqProcessTypeAddVO reqProcessTypeAddVO);

    /**
     * 编辑工艺类型
     * @param reqProcessTypeEditVO 工艺类型
     */
    void edit(ReqProcessTypeEditVO reqProcessTypeEditVO);
}
