package com.cdkit.modules.plm.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.cdkitframework.poi.excel.annotation.Excel;

/**
 * @Author：z<PERSON> yang
 * @name：ProductPartExtend
 * @Date：2024/8/1 09:59
 */
@Data
@TableName("product_part_extend")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "product_part_extend对象", name = "总装零部件扩展表")
public class ProductPartExtend {
    private static final long serialVersionUID = 1L;

    /**
     * id，同product_tree的id，1:1关系
     */
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id，同product_tree的id，1:1关系")
    private java.lang.String id;
    /**
     * product_tree的id
     */
    @Excel(name = "product_tree的id", width = 15)
    @Schema(description = "product_tree的id")
    private java.lang.String productId;
    /**
     * 自定义键
     */
    @Excel(name = "自定义键", width = 15)
    @Schema(description = "自定义键")
    private java.lang.String definedKey;
    /**
     * 自定义值
     */
    @Excel(name = "自定义值", width = 15)
    @Schema(description = "自定义值")
    private java.lang.String definedValue;

    /**自定义名称*/
    @Excel(name = "自定义名称", width = 15)
    @Schema(description = "自定义名称")
    private String definedName;
    /**自定义类型*/
    @Excel(name = "自定义类型", width = 15)
    @Schema(description = "自定义类型：1扩展字段 2个性化定制字段")
    private String definedType;
    /**排序*/
    @Excel(name = "排序", width = 15)
    @Schema(description = "排序")
    private Double sort;
    /**数据类型 string datetime int BigDecimal*/
    @Excel(name = "数据类型", width = 15)
    @Schema(description = "数据类型 string datetime int BigDecimal")
    private String dbType;
    /**控件类型 text switch*/
    @Excel(name = "控件类型", width = 15)
    @Schema(description = "控件类型 text switch")
    private String fieldShowType;
    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private java.lang.String createBy;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private java.util.Date createTime;
    /**
     * 租户号
     */
    @Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private java.lang.String tenantId;
}
