package com.cdkit.modules.plm.product.vo.resp;

import com.baomidou.mybatisplus.annotation.TableField;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkit.common.aspect.annotation.Dict;
import com.cdkit.common.aspect.annotation.DictEntity;
import com.cdkit.md.entity.MdMaterialExtend;
import com.cdkitframework.poi.excel.annotation.Excel;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author：z<PERSON> yang
 * @name：ProductPartVO
 * @Date：2024/3/28 09:05
 */
@Data
@Schema(description = "总装/零部件")
public class ProductAttributesVO {

    @Schema(description = "id，同product_tree的id，1:1关系")
    private String id;

    @Schema(description = "父级ID")
    private String pid;
    /**
     * 物料名称
     */
    @Excel(name = "物料名称", width = 15)
    @Schema(description = "物料名称", readOnly = true)
    private String materialName;
    /**
     * 物料编码
     */
    @Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @Schema(description = "名称", readOnly = true)
    private String name;
    /**
     * 产品种类代号
     */
    @Excel(name = "产品种类代号", width = 15)
    @Schema(description = "产品种类代号", readOnly = true)
    private String code;
    /**
     * 生产类型对应主数据的物料中操作类型
     */
    @Excel(name = "生产类型", width = 15)
    @Schema(description = "生产类型-来自数据字典-materials_op_type", readOnly = true)
    @DictEntity(dicCode = "materials_op_type")
    private String manufactureType;

    private String manufactureType_dictText;
    /**
     * 料件类型
     */
    @Excel(name = "料件类型", width = 15)
    @Schema(description = "料件类型-来自数据字典-material_item_type", readOnly = true)
    @DictEntity(dicCode = "material_item_type")
    private String partType;

    private String partType_dictText;
    /**
     * 长
     */
    @Excel(name = "长", width = 15)
    @Schema(description = "长", readOnly = true)
    private BigDecimal length;
    /**
     * 宽
     */
    @Excel(name = "宽", width = 15)
    @Schema(description = "宽", readOnly = true)
    private BigDecimal width;
    /**
     * 高
     */
    @Excel(name = "高", width = 15)
    @Schema(description = "高", readOnly = true)
    private BigDecimal height;
    /**
     * 规格
     */
    @Excel(name = "规格", width = 15)
    @Schema(description = "规格", readOnly = true)
    private String specs;
    /**
     * 版本号，a、b、c、d增长
     */
    @Excel(name = "版本号，a、b、c、d增长", width = 15)
    @Schema(description = "版本号，a、b、c、d增长", readOnly = true)
    private String version;
    /**
     * 装配数量
     */
    @Excel(name = "装配数量", width = 15)
    @Schema(description = "装配数量")
    private BigDecimal assembleQuantity;
    /**
     * 装配序号
     */
    @Excel(name = "装配序号", width = 15)
    @Schema(description = "装配序号")
    private Integer assembleSort;
    /**
     * 装配单位
     */
    @Excel(name = "装配单位", width = 15)
    @Schema(description = "装配单位-来自主数据-计量单位", readOnly = true)
    private String assembleUnit;

    /**
     * 序号
     */
    @Excel(name = "序号", width = 15)
    @Schema(description = "序号")
    private String sort;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 描述
     */
    @Schema(description = "描述")
    private String description;

    /**
     * 成品总损耗比例
     */
    @Schema(description = "成品总损耗比例")
    private BigDecimal lossRatio;

    /**
     * 件次号继承来源产品id
     */
    @Schema(description = "件次号继承来源产品id")
    private String extendProductId;

    /**
     * 件次号继承来源产品name
     */
    @Schema(description = "件次号继承来源产品name")
    private String extendProductName;

    /**
     * 扩展属性
     */
    private List<MdMaterialExtend> productPartExtendList;

    /**
     * 定制属性
     */
    private List<MdMaterialExtend> productPartCustomizedList;

    @Schema(description = "是否主材")
    private Integer mainMaterialsFlag;


    @Schema(description = "执行标准")
    private String executionStandards;

    @Schema(description = "制版条件")
    private String plateMakingConditions;

    @Schema(description = "工序指导")
    private String processRemark;
}
