package com.cdkit.modules.plm.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/11
 */
@Getter
@AllArgsConstructor
public enum FormulaStatusEnum {
    /**
     * 新建
     */
    ADD("1", "新建"),
    /**
     * 审核中
     */
    INREVIEW("2", "审核中"),
    /**
     * 已审核
     */
    AUDITED("3", "已审核"),

    /**
     * 已驳回
     */
    REJECT("4", "已驳回");

    private final String code;
    private final String desc;

    public static String findByDes(int code) {
        for (FormulaStatusEnum item : FormulaStatusEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
