package com.cdkit.modules.plm.didgen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "did_rule")
public class DidRule implements Serializable {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 编码
     */
    @TableField(value = "did_code")
    private String didCode;

    /**
     * 名称
     */
    @TableField(value = "did_name")
    private String didName;

    /**
     * serial（流水吗长度，步长，归零值）
     */
    @TableField(value = "expression")
    private String expression;

    private static final long serialVersionUID = 1L;
}
