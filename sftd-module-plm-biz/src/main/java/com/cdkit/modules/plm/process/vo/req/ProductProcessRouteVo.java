package com.cdkit.modules.plm.process.vo.req;

import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class ProductProcessRouteVo implements Serializable {
    private static final long serialVersionUID = 5647207186927123254L;
    @NotBlank(message = "当前产品id不能为空！")
    private String currProductId;
    @Valid
    private List<MdProcessVo> mdProcessVoList;

    private  String processContent;
}
