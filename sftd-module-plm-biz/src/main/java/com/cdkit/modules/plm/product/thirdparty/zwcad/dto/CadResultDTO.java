package com.cdkit.modules.plm.product.thirdparty.zwcad.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：CadResult
 * @Date：2024/4/25 08:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CadResultDTO<T> {
    public static final Integer SUCCESS_CODE = 0;
    private Integer code;
    private String msg;
    private T data;

    public boolean isSuccess() {
        return SUCCESS_CODE.equals(code);
    }
}
