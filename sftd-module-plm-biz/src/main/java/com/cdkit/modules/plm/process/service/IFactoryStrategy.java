package com.cdkit.modules.plm.process.service;


import com.cdkit.modules.plm.product.entity.ProductFormulation;
import com.cdkit.modules.plm.product.entity.ProductTree;
import jakarta.servlet.http.HttpServletRequest;

import java.math.BigDecimal;
import java.util.List;

/**
 * 实现的工厂策略模式
 * <AUTHOR>
 * @date 2024/10/31
 */
public interface IFactoryStrategy {

    /**
     * 查询产品档案列表
     *
     * @return 返回结果
     */
    List<ProductTree> getProductFileList(String materialCodeOrName);

    void createTree(ProductFormulation productFormulation);

    BigDecimal getAssembleQuantity();

    void createApproval(String productFormulationId, HttpServletRequest request) ;
}
