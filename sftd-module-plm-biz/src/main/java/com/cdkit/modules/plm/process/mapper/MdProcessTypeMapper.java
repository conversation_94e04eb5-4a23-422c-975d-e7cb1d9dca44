package com.cdkit.modules.plm.process.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.common.system.vo.SelectTreeModel;
import com.cdkit.modules.plm.process.entity.MdProcessType;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description: 工序分类主数据
 * @Author: mc
 * @Date:   2024-07-26
 * @Version: V1.0
 */
public interface MdProcessTypeMapper extends BaseMapper<MdProcessType> {

	/**
	 * 编辑节点状态
	 * @param id
	 * @param status
	 */
	void updateTreeNodeStatus(@Param("id") String id, @Param("status") String status);

	/**
	 * 【vue3专用】根据父级ID查询树节点数据
	 *
	 * @param pid
	 * @param query
	 * @return
	 */
	List<SelectTreeModel> queryListByPid(@Param("pid") String pid, @Param("query") Map<String, String> query);

}
