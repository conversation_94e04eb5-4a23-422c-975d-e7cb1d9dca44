package com.cdkit.modules.plm.process.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Schema(description = "工艺模板新增")
public class ReqProcessTemplateAddVO {
    /**模板名称*/
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 64, message = "模板名称长度不能超过64个字符")
    @Schema(description = "模板名称名称", required = true)
    private String templateName;
    /**工艺类型ID*/
    @NotBlank(message = "工艺类型ID不能为空")
    @Schema(description = "工艺类型ID", required = true)
    private String processTypeId;
}
