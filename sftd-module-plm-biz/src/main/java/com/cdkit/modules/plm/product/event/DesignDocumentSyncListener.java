package com.cdkit.modules.plm.product.event;

import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.plm.product.entity.DesignDocument;
import com.cdkit.modules.plm.product.entity.Signature;
import com.cdkit.modules.plm.product.service.IDesignDocumentService;
import com.cdkit.modules.plm.product.service.SignatureService;
import com.cdkit.modules.plm.product.thirdparty.zwcad.CadFeign;
import com.cdkit.modules.plm.product.thirdparty.zwcad.dto.CadResultDTO;
import com.cdkit.modules.plm.product.thirdparty.zwcad.dto.UploadRespDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;

/**
 * @Author：zhao yang
 * @name：DesignDocumentSyncListener
 * @Date：2024/4/25 14:11
 */
@Component
@Slf4j
public class DesignDocumentSyncListener implements ApplicationListener<DwgSyncEvent> {

    @Resource
    IDesignDocumentService designDocumentService;
    @Resource
    SignatureService signatureService;
    @Resource
    CadFeign cadFeign;

    @Value("${thirdparty.zwcad.sync:true}")
    private boolean sync;
    @Value("${thirdparty.zwcad.folderId:0}")
    private String folderId;

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onApplicationEvent(DwgSyncEvent event) {
        if (!sync) {
            log.info("未开始同步");
            return;
        }
        String id = event.getId();
        MultipartFile file = event.getFile();
        CadResultDTO<UploadRespDTO> ret = cadFeign.upload(folderId, file);
        if (!ret.isSuccess()) {
            throw new CdkitCloudException(String.format("同步失败，文件名：%s", file.getOriginalFilename()));
        }
        int type = event.getType();
        if (type == 1) {

            DesignDocument designDocument = designDocumentService.getById(id);
            designDocument.setDocId(ret.getData().getDocId());
            //获取轻量化文件
            //try {
            //MultipartFile pdf = cadHandleService.transPdf(id);
            //String pdfFilePath = CustomMinioUtil.upload(pdf, Constants.MINIO_RELATIVE);
            //designDocument.setLightFilePath(pdfFilePath);
            //designDocument.setLightFileName(pdf.getName());
            //
            //} catch (Exception e) {
            //    log.error("转换轻量化文件报错，id：{}", id);
            //}
            designDocumentService.updateById(designDocument);
        } else {
            Signature signature = signatureService.getById(id);
            signature.setDocId(ret.getData().getDocId());
            signatureService.updateById(signature);
        }
    }
}
