<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.plm.process.mapper.ProductProcessRouteMapper">

    <delete id="removeByCurProductId">
        update product_process_route set is_new = 0 where curr_product_id = #{currProductId}
    </delete>
    <delete id="removeByFormulationId">
        update product_process_route set is_new = 0 where formulation_id = #{formulationId}
    </delete>
    <select id="selectProcessRouteDetails" resultType="com.cdkit.modules.plm.process.vo.resp.ScheduleProcessRouteResp">
        SELECT
            ppr.material_code as productCode,
            ppr.process_code as routingCode,
            mp.process_name as routingName,
            mp.work_hour as processDuration
        FROM
            product_process_route ppr
                JOIN md_process mp ON ppr.process_code = mp.process_code
        where
            ppr.process_code in
            <foreach collection="processCodeList" item="code" open="(" close=")" separator=",">
                #{code}
            </foreach>
          and ppr.del_flag = 0
          and mp.del_flag = 0
          group by ppr.material_code, ppr.process_code, mp.process_name, mp.work_hour
    </select>
</mapper>
