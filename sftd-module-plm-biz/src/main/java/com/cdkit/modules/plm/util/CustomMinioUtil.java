package com.cdkit.modules.plm.util;

import lombok.extern.slf4j.Slf4j;
import com.cdkit.common.util.MinioUtil;
import com.cdkit.common.util.SpringContextUtils;
import com.cdkit.modules.plm.config.CustomMinioConfig;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * @Author：zhao yang
 * @name：MinioUtil
 * @Date：2024/4/5 17:01
 * 临时方案，先替换
 */
@Slf4j
public class CustomMinioUtil {

    //minio_url: http://s3.sf-dev.io
    //minio_name: admin
    //minio_pass: sftd@2024123456
    //bucketName: plm

    //static {
    //    MinioUtil.setMinioUrl("http://s3.sf-dev.io");
    //    MinioUtil.setMinioName("admin");
    //    MinioUtil.setMinioPass("sftd@2024123456");
    //    MinioUtil.setBucketName("plm");
    //}


    public static String upload(MultipartFile multipartFile, String relativePath) throws Exception {
        String uploadPath = MinioUtil.upload(multipartFile, relativePath);
        return uploadPath
                .replace(SpringContextUtils.getBean(CustomMinioConfig.class).getMinioUrl(), "")
                //.replace(SpringContextUtils.getBean(CustomMinioConfig.class).getBucketName(), "")
                .replace("//", "/");
    }

    public static InputStream getFile(String filePath) {
        String bucket = filePath.substring(0, filePath.indexOf("/"));
        return MinioUtil.getMinioFile(bucket, filePath.substring(filePath.indexOf("/") + 1));
    }

}
