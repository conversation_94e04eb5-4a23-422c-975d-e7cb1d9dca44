package com.cdkit.modules.plm.product.controller;
import java.util.Arrays;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.plm.product.entity.ProductOrderExtend;
import com.cdkit.modules.plm.product.service.IProductOrderExtendService;
import com.cdkit.modules.plm.product.vo.req.ProductGeneralManualVO;
import com.cdkit.modules.plm.product.vo.req.ProductUploadVO;
import com.cdkit.modules.plm.product.vo.req.TechnicalRequirementsVO;
import com.cdkit.modules.plm.product.vo.resp.ProductExtendVO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.query.QueryGenerator;

import lombok.extern.slf4j.Slf4j;
import com.cdkit.common.system.base.controller.CdkitController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.cdkit.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 zhaoyang
 * @Description: product_order_extend
 * @Author: cdkit-boot
 * @Date:   2025-06-23
 * @Version: V1.0
 */
@Tag(name="product_order_extend")
@RestController
@RequestMapping("/product/productOrderExtend")
@Slf4j
public class ProductOrderExtendController extends CdkitController<ProductOrderExtend, IProductOrderExtendService> {
	@Autowired
	private IProductOrderExtendService productOrderExtendService;
	
	/**
	 * 分页列表查询
	 *
	 * @param productOrderExtend
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "product_order_extend-分页列表查询")
	@Operation(summary="product_order_extend-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ProductOrderExtend>> queryPageList(ProductOrderExtend productOrderExtend,
														   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														   HttpServletRequest req) {
		QueryWrapper<ProductOrderExtend> queryWrapper = QueryGenerator.initQueryWrapper(productOrderExtend, req.getParameterMap());
		Page<ProductOrderExtend> page = new Page<ProductOrderExtend>(pageNo, pageSize);
		IPage<ProductOrderExtend> pageList = productOrderExtendService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param productOrderExtend
	 * @return
	 */
	@AutoLog(value = "product_order_extend-添加")
	@Operation(summary="product_order_extend-添加")
	@RequiresPermissions("product:product_order_extend:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ProductOrderExtend productOrderExtend) {
		productOrderExtendService.save(productOrderExtend);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param productOrderExtend
	 * @return
	 */
	@AutoLog(value = "product_order_extend-编辑")
	@Operation(summary="product_order_extend-编辑")
	@RequiresPermissions("product:product_order_extend:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ProductOrderExtend productOrderExtend) {
		productOrderExtendService.updateById(productOrderExtend);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "product_order_extend-通过id删除")
	@Operation(summary="product_order_extend-通过id删除")
	@RequiresPermissions("product:product_order_extend:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		productOrderExtendService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "product_order_extend-批量删除")
	@Operation(summary="product_order_extend-批量删除")
	@RequiresPermissions("product:product_order_extend:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.productOrderExtendService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "product_order_extend-通过id查询")
	@Operation(summary="product_order_extend-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ProductOrderExtend> queryById(@RequestParam(name="id",required=true) String id) {
		ProductOrderExtend productOrderExtend = productOrderExtendService.getById(id);
		if(productOrderExtend==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(productOrderExtend);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param productOrderExtend
    */
    @RequiresPermissions("product:product_order_extend:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProductOrderExtend productOrderExtend) {
        return super.exportXls(request, productOrderExtend, ProductOrderExtend.class, "product_order_extend");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("product:product_order_extend:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProductOrderExtend.class);
    }


	 /**
	  *   检验技术要求书
	  *
	  * @param technicalRequirementsVO
	  * @return
	  */
	 @Operation(summary="添加检验技术要求书")
	 @PostMapping(value = "/addTechnicalRequirements")
	 public Result<String> addTechnicalRequirements(@RequestBody TechnicalRequirementsVO technicalRequirementsVO) {
		 productOrderExtendService.addTechnicalRequirements(technicalRequirementsVO);
		 return Result.OK("添加成功！");
	 }


	 /**
	  *   产品通用说明书
	  *
	  * @param productGeneralManualVO
	  * @return
	  */
	 @Operation(summary="添加产品通用说明书")
	 @PostMapping(value = "/addProductGeneralManual")
	 public Result<String> addProductGeneralManual(@RequestBody ProductGeneralManualVO productGeneralManualVO) {
		 productOrderExtendService.addProductGeneralManual(productGeneralManualVO);
		 return Result.OK("添加成功！");
	 }


	 /**
	  *   附件上传
	  *
	  * @param productUploadVO
	  * @return
	  */
	 @Operation(summary="附件上传")
	 @PostMapping(value = "/addProductUpload")
	 public Result<String> addProductUpload(@RequestBody ProductUploadVO productUploadVO) {
		 productOrderExtendService.addProductUpload(productUploadVO);
		 return Result.OK("添加成功！");
	 }



	 /**
	  * 通过配方id查询拓展信息
	  *
	  * @param id
	  * @return
	  */
	 @Operation(summary="通过配方id查询拓展信息")
	 @GetMapping(value = "/queryByFormulaId")
	 public Result<ProductExtendVO> queryByFormulaId(@RequestParam(name="id",required=true) String id) {
		 ProductExtendVO productOrderExtend = productOrderExtendService.queryByFormulaId(id);
		 if(productOrderExtend==null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(productOrderExtend);
	 }

}
