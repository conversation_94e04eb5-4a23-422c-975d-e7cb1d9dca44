package com.cdkit.modules.plm.product.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.modules.plm.product.entity.ProductOrderExtend;
import com.cdkit.modules.plm.product.mapper.ProductFormulationMapper;
import com.cdkit.modules.plm.product.mapper.ProductOrderExtendMapper;
import com.cdkit.modules.plm.product.service.IProductOrderExtendService;
import com.cdkit.modules.plm.product.vo.req.ProductGeneralManualVO;
import com.cdkit.modules.plm.product.vo.req.ProductUploadVO;
import com.cdkit.modules.plm.product.vo.req.TechnicalRequirementsVO;
import com.cdkit.modules.plm.product.vo.resp.ProductExtendVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: product_order_extend
 * @Author: cdkit-boot
 * @Date:   2025-06-23
 * @Version: V1.0
 */
@Service
public class ProductOrderExtendServiceImpl extends ServiceImpl<ProductOrderExtendMapper, ProductOrderExtend> implements IProductOrderExtendService {
    @Resource
    private ProductOrderExtendMapper productOrderExtendMapper;
    @Override
    public void addTechnicalRequirements(TechnicalRequirementsVO technicalRequirementsVO) {
        if("2".equals(technicalRequirementsVO.getUpdateFlag())){
            productOrderExtendMapper.delete(new LambdaQueryWrapper<ProductOrderExtend>()
                    .eq(ProductOrderExtend::getProductOrderId, technicalRequirementsVO.getId())
                    .eq(ProductOrderExtend::getExtendType,"1"));
        }
            List<ProductOrderExtend> addList = new ArrayList<>();
            List<ProductOrderExtend> fileList = new ArrayList<>();
            ProductOrderExtend productOrderExtend = new ProductOrderExtend();
            productOrderExtend.setDefinedKey("replace");
            productOrderExtend.setDefinedValue(technicalRequirementsVO.getReplace());
            productOrderExtend.setProductOrderId(technicalRequirementsVO.getId());
            productOrderExtend.setDefinedName("代替");
            productOrderExtend.setDefinedType("1");
            productOrderExtend.setDelFlag(0);
            productOrderExtend.setExtendType("1");

            if (null!=technicalRequirementsVO.getTechnicalRequirementsFile()) {
                ProductOrderExtend fileUrl = new ProductOrderExtend();
                fileUrl.setDefinedKey("technicalRequirementsFile");
                fileUrl.setDefinedValue(technicalRequirementsVO.getTechnicalRequirementsFile());
                fileUrl.setProductOrderId(technicalRequirementsVO.getId());
                fileUrl.setDefinedName("检验技术要求书文件");
                fileUrl.setDefinedType("1");
                fileUrl.setExtendType("1");
                fileUrl.setDelFlag(0);
                fileList.add(fileUrl);
            }

            ProductOrderExtend productOrderExtend2 = new ProductOrderExtend();
        productOrderExtend2.setDefinedKey("normativeReferenceDocuments");
        productOrderExtend2.setDefinedValue(technicalRequirementsVO.getNormativeReferenceDocuments());
        productOrderExtend2.setProductOrderId(technicalRequirementsVO.getId());
        productOrderExtend2.setDefinedName("规范性引用文件");
        productOrderExtend2.setDefinedType("1");
        productOrderExtend2.setExtendType("1");
        productOrderExtend2.setDelFlag(0);

            ProductOrderExtend productOrderExtend3 = new ProductOrderExtend();
        productOrderExtend3.setDefinedKey("testMethod");
        productOrderExtend3.setDefinedValue(technicalRequirementsVO.getTestMethod());
        productOrderExtend3.setProductOrderId(technicalRequirementsVO.getId());
        productOrderExtend3.setDefinedName("试验方法");
        productOrderExtend3.setDefinedType("1");
        productOrderExtend3.setExtendType("1");
        productOrderExtend3.setDelFlag(0);

            ProductOrderExtend productOrderExtend4 = new ProductOrderExtend();
        productOrderExtend4.setDefinedKey("inspectionRules");
        productOrderExtend4.setDefinedValue(technicalRequirementsVO.getInspectionRules());
        productOrderExtend4.setProductOrderId(technicalRequirementsVO.getId());
        productOrderExtend4.setDefinedName("检验规则");
        productOrderExtend4.setDefinedType("1");
        productOrderExtend4.setExtendType("1");
        productOrderExtend4.setDelFlag(0);
            addList.add(productOrderExtend);
            addList.add(productOrderExtend2);
            addList.add(productOrderExtend3);
            addList.add(productOrderExtend4);

            this.saveBatch(addList);
            this.saveBatch(fileList);


    }

    @Override
    public void addProductGeneralManual(ProductGeneralManualVO productGeneralManualVO) {
        if("2".equals(productGeneralManualVO.getUpdateFlag())){
            productOrderExtendMapper.delete(new LambdaQueryWrapper<ProductOrderExtend>()
                    .eq(ProductOrderExtend::getProductOrderId, productGeneralManualVO.getId())
                    .eq(ProductOrderExtend::getExtendType,"2"));
        }

        List<ProductOrderExtend> addList = new ArrayList<>();
        ProductOrderExtend productOrderExtend = new ProductOrderExtend();
        productOrderExtend.setDefinedKey("basicProperties");
        productOrderExtend.setDefinedValue(productGeneralManualVO.getBasicProperties());
        productOrderExtend.setProductOrderId(productGeneralManualVO.getId());
        productOrderExtend.setDefinedName("基本性质");
        productOrderExtend.setDefinedType("1");
        productOrderExtend.setExtendType("2");
        productOrderExtend.setDelFlag(0);

        ProductOrderExtend productOrderExtend1 = new ProductOrderExtend();
        productOrderExtend1.setDefinedKey("productOverview");
        productOrderExtend1.setDefinedValue(productGeneralManualVO.getProductOverview());
        productOrderExtend1.setProductOrderId(productGeneralManualVO.getId());
        productOrderExtend1.setDefinedName("产品概述");
        productOrderExtend1.setDefinedType("1");
        productOrderExtend1.setExtendType("2");
        productOrderExtend1.setDelFlag(0);

        ProductOrderExtend productOrderExtend2= new ProductOrderExtend();
        productOrderExtend2.setDefinedKey("productSpecification");
        productOrderExtend2.setDefinedValue(productGeneralManualVO.getProductSpecification());
        productOrderExtend2.setProductOrderId(productGeneralManualVO.getId());
        productOrderExtend2.setDefinedName("产品主要技术特点");
        productOrderExtend2.setDefinedType("1");
        productOrderExtend2.setExtendType("2");
        productOrderExtend2.setDelFlag(0);

        ProductOrderExtend productOrderExtend3 = new ProductOrderExtend();
        productOrderExtend3.setDefinedKey("scopeApplication");
        productOrderExtend3.setDefinedValue(productGeneralManualVO.getScopeApplication());
        productOrderExtend3.setProductOrderId(productGeneralManualVO.getId());
        productOrderExtend3.setDefinedName("适用范围");
        productOrderExtend3.setDefinedType("1");
        productOrderExtend3.setExtendType("2");
        productOrderExtend3.setDelFlag(0);

        ProductOrderExtend productOrderExtend4 = new ProductOrderExtend();
        productOrderExtend4.setDefinedKey("safetyPrecautions");
        productOrderExtend4.setDefinedValue(productGeneralManualVO.getSafetyPrecautions());
        productOrderExtend4.setProductOrderId(productGeneralManualVO.getId());
        productOrderExtend4.setDefinedName("安全注意事项（安全特性）");
        productOrderExtend4.setDefinedType("1");
        productOrderExtend4.setExtendType("2");
        productOrderExtend4.setDelFlag(0);

        ProductOrderExtend productOrderExtend5 = new ProductOrderExtend();
        productOrderExtend5.setDefinedKey("safetyPacket");
        productOrderExtend5.setDefinedValue(productGeneralManualVO.getSafetyPacket());
        productOrderExtend5.setProductOrderId(productGeneralManualVO.getId());
        productOrderExtend5.setDefinedName("安全注意事项（包装）");
        productOrderExtend5.setDefinedType("1");
        productOrderExtend5.setExtendType("2");
        productOrderExtend5.setDelFlag(0);

        ProductOrderExtend productOrderExtend6 = new ProductOrderExtend();
        productOrderExtend6.setDefinedKey("oilfieldCases");
        productOrderExtend6.setDefinedValue(productGeneralManualVO.getOilfieldCases());
        productOrderExtend6.setProductOrderId(productGeneralManualVO.getId());
        productOrderExtend6.setDefinedName("应用油田案例");
        productOrderExtend6.setDefinedType("1");
        productOrderExtend6.setExtendType("2");
        productOrderExtend6.setDelFlag(0);

        addList.add(productOrderExtend);
        addList.add(productOrderExtend1);
        addList.add(productOrderExtend2);
        addList.add(productOrderExtend3);
        addList.add(productOrderExtend4);
        addList.add(productOrderExtend5);
        addList.add(productOrderExtend6);

        this.saveBatch(addList);

    }

    @Override
    public void addProductUpload(ProductUploadVO productUploadVO) {
        if("2".equals(productUploadVO.getUpdateFlag())){
            productOrderExtendMapper.delete(new LambdaQueryWrapper<ProductOrderExtend>()
                    .eq(ProductOrderExtend::getProductOrderId, productUploadVO.getId())
                    .eq(ProductOrderExtend::getExtendType,"3"));
        }
        List<ProductOrderExtend> addList = new ArrayList<>();
        if(null!=productUploadVO.getMaterialCodeFileList()) {
            ProductOrderExtend fileUrl = new ProductOrderExtend();
            fileUrl.setDefinedKey("materialCodeFile");
            fileUrl.setDefinedValue(productUploadVO.getMaterialCodeFileList());
            fileUrl.setProductOrderId(productUploadVO.getId());
            fileUrl.setDefinedName("物资编码申请表");
            fileUrl.setDefinedType("1");
            fileUrl.setExtendType("3");
            fileUrl.setDelFlag(0);
            addList.add(fileUrl);
        }

        if(null!=productUploadVO.getProductSdsList()) {
                ProductOrderExtend fileUrl1 = new ProductOrderExtend();
            fileUrl1.setDefinedKey("productSds");
            fileUrl1.setDefinedValue(productUploadVO.getProductSdsList());
            fileUrl1.setProductOrderId(productUploadVO.getId());
            fileUrl1.setDefinedName("产品sds");
            fileUrl1.setDefinedType("1");
            fileUrl1.setExtendType("3");
            fileUrl1.setDelFlag(0);
                addList.add(fileUrl1);

        }

        if(null!=productUploadVO.getSecurityLabelList()) {
                ProductOrderExtend fileUrl2 = new ProductOrderExtend();
            fileUrl2.setDefinedKey("securityLabel");
            fileUrl2.setDefinedValue(productUploadVO.getSecurityLabelList());
            fileUrl2.setProductOrderId(productUploadVO.getId());
            fileUrl2.setDefinedName("安全标签");
            fileUrl2.setDefinedType("1");
            fileUrl2.setExtendType("3");
            fileUrl2.setDelFlag(0);
                addList.add(fileUrl2);
        }


        this.saveBatch(addList);
    }

    @Override
    public ProductExtendVO queryByFormulaId(String id) {
        ProductExtendVO productExtendVO = new ProductExtendVO();
        //根据配方id查询出所有信息
        List<ProductOrderExtend> list = this.list(new LambdaQueryWrapper<ProductOrderExtend>().eq(ProductOrderExtend::getProductOrderId, id));
        //检验技术要求书
        TechnicalRequirementsVO technicalRequirementsVO = new TechnicalRequirementsVO();
        List<ProductOrderExtend> replace = list.stream()
                .filter(item -> "replace".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (replace.size() > 0) {
            technicalRequirementsVO.setReplace(replace.get(0).getDefinedValue());
        }
        List<ProductOrderExtend> technicalRequirementsFileList = list.stream()
                .filter(item -> "technicalRequirementsFile".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (technicalRequirementsFileList.size() > 0) {
            technicalRequirementsVO.setTechnicalRequirementsFile(technicalRequirementsFileList.get(0).getDefinedValue());
        }
        List<ProductOrderExtend> normativeReferenceDocuments = list.stream()
                .filter(item -> "normativeReferenceDocuments".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (normativeReferenceDocuments.size() > 0) {
            technicalRequirementsVO.setNormativeReferenceDocuments(normativeReferenceDocuments.get(0).getDefinedValue());
        }
        List<ProductOrderExtend> testMethod = list.stream()
                .filter(item -> "testMethod".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (testMethod.size() > 0) {
            technicalRequirementsVO.setTestMethod(testMethod.get(0).getDefinedValue());
        }
        List<ProductOrderExtend> inspectionRules = list.stream()
                .filter(item -> "inspectionRules".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (replace.size() > 0) {
            technicalRequirementsVO.setInspectionRules(inspectionRules.get(0).getDefinedValue());
        }

        //产品通用说明书
        ProductGeneralManualVO productGeneralManualVO = new ProductGeneralManualVO();
        List<ProductOrderExtend> basicProperties = list.stream()
                .filter(item -> "basicProperties".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (basicProperties.size() > 0) {
            productGeneralManualVO.setBasicProperties(basicProperties.get(0).getDefinedValue());
        }
        //产品概述
        List<ProductOrderExtend> productOverviews = list.stream()
                .filter(item -> "productOverview".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (productOverviews.size() > 0) {
            productGeneralManualVO.setProductOverview(productOverviews.get(0).getDefinedValue());
        }
        List<ProductOrderExtend> productSpecification = list.stream()
                .filter(item -> "productSpecification".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (productSpecification.size() > 0) {
            productGeneralManualVO.setProductSpecification(productSpecification.get(0).getDefinedValue());
        }
        List<ProductOrderExtend> scopeApplication = list.stream()
                .filter(item -> "scopeApplication".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (scopeApplication.size() > 0) {
            productGeneralManualVO.setScopeApplication(scopeApplication.get(0).getDefinedValue());
        }
        List<ProductOrderExtend> safetyPrecautions = list.stream()
                .filter(item -> "safetyPrecautions".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (safetyPrecautions.size() > 0) {
            productGeneralManualVO.setSafetyPrecautions(safetyPrecautions.get(0).getDefinedValue());
        }
        List<ProductOrderExtend> safetyPacket = list.stream()
                .filter(item -> "safetyPacket".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (safetyPacket.size() > 0) {
            productGeneralManualVO.setSafetyPacket(safetyPacket.get(0).getDefinedValue());
        }
        List<ProductOrderExtend> oilfieldCases = list.stream()
                .filter(item -> "oilfieldCases".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (oilfieldCases.size() > 0) {
            productGeneralManualVO.setOilfieldCases(oilfieldCases.get(0).getDefinedValue());
        }

        //附件上传
        ProductUploadVO productUploadVO = new ProductUploadVO();
        List<ProductOrderExtend> materialCodeFile = list.stream()
                .filter(item -> "materialCodeFile".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (materialCodeFile.size() > 0) {
            productUploadVO.setMaterialCodeFileList(materialCodeFile.get(0).getDefinedValue());
        }

        List<ProductOrderExtend> productSds = list.stream()
                .filter(item -> "productSds".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (productSds.size() > 0) {
            productUploadVO.setProductSdsList(productSds.get(0).getDefinedValue());
        }

        List<ProductOrderExtend> securityLabel = list.stream()
                .filter(item -> "securityLabel".equals(item.getDefinedKey())) // 筛选definedKey等于目标值的元素
                .collect(Collectors.toList());
        if (securityLabel.size() > 0) {
            productUploadVO.setSecurityLabelList(securityLabel.get(0).getDefinedValue());
        }

        productExtendVO.setTechnicalRequirementsVO(technicalRequirementsVO);
        productExtendVO.setProductUploadVO(productUploadVO);
        productExtendVO.setProductGeneralManualVO(productGeneralManualVO);
        return productExtendVO;
    }
}
