package com.cdkit.modules.plm.product.thirdparty.zwcad.dto;

import lombok.Data;

import java.util.List;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：BlockDataDTO
 * @Date：2024/4/25 09:34
 */
@Data
public class BlockDataDTO {
    private String blockId;
    private String blockName;
    private int blockRefHandle;
    private int layoutHandle;
    private List<BlockData> blockData;

    @Data
    public static class BlockData {
        private String refUvector;
        private String refVvector;
        private String refInsertPoint;
        private String refHandle;
        private String refName;
        private List<RefData> refData;
    }

    @Data
    public static class RefData {
        private String signId;
        private int type;
        private int attrHandle;
        private String attrName;
        private String attrValue;
        private String attrUvector;
        private String attrVvector;
        private String attrInsertPoint;
        private String signFileId;
        private String certFileId;
        private String certPassword;
        private String signTime;
    }
}
