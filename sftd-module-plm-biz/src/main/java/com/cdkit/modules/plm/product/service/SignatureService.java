package com.cdkit.modules.plm.product.service;

import com.cdkit.modules.plm.product.entity.Signature;
import com.baomidou.mybatisplus.extension.service.IService;

public interface SignatureService extends IService<Signature>{

    /**
     * 人员签名上传
     * @param userId
     * @param file
     */
    void upload(String username, String filePath) throws Exception;

    /**
     * 根据id编辑
     * @param id
     * @param file
     */
    void edit(String id, String filePath) throws Exception;
}
