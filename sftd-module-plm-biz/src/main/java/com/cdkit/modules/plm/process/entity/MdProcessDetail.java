package com.cdkit.modules.plm.process.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import java.util.Date;

import java.io.UnsupportedEncodingException;

/**
 * @Description: 工序定义明细
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Schema(description="md_process_detail对象", name="工序定义明细")
@Data
@TableName("md_process_detail")
public class MdProcessDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    private String id;
	/**工序id*/
    @Schema(description = "工序id")
    private String processId;
	/**序号*/
	@Excel(name = "序号", width = 15)
    @Schema(description = "序号")
    private Integer number;
    /**步骤编码*/
    @Excel(name = "步骤编码", width = 15)
    @Schema(description = "步骤编码")
    private String stepCode;
	/**步骤名称*/
	@Excel(name = "步骤名称", width = 15)
    @Schema(description = "步骤名称")
    private String stepName;
	/**步骤内容*/
	@Excel(name = "步骤内容", width = 15)
    @Schema(description = "步骤内容")
    private String stepContent;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
    /**pid*/
    @Excel(name = "pid", width = 15)
    @Schema(description = "pid")
    private String pid;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;



}
