package com.cdkit.modules.plm.product.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.md.api.IMdMaterialApi;
import com.cdkit.md.entity.MdMaterial;
import com.cdkit.modules.plm.product.entity.ProductPart;
import com.cdkit.modules.plm.product.mapper.ProductPartMapper;
import com.cdkit.modules.plm.product.service.IProductPartService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Description: product_part
 * @Author: mc
 * @Date:   2024-03-26
 * @Version: V1.0
 */
@Service
public class ProductPartServiceImpl extends ServiceImpl<ProductPartMapper, ProductPart> implements IProductPartService {
    @Resource
    private IMdMaterialApi mdMaterialApi;

    @Override
    public List<MdMaterial> queryFungibleMaterialCodes(String id) {
        List<MdMaterial> res = CollectionUtil.newArrayList();
        ProductPart productPart = this.getById(id);
        if (ObjectUtil.isNull(productPart)) {
            throw new CdkitCloudException("没有找到对应的boom信息");
        }
        if (StringUtils.isBlank(productPart.getFungibleMaterialCodes())) {
            return res;
        }
        String codes = productPart.getFungibleMaterialCodes();
        String[] split = codes.split(",");
        Map<String, MdMaterial> stringMdMaterialMap = mdMaterialApi.queryMdMaterialAll(codes);
        for (String s : split) {
            res.add(stringMdMaterialMap.get(s));
        }
        return res;
    }

    @Override
    public void editFungibleMaterialCodes(String id, String materialCodes) {
        this.update(new LambdaUpdateWrapper<ProductPart>().set(ProductPart::getFungibleMaterialCodes, materialCodes).eq(ProductPart::getId, id));
    }
}
