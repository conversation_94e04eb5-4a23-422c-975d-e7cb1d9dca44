package com.cdkit.modules.plm.process.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.process.vo.req.AddBaseKnowledgeVO;
import com.cdkit.modules.plm.process.vo.req.BaseKnowledgeVO;
import com.cdkit.modules.plm.process.vo.req.EditBaseKnowledgeVO;
import com.cdkit.modules.plm.process.vo.resp.BaseKnowledgeTreeVO;
import com.cdkit.modules.plm.process.entity.BaseKnowledge;
import com.cdkit.modules.plm.process.service.BaseKnowledgeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@Tag(name = "工艺知识库")
@RestController
@RequestMapping("baseKnowledge")
@Slf4j
@AllArgsConstructor
public class BaseKnowledgeController extends CdkitController<BaseKnowledge, BaseKnowledgeService> {

    /**
     * 添加
     *
     * @param addBaseKnowledgeVO
     * @return
     */
    @Operation(summary = "添加工艺知识库", description = "添加工艺知识库")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody @Valid AddBaseKnowledgeVO addBaseKnowledgeVO) {
        service.add(addBaseKnowledgeVO);
        return Result.OK("操作成功！");
    }

    /**
     * 编辑
     *
     * @param baseKnowledgeVO
     * @return
     */
    @Operation(summary = "编辑工艺知识库", description = "编辑工艺知识库")
    @PostMapping(value = "/edit")
    public Result<String> edit(@RequestBody @Validated EditBaseKnowledgeVO baseKnowledgeVO) {
        service.edit(baseKnowledgeVO);
        return Result.OK("操作成功！");
    }


    /**
     * 通过id删除工艺知识库
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id删除工艺知识库", description = "通过id删除工艺知识库")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        service.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询工艺知识库
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id查询工艺知识库", description = "通过id查询工艺知识库")
    @GetMapping(value = "queryById")
    public Result<BaseKnowledgeVO> queryById(@RequestParam(name = "id") String id) {
        BaseKnowledge baseKnowledge = service.getById(id);
        if (baseKnowledge == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(BeanUtil.copyProperties(baseKnowledge, BaseKnowledgeVO.class));
    }

    @Operation(summary = "工艺知识库树形", description = "根据根节点id查询树形")
    @GetMapping(value = "/tree")
    public Result<List<BaseKnowledgeTreeVO>> tree(@RequestParam(required = false) List<String> rootIds) {
        List<BaseKnowledgeTreeVO> baseKnowledgeTreeVOS = service.tree(rootIds);
        return Result.OK(baseKnowledgeTreeVOS);
    }


}
