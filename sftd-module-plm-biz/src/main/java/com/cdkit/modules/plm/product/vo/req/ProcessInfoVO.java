package com.cdkit.modules.plm.product.vo.req;

import com.cdkit.modules.plm.process.entity.ProcessMaterialBinding;
import com.cdkit.modules.plm.process.vo.resp.RespProcessDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/11
 */
@Data
public class ProcessInfoVO {
    /**
     * 工序ID
     */
    @Schema(description = "工序ID")
    private String processId;
    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    private String processName;
    /**
     * 产出物ID
     */
    @Schema(description = "产出物Id")
    private String outputProductId;

    /**
     * 产出物物料编码
     */
    @Schema(description = "产出物物料编码")
    private String materialCode;
    /**
     * 产能
     */
    @Schema(description = "产能")
    private BigDecimal workHour;
    /**
     * 工序绑定物料ID
     */
    @Schema(description = "工序绑定物料ID")
    private List<String> productIdList;

    /**
     * 工序绑定物料ID
     */
    @Schema(description = "工序绑定配方明细ID")
    private List<String> formulationDetailIdList;
    /**工序对应步骤*/
    @Schema(description = "工序对应步骤")
    private List<RespProcessDetailVO> processDetail;

    private List<ProcessMaterialBinding> bindingList;
}
