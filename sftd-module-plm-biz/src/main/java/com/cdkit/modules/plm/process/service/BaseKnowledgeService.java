package com.cdkit.modules.plm.process.service;

import com.cdkit.modules.plm.process.vo.req.AddBaseKnowledgeVO;
import com.cdkit.modules.plm.process.vo.req.EditBaseKnowledgeVO;
import com.cdkit.modules.plm.process.vo.resp.BaseKnowledgeTreeVO;
import com.cdkit.modules.plm.process.entity.BaseKnowledge;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface BaseKnowledgeService extends IService<BaseKnowledge>{


    void add(AddBaseKnowledgeVO addBaseKnowledgeVO);

    void edit(EditBaseKnowledgeVO baseKnowledgeVO);

    List<BaseKnowledgeTreeVO> tree(List<String> rootIds);
}
