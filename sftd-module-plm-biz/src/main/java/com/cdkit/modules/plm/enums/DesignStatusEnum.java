package com.cdkit.modules.plm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设计状态
 *
 * @Author：<PERSON><PERSON> yang
 * @name：ProductTreeType
 * @Date：2024/3/29 11:01
 */
@AllArgsConstructor
@Getter
public enum DesignStatusEnum {

    DESIGNING(1, "设计中"),
    PUBLISHED(2, "已发布"),
    OUTBOUND(3, "已出库"),
    INBOUND(4, "已入库"),
    FILING(5, "已归档"),
    APPROVAL(6, "审批中"),
    NPPUBLISHED(7, "取消发布");


    private final Integer code;
    private final String desc;

}
