package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：GetBorrowInfoQueryVO
 * @Date：2024/4/16 18:00
 */
@Data
@Schema(description ="获取借用信息查询vo")
public class GetBorrowInfoQueryVO {
    @Schema(description ="搜索关键词")
    private String queryCondition;
    @Schema(description ="零部件id")
    private String productPartId;
    @Schema(description ="页码")
    private Integer pageNo = 1;
    @Schema(description ="页容量")
    private Integer pageSize = 10;
    private String sourceId;
}
