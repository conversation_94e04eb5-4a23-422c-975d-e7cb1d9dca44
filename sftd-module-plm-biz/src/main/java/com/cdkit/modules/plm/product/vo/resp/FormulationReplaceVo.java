package com.cdkit.modules.plm.product.vo.resp;

import com.baomidou.mybatisplus.annotation.*;
import com.cdkit.common.aspect.annotation.Dict;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * @Description: product_formulation
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
@Schema(description="product_formulation对象")
@Data
public class FormulationReplaceVo implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
    @Schema(description = "主键")
    private String id;
	/**物料编码*/
    @Schema(description = "物料编码")
    private String materialCode;
	/**配方编码*/
    @Schema(description = "配方编码")
    private String formulaCode;
    @Schema(description = "配方版本")
    private String formulaVersion;
    /**配方类别1-正式配方 2-临时配方*/
    @Schema(description = "配方类别1-正式配方 2-临时配方")
    @Dict(dicCode="formula_type")
    private String formulaType;
    @Schema(description = "物料描述")
    private String remark;
    @Schema(description = "配方中所有替代料")
    private String fungibleMaterialCode;

    @Schema(description = "booid")
    private String boomId;

    //配方id
    private String productFormulationId;


}
