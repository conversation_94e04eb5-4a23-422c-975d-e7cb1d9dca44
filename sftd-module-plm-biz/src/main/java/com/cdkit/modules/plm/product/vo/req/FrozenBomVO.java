package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：FrozenBOMVO
 * @Date：2024/3/28 14:40
 */
@Data
@Schema(description = "固化bom请求vo")
public class FrozenBomVO {
    @Schema(description = "节点id")
    @NotBlank(message = "节点id不能为空")
    private String id;

    @Schema(description = "bom名称")
    @NotBlank(message = "bom名称不能为空")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "节点类型 1:产品树 2工艺树")
    private Integer nodeType;
}
