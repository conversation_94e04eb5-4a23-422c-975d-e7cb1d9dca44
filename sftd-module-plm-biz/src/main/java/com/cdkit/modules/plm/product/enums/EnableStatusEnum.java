package com.cdkit.modules.plm.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/11
 */
@Getter
@AllArgsConstructor
public enum EnableStatusEnum {
    /**
     * 启用
     */
    PASS(0, "启用"),
    /**
     * 停用
     */
    FAIL(1, "停用");

    private final int code;
    private final String desc;

    public static String findByDes(int code) {
        for (EnableStatusEnum item : EnableStatusEnum.values()) {
            if (item.getCode()==code) {
                return item.getDesc();
            }
        }
        return null;
    }
}
