package com.cdkit.modules.plm.config;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import com.cdkit.modules.plm.enums.FactoryEnum;
import com.cdkit.modules.plm.process.service.IFactoryStrategy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/10/31
 */
@Component
@Slf4j
public class StrategyByFactory {
    /**
     * 1、批量注入实现了 IFactoryStrategy 的Bean。
     * 2、用bean的数量当做map的大小
     */
    @Resource
    private final Map<String, IFactoryStrategy> BUSINESS_FACTORY = new ConcurrentHashMap<>(FactoryEnum.values().length);

    /** 根据类获取不同的
     * @param type type
     * @return {@link IFactoryStrategy}
     **/
    public IFactoryStrategy getBusinessMap(Integer type){
        log.info("IFactoryStrategy接收类型:{}", type);
        Assert.notNull(type, "类型不能为空！");
        String beanName = FactoryEnum.getBeanName(type);
        log.info("IFactoryStrategy:{}", JSON.toJSONString(BUSINESS_FACTORY.get(beanName).getClass()));
        IFactoryStrategy factoryStrategy = BUSINESS_FACTORY.get(beanName);
        return Objects.isNull(factoryStrategy) ? BUSINESS_FACTORY.get(FactoryEnum.DEFAULT_BEAN_NAME) : factoryStrategy;
    }

}
