package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.modules.plm.process.vo.resp.MdProcessRemarkPage;
import com.cdkit.modules.plm.product.mapper.ProductPartMapper;
import com.google.common.base.Joiner;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import com.cdkit.common.config.TenantContext;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.md.api.IMdMaterialApi;
import com.cdkit.md.api.IMdProductionLineApi;
import com.cdkit.md.entity.MdMaterial;
import com.cdkit.md.entity.MdProductionLine;
import com.cdkit.md.entity.RespProcessVO;
import com.cdkit.modules.plm.common.Constants;
import com.cdkit.modules.plm.enums.CodeTypeEnum;
import com.cdkit.modules.plm.process.entity.*;
import com.cdkit.modules.plm.process.mapper.*;
import com.cdkit.modules.plm.process.service.IMdProcessRouteDetailService;
import com.cdkit.modules.plm.process.service.IMdProcessRouteService;
import com.cdkit.modules.plm.process.service.IMdProcessService;
import com.cdkit.modules.plm.process.service.IProductProcessRouteService;
import com.cdkit.modules.plm.process.vo.req.ReqProductProcessRouteVo;
import com.cdkit.modules.plm.process.vo.resp.MdBoomPage;
import com.cdkit.modules.plm.product.entity.ProductPart;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.IProductPartService;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.util.DidUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 工序定义
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Service
public class MdProcessServiceImpl extends ServiceImpl<MdProcessMapper, MdProcess> implements IMdProcessService {

	@Autowired
	private MdProcessMapper mdProcessMapper;
	@Autowired
	private MdProcessDetailMapper mdProcessDetailMapper;
	@Autowired
	private IProductTreeService productTreeService;
	@Autowired
	private IMdProcessRouteService processRouteService;
	@Autowired
	private MdProcessRouteMapper mdProcessRouteMapper;
	@Autowired
	private MdProcessRouteDetailMapper processRouteDetailMapper;
	@Autowired
	private IProductProcessRouteService productProcessRouteService;
	@Autowired
	private ProductProcessRouteMapper productProcessRouteMapper;
	@Resource
	private IMdProductionLineApi mdProductionLineApi;
	@Resource
	private IMdMaterialApi mdMaterialApi;
	@Resource
	private IProductPartService productPartService;
	@Resource
	private ProductProcessDetailMapper productProcessDetailMapper;
	@Resource
	private IMdProcessRouteDetailService mdProcessRouteDetailService;
	@Resource
	private ProductPartMapper productPartMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(MdProcess mdProcess, List<MdProcessDetail> mdProcessDetailList) {
		mdProcess.setProcessCode(DidUtil.getDid(CodeTypeEnum.PROCESS_CODE.toString(), MapUtil.empty()));
		mdProcessMapper.insert(mdProcess);
		if(mdProcessDetailList!=null && mdProcessDetailList.size()>0) {
			for(MdProcessDetail entity:mdProcessDetailList) {
				//外键设置
				entity.setProcessId(mdProcess.getId());
				entity.setStepCode(DidUtil.getDid(CodeTypeEnum.STEP_CODE.toString(), MapUtil.empty()));
				mdProcessDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(MdProcess mdProcess,List<MdProcessDetail> mdProcessDetailList) {
		mdProcessMapper.updateById(mdProcess);

		//1.先删除子表数据
		mdProcessDetailMapper.deleteByMainId(mdProcess.getId());

		//2.子表数据重新插入
		if(mdProcessDetailList!=null && mdProcessDetailList.size()>0) {
			for(MdProcessDetail entity:mdProcessDetailList) {
				//外键设置
				entity.setProcessId(mdProcess.getId());
				if (StringUtils.isEmpty(entity.getStepCode())) {
					entity.setStepCode(DidUtil.getDid(CodeTypeEnum.STEP_CODE.toString(), MapUtil.empty()));
				}
				mdProcessDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		mdProcessDetailMapper.deleteByMainId(id);
		mdProcessMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			mdProcessDetailMapper.deleteByMainId(id.toString());
			mdProcessMapper.deleteById(id);
		}
	}

	/**
	 * 通过code查询
	 *
	 * @param processCode 工序编码
	 * @return 结果返回
	 */
	@Override
	public MdProcess queryByProcessCode(String processCode) {

		return mdProcessMapper.selectOne(new LambdaQueryWrapper<MdProcess>().eq(MdProcess::getProcessCode, processCode));
	}

	@Override
	@Transactional
	public void autoInsert(String flagText,String workShopId) {
		//1-1 根据flagText 查出工艺boom 信息
		List<ProductTree> list = productTreeService.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getSysOrgCode, flagText)
				.eq(ProductTree::getNodeType, 2));
		List<RespProcessVO> addList = new ArrayList<>();
		if (null!=list&&list.size()>0){
			for (ProductTree pt : list) {
				//生成工序
				MdProcess mdProcess = new MdProcess();
				mdProcess.setProcessCode(DidUtil.getDid(CodeTypeEnum.PROCESS_CODE_CZY.toString(), MapUtil.empty()));
				mdProcess.setProcessName(pt.getCode() + "工序");
				mdProcess.setOutputUnitId("kg");
				mdProcess.setProcessTypeId("1844932301307965442");
				mdProcess.setProcessKind("END_PROCESS");
				mdProcess.setOutsourced(0);
				mdProcess.setScheduling(1);
				mdProcess.setTraceable(0);
				mdProcess.setUseStatus(String.valueOf(1));
				mdProcess.setIsKey(0);
				mdProcess.setRemark(flagText);
				mdProcess.setTenantId(1001);
				mdProcess.setDelFlag(0);
				mdProcessMapper.insert(mdProcess);
				//生成工艺路线
				MdProcessRoute mdProcessRoute = new MdProcessRoute();
				mdProcessRoute.setProcessRouteCode(DidUtil.getDid(CodeTypeEnum.PROCESS_ROUTE_CZY_CODE.toString(), MapUtil.empty()));
				mdProcessRoute.setUseStatus("1");
				mdProcessRoute.setProcessRouteName(pt.getCode() + "工艺路线");
				mdProcessRoute.setUpdateBy(flagText);
				mdProcessRoute.setDelFlag(0);
				mdProcessRoute.setTenantId(1001);
				mdProcessRouteMapper.insert(mdProcessRoute);
				//生成工序工艺路线关联表数据
				MdProcessRouteDetail mdProcessRouteDetail = new MdProcessRouteDetail();
				mdProcessRouteDetail.setProcessRouteId(mdProcessRoute.getId());
				mdProcessRouteDetail.setProcessNumber("1");
				mdProcessRouteDetail.setProcessId(mdProcess.getId());
				//TODO 查询workshopid
				mdProcessRouteDetail.setWorkshopId(workShopId);
				mdProcessRouteDetail.setRemark(flagText);
				mdProcessRouteDetail.setTenantId(1001);
				mdProcessRouteDetail.setDelFlag(0);
				mdProcessRouteDetail.setReportRatio(BigDecimal.valueOf(100));
				//todo 产线id
				MdProductionLine mdProductionLine = new MdProductionLine();
				mdProductionLine.setRemark(flagText);
				mdProductionLine.setDelFlag(0);
				List<MdProductionLine> mdProductionLines = mdProductionLineApi.queryMdProductionLineList(mdProductionLine);
				mdProcess.setLineId(mdProductionLines.get(0).getId());
				mdProcessRouteDetail.setDeviceId(mdProductionLines.get(0).getId());
				processRouteDetailMapper.insert(mdProcessRouteDetail);
				//生成product_process_route,product_process_detail表
				ProductProcessRoute productProcessRoute = new ProductProcessRoute();
				productProcessRoute.setProductId(pt.getId());
				productProcessRoute.setCurrProductId(pt.getId());
				productProcessRoute.setMaterialCode(pt.getMaterialCode());
				productProcessRoute.setProcessRouteId(mdProcessRoute.getId());
				productProcessRoute.setProcessId(mdProcess.getId());
				productProcessRoute.setProcessCode(mdProcess.getProcessCode());
				productProcessRoute.setProcessNumber("1");
				productProcessRoute.setProcessRouteDetailId(mdProcessRouteDetail.getId());
				productProcessRoute.setIsNew(1);
				productProcessRoute.setTenantId(1001);
				productProcessRoute.setDelFlag(0);
				productProcessRoute.setUpdateBy(flagText);
				productProcessRouteMapper.insert(productProcessRoute);

				RespProcessVO respProcessVO = new RespProcessVO();
				respProcessVO.setId(mdProcess.getId());
				respProcessVO.setProcessId(mdProcess.getId());
				respProcessVO.setProcessName(mdProcess.getProcessName());
				respProcessVO.setProcessCode(mdProcess.getProcessCode());
				respProcessVO.setProcessContent(mdProcess.getLineId());
				addList.add(respProcessVO);
			}
            //主数据生成生产单元工序关联
			if(null!=addList&&addList.size()>0){
				mdProductionLineApi.bindLineProcess(addList);
			}
		}
	}

	@Override
	@Transactional
	public void createData(String boomId, List<MdBoomPage> list) {
		List<RespProcessVO> addList = new ArrayList<>();
		Map<String, ProductTree> treeMap = new HashMap<>();
		Map<String, MdProcessRoute> rountMap = new HashMap<>();
		List<ReqProductProcessRouteVo> routeList = new ArrayList<>();
		String tenant = TenantContext.getTenant();
		for(MdBoomPage boomPage:list){
			if(StringUtils.isBlank(boomPage.getMaterialCode())){
				throw new CdkitCloudException(boomPage.getMaterialName()+"该物料编码不能为空！");
			}
		}
		//查询涉及物料
		List<String> collect = list.stream().map(MdBoomPage::getMaterialCode).collect(Collectors.toList());
		Map<String, MdMaterial> stringMdMaterialMap = mdMaterialApi.queryMdMaterialAll(Joiner.on(",").join(collect));
		//1-1生成product_tree 数据
		//1-1-1 查询节点信息
		ProductTree parentTree = productTreeService.getOne(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getId, boomId));


		List<MdProcessRouteDetail> mdProcessRouteDetailsAll = new ArrayList<>();
		List<MdProcessRouteDetail> mdProcessRouteDetails = new ArrayList<>();
		int index=1;
		for(int k = 0 ; k < list.size(); k ++ ){
			MdBoomPage boomPage = list.get(k);
			ProductTree productTree = new ProductTree();
			productTree.setName(boomPage.getMaterialName());
			if (k == list.size() - 1) {
				Collections.reverse(mdProcessRouteDetails);
				for(int h=0;h<mdProcessRouteDetails.size();h++){
					MdProcessRouteDetail mdProcessRouteDetail = mdProcessRouteDetails.get(h);
					mdProcessRouteDetail.setProcessNumber(String.valueOf(h+1));
				}
				mdProcessRouteDetailsAll.addAll(mdProcessRouteDetails);
			}
			if(null==boomPage.getPName()){
				index=1;
				Collections.reverse(mdProcessRouteDetails);
				for(int h=0;h<mdProcessRouteDetails.size();h++){
					MdProcessRouteDetail mdProcessRouteDetail = mdProcessRouteDetails.get(h);
					mdProcessRouteDetail.setProcessNumber(String.valueOf(h+1));
				}
				mdProcessRouteDetailsAll.addAll(mdProcessRouteDetails);
				mdProcessRouteDetails = new ArrayList<>();
				//获取list
				Integer level = parentTree.getLevel() == null ? 1 : parentTree.getLevel() + 1;
				productTree.setLevel(level);
				productTree.setType(3);
				productTree.setCode(boomPage.getMaterialCode());
				productTree.setMaterialCode(boomPage.getMaterialCode());
				productTree.setDescription(boomPage.getMaterialName());
				productTree.setPid(parentTree.getId());
				productTree.setConverted(1);
				productTree.setNodeType(2);
				productTree.setDelFlag(0);
				productTree.setRemark(tenant+"main"+parentTree.getName());
				productTree.setTenantId(tenant);
				productTreeService.save(productTree);
				productTree.setPath(parentTree.getPath() + productTree.getId() + Constants.PATH_SPILT);
				productTree.setSort(productTree.getId());
				productTreeService.updateById(productTree);
				treeMap.put(boomPage.getMaterialCode(),productTree);
				//1-2生成product_part 数据
				ProductPart productPart = new ProductPart();
				productPart.setId(productTree.getId());
				productPart.setMaterialCode(boomPage.getMaterialCode());
				productPart.setMaterialName(boomPage.getMaterialName());
				productPart.setPartType("FINISHED");
				if("可销售".equals(boomPage.getManufactureType())){
					productPart.setManufactureType("marketable");
				}else if("可购买".equals(boomPage.getManufactureType())){
					productPart.setManufactureType("purchasable");
				}else if("可自制".equals(boomPage.getManufactureType())){
					productPart.setManufactureType("selfmade");
				}else if("可外委".equals(boomPage.getManufactureType())){
					productPart.setManufactureType("outsourced");
				}
				productPart.setHandleType(tenant+"main"+parentTree.getName());
				productPart.setAssembleQuantity(BigDecimal.valueOf(1));
				productPart.setStandardQuantity(BigDecimal.valueOf(1));
				MdMaterial mdMaterial = stringMdMaterialMap.get(boomPage.getMaterialCode());
				if(null==mdMaterial){
					throw new CdkitCloudException(boomPage.getMaterialName()+"该物料不存在，请维护物料主数据信息！");
				}
				productPart.setAssembleUnit(mdMaterial.getBasicUnitId());
				productPart.setTenantId(Integer.valueOf(tenant));
				productPartService.save(productPart);


				/*//生成工序
				MdProcess mdProcess = new MdProcess();
				mdProcess.setProcessCode(boomPage.getProcessCode());
				mdProcess.setProcessName(boomPage.getProcessName());
				mdProcess.setOutsourced(0);
				mdProcess.setScheduling(1);
				mdProcess.setTraceable(0);
				mdProcess.setUseStatus(String.valueOf(1));
				mdProcess.setIsKey(0);
				mdProcess.setRemark(tenant+"main"+parentTree.getName());
				mdProcess.setTenantId(Integer.valueOf(tenant));
				mdProcess.setDelFlag(0);
				mdProcessMapper.insert(mdProcess);
				//生成工序明细
				if(null!=boomPage.getMdProcessDetailList()&&boomPage.getMdProcessDetailList().size()>0){
					List<MdStepVO> mdProcessDetailList = boomPage.getMdProcessDetailList();
					int i=1;
					for(MdStepVO vo:mdProcessDetailList){
						MdProcessDetail mdProcessDetail = new MdProcessDetail();
						mdProcessDetail.setProcessId(mdProcess.getId());
						mdProcessDetail.setNumber(i);
						mdProcessDetail.setStepCode(DidUtil.getDid(CodeTypeEnum.STEP_CODE.toString(), MapUtil.empty()));
						mdProcessDetail.setStepName(vo.getStepName());
						mdProcessDetail.setStepContent(vo.getStepContext());
						mdProcessDetail.setRemark(tenant+"main"+parentTree.getName());
						mdProcessDetail.setTenantId(Integer.valueOf(tenant));
						mdProcessDetail.setDelFlag(0);
						mdProcessDetailMapper.insert(mdProcessDetail);
						i++;
					}
				}
				//生成工艺路线
				MdProcessRoute mdProcessRoute = new MdProcessRoute();
				mdProcessRoute.setProcessRouteCode(DidUtil.getDid(CodeTypeEnum.PROCESS_ROUTE_CZY_CODE.toString(), MapUtil.empty()));
				mdProcessRoute.setUseStatus("1");
				mdProcessRoute.setProcessRouteName(boomPage.getProcessCode() + "工艺路线");
				mdProcessRoute.setUpdateBy(tenant+"main"+parentTree.getName());
				mdProcessRoute.setDelFlag(0);
				mdProcessRouteMapper.insert(mdProcessRoute);
				rountMap.put(productTree.getName(),mdProcessRoute);
				//生成工序工艺路线关联表数据
				MdProcessRouteDetail mdProcessRouteDetail = new MdProcessRouteDetail();
				mdProcessRouteDetail.setProcessRouteId(mdProcessRoute.getId());
				//mdProcessRouteDetail.setProcessNumber(String.valueOf(index));
				mdProcessRouteDetail.setProcessId(mdProcess.getId());
				//TODO 查询workshopid
				//mdProcessRouteDetail.setWorkshopId(workShopId);
				mdProcessRouteDetail.setRemark(tenant+"main"+parentTree.getName());
				mdProcessRouteDetail.setDelFlag(0);
				mdProcessRouteDetail.setReportRatio(BigDecimal.valueOf(100));
				//todo 产线id
				MdProductionLine mdProductionLine = new MdProductionLine();
				mdProductionLine.setRemark(parentTree.getRemark());
				mdProductionLine.setDelFlag(0);
				List<MdProductionLine> mdProductionLines = mdProductionLineApi.queryMdProductionLineList(mdProductionLine);
				mdProcess.setLineId(mdProductionLines.get(0).getId());
				mdProcessRouteDetail.setDeviceId(mdProductionLines.get(0).getId());
				mdProcessRouteDetails.add(mdProcessRouteDetail);


				RespProcessVO respProcessVO = new RespProcessVO();
				respProcessVO.setId(mdProcess.getId());
				respProcessVO.setProcessId(mdProcess.getId());
				respProcessVO.setProcessName(mdProcess.getProcessName());
				respProcessVO.setProcessCode(mdProcess.getProcessCode());
				respProcessVO.setProcessContent(mdProcess.getLineId());
				addList.add(respProcessVO);
				ReqProductProcessRouteVo reqProductProcessRouteVo = new ReqProductProcessRouteVo();
				reqProductProcessRouteVo.setCurrProductId(productTree.getId());
				reqProductProcessRouteVo.setProcessRouteId(mdProcessRoute.getId());
				routeList.add(reqProductProcessRouteVo);*/
			}else{
				if(StringUtils.isBlank(boomPage.getMaterialCode())){
					throw new CdkitCloudException(boomPage.getMaterialName()+"父级物料编码不能为空！");
				}
				if(null==treeMap.get(boomPage.getPCode())){
					throw new CdkitCloudException(boomPage.getMaterialName()+"该物料无父级物料数据,请维护！");
				}
				index=index+1;
				ProductTree productTree1 = treeMap.get(boomPage.getPCode());
				Integer level = productTree1.getLevel() == null ? 1 : productTree1.getLevel() + 1;
				productTree.setType(4);
				productTree.setLevel(level);
				productTree.setCode(boomPage.getMaterialCode()+ RandomUtil.randomNumbers(4));
				productTree.setMaterialCode(boomPage.getMaterialCode());
				productTree.setDescription(boomPage.getMaterialName());
				productTree.setPid(treeMap.get(boomPage.getPCode()).getId());
				productTree.setConverted(1);
				productTree.setNodeType(2);
				productTree.setDelFlag(0);
				productTree.setRemark(treeMap.get(boomPage.getPCode()).getCode()+"main1");
				productTree.setTenantId(tenant);
				productTreeService.save(productTree);
				productTree.setPath(treeMap.get(boomPage.getPCode()).getPath() + productTree.getId() + Constants.PATH_SPILT);
				productTree.setSort(productTree.getId());
				productTreeService.updateById(productTree);
				treeMap.put(boomPage.getMaterialCode(),productTree);

				//1-2生成product_part 数据
				ProductPart productPart = new ProductPart();
				productPart.setId(productTree.getId());
				productPart.setMaterialCode(boomPage.getMaterialCode());
				productPart.setMaterialName(boomPage.getMaterialName());
				productPart.setPartType("FINISHED");
				if("可销售".equals(boomPage.getManufactureType())){
					productPart.setManufactureType("marketable");
				}else if("可购买".equals(boomPage.getManufactureType())){
					productPart.setManufactureType("purchasable");
				}else if("可自制".equals(boomPage.getManufactureType())){
					productPart.setManufactureType("selfmade");
				}else if("可外委".equals(boomPage.getManufactureType())){
					productPart.setManufactureType("outsourced");
				}
				productPart.setHandleType(treeMap.get(boomPage.getPCode()).getCode()+"main1");
				productPart.setAssembleQuantity(boomPage.getAssembleQuantity());
				productPart.setStandardQuantity(boomPage.getAssembleQuantity());
				productPart.setWeight(String.valueOf(boomPage.getAssembleQuantity()));
				MdMaterial mdMaterial = stringMdMaterialMap.get(boomPage.getMaterialCode());
				if(null==mdMaterial){
					throw new CdkitCloudException(boomPage.getMaterialName()+"该物料不存在，请维护物料主数据信息！");
				}
				productPart.setAssembleUnit(mdMaterial.getBasicUnitId());
				productPart.setTenantId(Integer.valueOf(tenant));
				productPartService.save(productPart);

				//生成工序绑定关系
		/*		if(null!=boomPage.getProcessName()){
					MdProcess mdProcess = new MdProcess();
					mdProcess.setProcessCode(boomPage.getProcessCode());
					mdProcess.setProcessName(boomPage.getProcessName());
					mdProcess.setOutsourced(0);
					mdProcess.setScheduling(1);
					mdProcess.setTraceable(0);
					mdProcess.setUseStatus(String.valueOf(1));
					mdProcess.setIsKey(0);
					mdProcess.setRemark(tenant+"main"+parentTree.getName());
					mdProcess.setTenantId(Integer.valueOf(tenant));
					mdProcess.setDelFlag(0);
					mdProcessMapper.insert(mdProcess);
					//生成工序明细
					if(null!=boomPage.getMdProcessDetailList()&&boomPage.getMdProcessDetailList().size()>0){
						List<MdStepVO> mdProcessDetailList = boomPage.getMdProcessDetailList();
						int i=1;
						for(MdStepVO vo:mdProcessDetailList){
							MdProcessDetail mdProcessDetail = new MdProcessDetail();
							mdProcessDetail.setProcessId(mdProcess.getId());
							mdProcessDetail.setNumber(i);
							mdProcessDetail.setStepCode(DidUtil.getDid(CodeTypeEnum.STEP_CODE.toString(), MapUtil.empty()));
							mdProcessDetail.setStepName(vo.getStepName());
							mdProcessDetail.setStepContent(vo.getStepContext());
							mdProcessDetail.setRemark(tenant+"main"+parentTree.getName());
							mdProcessDetail.setTenantId(Integer.valueOf(tenant));
							mdProcessDetail.setDelFlag(0);
							mdProcessDetailMapper.insert(mdProcessDetail);

							i++;
						}
					}
					//生成工序工艺路线关联表数据
					MdProcessRouteDetail mdProcessRouteDetail = new MdProcessRouteDetail();
					//mdProcessRouteDetail.setProcessRouteId(rountMap.get(boomPage.getPName()).getId());
					//mdProcessRouteDetail.setProcessNumber(String.valueOf(index));
					mdProcessRouteDetail.setProcessId(mdProcess.getId());
					//TODO 查询workshopid
					//mdProcessRouteDetail.setWorkshopId(workShopId);
					mdProcessRouteDetail.setRemark(tenant+"main"+parentTree.getName());
					mdProcessRouteDetail.setDelFlag(0);
					mdProcessRouteDetail.setReportRatio(BigDecimal.valueOf(100));
					//todo 产线id
					MdProductionLine mdProductionLine = new MdProductionLine();
					mdProductionLine.setRemark(parentTree.getRemark());
					mdProductionLine.setDelFlag(0);
					List<MdProductionLine> mdProductionLines = mdProductionLineApi.queryMdProductionLineList(mdProductionLine);
					mdProcess.setLineId(mdProductionLines.get(0).getId());
					mdProcessRouteDetail.setDeviceId(mdProductionLines.get(0).getId());
					mdProcessRouteDetails.add(mdProcessRouteDetail);
					//生成product_process_route,product_process_detail表

					RespProcessVO respProcessVO = new RespProcessVO();
					respProcessVO.setId(mdProcess.getId());
					respProcessVO.setProcessId(mdProcess.getId());
					respProcessVO.setProcessName(mdProcess.getProcessName());
					respProcessVO.setProcessCode(mdProcess.getProcessCode());
					respProcessVO.setProcessContent(mdProcess.getLineId());
					addList.add(respProcessVO);
				}*/
			}
		}


		/*if(	mdProcessRouteDetails.size()>0){
			mdProcessRouteDetailService.saveBatch(mdProcessRouteDetailsAll);
		}
		//工序生产单元绑定
		if(null!=addList&&addList.size()>0){
			mdProductionLineApi.bindLineProcess(addList);
		}
		//导入工艺路线
		if(routeList.size()>0){
			for(ReqProductProcessRouteVo vo:routeList){
				//productProcessRouteService.importProcessRoute(vo);
			}
		}*/
	}

	@Override
	public void importProcessRemark(List<MdProcessRemarkPage> list) {
		List<ProductPart> updateList = new ArrayList<>();
		Map<String, List<MdProcessRemarkPage>> groupedMap = new HashMap<>();

		// 分组逻辑
		for (MdProcessRemarkPage item : list) {
			if (item.getFormulaCode() != null) {
				groupedMap.computeIfAbsent(item.getFormulaCode(), k -> new ArrayList<>())
						.add(item);
			}
		}

		// 拼接 remark
		Map<String, String> resultMap = new HashMap<>();
		for (Map.Entry<String, List<MdProcessRemarkPage>> entry : groupedMap.entrySet()) {
			String formulaCode = entry.getKey();
			List<MdProcessRemarkPage> groupItems = entry.getValue();

			String combinedRemark = groupItems.stream()
					.map(MdProcessRemarkPage::getRemark)
					.filter(remark -> remark != null && !remark.isEmpty())
					.collect(Collectors.joining("\n"));

			resultMap.put(formulaCode, combinedRemark);
		}
        //绑定插入工序指导
		resultMap.forEach((formulaCode, combinedRemark) -> {
			//查询配方对应的part
			List<ProductTree> ptTreeList = productTreeService.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getNodeType, 2)
					.eq(ProductTree::getCode, formulaCode));
			if(!ptTreeList.isEmpty()){
				ProductPart productPart = productPartMapper.selectById(ptTreeList.get(0).getId());
				productPart.setProcessRemark(combinedRemark);
				updateList.add(productPart);
			}

		});
		if(!updateList.isEmpty()){
			productPartService.updateBatchById(updateList);
		}
		System.out.println(resultMap);
	}
}
