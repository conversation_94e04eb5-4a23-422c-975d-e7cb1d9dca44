package com.cdkit.modules.plm.process.mapper;

import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.plm.process.entity.ProcessExperimentTerm;

/**
 * @Description: 工艺实验项
 * @Author: cdkit-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
public interface ProcessExperimentTermMapper extends BaseMapper<ProcessExperimentTerm> {

    /**
     * 删除实验项
     * @param refKey 关联Key
     */
    void deleteByRefKey(@Param("refKey") String refKey);
}
