package com.cdkit.modules.plm.product.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.UnsupportedEncodingException;

/**
 * @Description: product_formulation_detail
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
@Schema(description="product_formulation_detail对象")
@Data
@TableName("product_formulation_detail")
public class ProductFormulationDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**产品配方id*/
    @Schema(description = "产品配方id")
    private String productFormulationId;
	/**项次*/
	@Excel(name = "项次", width = 15)
    @Schema(description = "项次")
    private Integer itemNumber;
	/**bommid*/
	@Excel(name = "bommid", width = 15)
    @Schema(description = "bommid")
    private String boomId;
	/**物料编码*/
	@Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;
	/**用量分子*/
	@Excel(name = "用量分子", width = 15)
    @Schema(description = "用量分子")
    private java.math.BigDecimal molecule;
	/**用量分母*/
	@Excel(name = "用量分母", width = 15)
    @Schema(description = "用量分母")
    private java.math.BigDecimal denominator;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;


    @Schema(description = "物料名称")
    private String materialName;

    @Schema(description = "物料类型名称")
    @TableField(exist = false)
    private String materialTypeName;

    @Schema(description = "单位")
    @TableField(exist = false)
    private String unitName;


    @Schema(description = "替代物")
    @TableField(exist = false)
    private String fungibleMaterialCodes;

    @Schema(description = "替代物名称")
    private String fungibleMaterialName;

    /**历史boomID*/
    @Excel(name = "historyBoomId", width = 15)
    @Schema(description = "historyBoomId")
    private String historyBoomId;



}
