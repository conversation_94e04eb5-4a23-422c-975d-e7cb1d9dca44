package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.md.api.IMdMaterialApi;
import com.cdkit.md.entity.MdMaterialExtend;
import com.cdkit.md.entity.MdMaterialPage;
import com.cdkit.modules.plm.enums.*;
import com.cdkit.modules.plm.operation.service.IOperationLogService;
import com.cdkit.modules.plm.process.service.IProcessBomService;
import com.cdkit.modules.plm.process.vo.req.ReqProductBomConvertVO;
import com.cdkit.modules.plm.product.dto.ProductListDTO;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.ProductPart;
import com.cdkit.modules.plm.product.entity.ProductPartExtend;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.mapper.ProductPartMapper;
import com.cdkit.modules.plm.product.mapper.ProductTreeMapper;
import com.cdkit.modules.plm.product.service.IProductPartExtendService;
import com.cdkit.modules.plm.product.service.IProductPartService;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.AddProductPartVO;
import lombok.extern.slf4j.Slf4j;
import com.cdkit.modules.plm.util.DidUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工艺BOM相关业务实现
 *
 * <AUTHOR>
 * @date 2024/3/28
 */
@Service
@Slf4j
public class ProcessBomServiceImpl implements IProcessBomService {
    @Resource
    private ProductTreeMapper productTreeMapper;
    @Resource @Lazy
    private IProductTreeService productTreeService;
    @Resource
    private ProductPartMapper productPartMapper;
    @Resource
    private IOperationLogService operationLogUtil;
    @Resource
    private IProductPartExtendService productPartExtendService;
    @Resource
    private IProductPartService productPartService;
    @Resource
    private IMdMaterialApi mdMaterialApi;

    /**
     * 通过产品编码或代号查询产品bom
     *
     * @param code 产品编码或代号
     * @return 产品BOM树
     */
    @Override
    public ProductTreeDTO getProductBomList(String code) {
        ProductTree productTree = productTreeMapper.selectOne(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, code)
                .eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode()));
        if (productTree == null) {
            throw new CdkitCloudException("您将要转换的节点在设计BOM中不存在或已删除！");
        }
        // 查询子节点
        List<ProductTreeDTO> tree = productTreeService.tree(Collections.singletonList(productTree.getId()), NodeTypeEnum.PRODUCT_TREE,null);
        if (tree.size() > 0) {
            ProductTreeDTO productBomList = tree.get(0);
            // 默认零件一致
            setDiff(productBomList, BomConvertDiffEnum.WHITE.getCode().toString());
            ProductTreeDTO processBomList = getProcessBomList(productBomList.getCode());
            if (processBomList == null) {
                // 未找到相同零件
                setDiff(productBomList, BomConvertDiffEnum.ORANGE.getCode().toString());
                return productBomList;
            }
            // 零件相同，属性不同
            // 产品树结构转换，便于比较
            List<ProductListDTO> productListDTO = new ArrayList<>();
            treeToList(productBomList, productListDTO);
            richList(productListDTO);
            // 工艺树结构转换
            List<ProductListDTO> processListDTO = new ArrayList<>();
            treeToList(processBomList, processListDTO);
            richList(processListDTO);

            List<String> difference = productListDTO.stream()
                    .filter(element -> !processListDTO.contains(element)).map(ProductListDTO::getCode)
                    .collect(Collectors.toList());
            setDiff(productBomList, BomConvertDiffEnum.YELLOW.getCode().toString(), difference);

            // 判断是否是新增对象
            List<String> firstCodeList = productListDTO.stream().map(ProductListDTO::getCode).collect(Collectors.toList());
            List<String> secondCodeList = processListDTO.stream().map(ProductListDTO::getCode).collect(Collectors.toList());
            List<String> add = firstCodeList.stream().filter(element -> !secondCodeList.contains(element)).collect(Collectors.toList());
            setDiff(productBomList, BomConvertDiffEnum.ORANGE.getCode().toString(), add);

            // TODO 图纸有差异
            return productBomList;

        }

        return null;
    }

    /**
     * 产品树结构转为列表结构
     *
     * @param productTreeDTO 树结构
     * @param productListDTO 列表结构
     */
    private void treeToList(ProductTreeDTO productTreeDTO, List<ProductListDTO> productListDTO) {
        ProductListDTO dto = new ProductListDTO();
        dto.setId(productTreeDTO.getId());
        dto.setName(productTreeDTO.getName());
        dto.setCode(productTreeDTO.getCode());
        productListDTO.add(dto);
        if (productTreeDTO.getChildren() != null && productTreeDTO.getChildren().size() > 0) {
            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
                treeToList(item, productListDTO);
            }
        }
    }

    /**
     * 零件详细属性赋值
     *
     * @param productListDTO 列表结构
     */
    private void richList(List<ProductListDTO> productListDTO) {
        List<String> collect = productListDTO.stream().map(ProductListDTO::getId).collect(Collectors.toList());
        List<ProductPart> productParts = productPartMapper.selectBatchIds(collect);
        productListDTO = productListDTO.stream()
                .map(product -> productParts.stream()
                        .filter(part -> product.getId().equals(part.getId()))
                        .findFirst()
                        .map(part -> {
                            product.setAssembleQuantity(part.getAssembleQuantity());
                            product.setAssembleSort(part.getAssembleSort());
                            product.setAssembleUnit(part.getAssembleUnit());
                            product.setHandleType(part.getHandleType());
                            product.setManufactureType(part.getManufactureType());
                            product.setMaterialCode(part.getMaterialCode());
                            product.setMaterialName(part.getMaterialName());
                            product.setPartType(part.getPartType());
                            product.setSpecs(part.getSpecs());
                            product.setStructType(part.getStructType());
                            product.setWeight(part.getWeight());
                            return product;
                        }).orElse(null)
                ).collect(Collectors.toList());
    }

    /**
     * 零件详细属性对比
     *
     * @param productTreeDTO 树结构
     * @param diff           对比标识
     * @param difference     零件属性不一致
     */
    private void setDiff(ProductTreeDTO productTreeDTO, String diff, List<String> difference) {

        if (difference.contains(productTreeDTO.getCode())) {
            productTreeDTO.setDiff(diff);
        }
        if (productTreeDTO.getChildren() != null && productTreeDTO.getChildren().size() > 0) {
            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
                setDiff(item, diff, difference);
            }
        }
    }

    /***
     * 设置零件对比标识
     * @param productTreeDTO 产品树
     * @param diff 对比标识
     */
    private void setDiff(ProductTreeDTO productTreeDTO, String diff) {
        productTreeDTO.setDiff(diff);
        if (productTreeDTO.getChildren() != null && productTreeDTO.getChildren().size() > 0) {
            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
                setDiff(item, diff);
            }
        }
    }

    /**
     * 通过产品编码或代号查询产品bom
     *
     * @param code 产品编码或代号
     * @return 工艺BOM树
     */
    @Override
    public ProductTreeDTO getProcessBomList(String code) {
        ProductTree productTree = productTreeMapper.selectOne(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, code)
                .eq(ProductTree::getConverted, 1));
        if (productTree == null) {
            return null;
        }
        List<ProductTreeDTO> tree = productTreeService.tree(Collections.singletonList(productTree.getId()), NodeTypeEnum.PROCESS_TREE,null);
        if (tree.size() > 0) {
            return tree.get(0);
        }
        return null;
    }

    /**
     * 完成BOM转换
     *
     * @param reqProductBomConvertVO 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishBomConvert(List<ReqProductBomConvertVO> reqProductBomConvertVO) {
        log.info("完成BOM转换请求参数：{}", JSON.toJSONString(reqProductBomConvertVO));
        if (reqProductBomConvertVO.size() == 0) {
            throw new CdkitCloudException("请选择设计BOM下的节点进行转换");
        }
        // 需要排序，父级节点优先创建
        List<String> collect = reqProductBomConvertVO.stream().map(ReqProductBomConvertVO::getId).collect(Collectors.toList());
        List<ProductTree> listOrder = productTreeMapper.selectList(new LambdaQueryWrapper<ProductTree>().in(ProductTree::getId, collect).orderByAsc(ProductTree::getPath));

        for (ProductTree item : listOrder) {
            List<ProductTree> list = productTreeMapper.selectList(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, item.getCode())
                    .eq(ProductTree::getNodeType, NodeTypeEnum.PROCESS_TREE.getCode()));
            if (list.size() == 0) {
                // 新增节点
                // 查询产品树父级节点编码

                ProductTree productTree1 = productTreeMapper.selectById(item.getId());
                ProductTree parentNode = productTreeMapper.selectOne(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getId, item.getPid()));
                if (parentNode == null) {
                    throw new CdkitCloudException("转换【" + productTree1.getName() + "】时，没有选择父级节点，请转换父级节点");
                }
                if (ProductTreeTypeEnum.PRODUCT_KIND.getCode().equals(parentNode.getType())) {
                    // 如果父级节点是产品，则此节点新增到产品节点下
                    productTree1.setId(String.valueOf(IdWorker.getId()));
                    productTree1.setPid(parentNode.getId());
                    productTree1.setConverted(1);
                    productTree1.setConvertedFromId(item.getId());
                    productTree1.setNodeType(NodeTypeEnum.PROCESS_TREE.getCode());
                    productTreeMapper.insert(productTree1);

                } else {
                    // 如果父级节点是总装或者零件，则需查询工艺树中父级节点信息
                    ProductTree processParentNode = productTreeMapper.selectOne(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, parentNode.getCode())
                            .eq(ProductTree::getNodeType, NodeTypeEnum.PROCESS_TREE.getCode()));
                    if (processParentNode == null) {
                        throw new CdkitCloudException("【" + productTree1.getName() + "】无父级节点，请转换父级节点");
                    }
                    productTree1.setId(String.valueOf(IdWorker.getId()));
                    productTree1.setPid(processParentNode.getId());
                    productTree1.setConverted(1);
                    productTree1.setConvertedFromId(item.getId());
                    productTree1.setNodeType(NodeTypeEnum.PROCESS_TREE.getCode());
                    productTreeMapper.insert(productTree1);
                }

                // 新增节点下的图纸
                // 查询要转换的节点下是否有图纸
                LambdaQueryWrapper<ProductTree> wrapper = new LambdaQueryWrapper<ProductTree>()
                        .eq(ProductTree::getPid, item.getId())
                        .eq(ProductTree::getType, ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode())
                        .eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode());

                List<ProductTree> productTrees = productTreeMapper.selectList(wrapper);
                for (ProductTree productTree : productTrees) {
                    String convertedFromId = productTree.getId();
                    productTree.setId(String.valueOf(IdWorker.getId()));
                    productTree.setPid(productTree1.getId());
                    productTree.setConverted(1);
                    productTree.setConvertedFromId(convertedFromId);
                    productTree.setNodeType(NodeTypeEnum.PROCESS_TREE.getCode());
                    productTreeMapper.insert(productTree);
                }

                ProductPart productPart = productPartMapper.selectById(item.getId());
                productPart.setId(productTree1.getId());
                productPartMapper.insertOrUpdate(productPart);

                saveProductPartExtend(productTree1.getId());

            } else {
                // 更新节点
                // 查询产品树节点信息
                if (list.size() == 1) {
                    // 产品不是借用件，借用件暂不处理
                    ProductTree productTree = list.get(0);
                    ProductTree productTree1 = productTreeMapper.selectById(item.getId());
                    BeanUtils.copyProperties(productTree1, productTree);
                    productTree.setConvertedFromId(item.getId());
                    productTreeMapper.updateById(productTree);

                    ProductPart productPart = productPartMapper.selectById(item.getId());
                    productPart.setId(productTree.getId());
                    productPartMapper.insertOrUpdate(productPart);
                    saveProductPartExtend(productTree.getId());

                    // 新增节点下的图纸
                    // 查询要转换的节点下是否有图纸
                    LambdaQueryWrapper<ProductTree> wrapper = new LambdaQueryWrapper<ProductTree>()
                            .eq(ProductTree::getPid, item.getId())
                            .eq(ProductTree::getType, ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode())
                            .eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode());

                    List<ProductTree> productTrees = productTreeMapper.selectList(wrapper);
                    if (productTrees.size() > 0) {
                        // 如果父级节点是总装或者零件，则需查询工艺树中父级节点信息
                        ProductTree processParentNode = productTreeMapper.selectOne(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, item.getCode())
                                .eq(ProductTree::getNodeType, NodeTypeEnum.PROCESS_TREE.getCode()));
                        if (processParentNode == null) {
                            throw new CdkitCloudException("【" + productTree1.getName() + "】无父级节点，请转换父级节点");
                        }
                        productTreeMapper.deleteDesignDocument(processParentNode.getId(), ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode(), NodeTypeEnum.PROCESS_TREE.getCode());
                        for (ProductTree pt : productTrees) {
                            String convertedFromId = pt.getId();
                            pt.setId(String.valueOf(IdWorker.getId()));
                            pt.setPid(processParentNode.getId());
                            pt.setConverted(1);
                            pt.setConvertedFromId(convertedFromId);
                            pt.setNodeType(NodeTypeEnum.PROCESS_TREE.getCode());
                            productTreeMapper.insert(pt);
                        }
                    }

                }

            }
        }
        List<String> nameList = listOrder.stream().map(ProductTree::getName).collect(Collectors.toList());
        operationLogUtil.insertOperationLog(OperationTypeEnum.BOM_CHANGE.getType(), String.join(",", nameList));
    }

    /**
     * 保存扩展属性
     * @param id 产品ID
     */
    private void saveProductPartExtend(String id) {
        // 查询扩展属性
        List<ProductPartExtend> extendProperties = productPartExtendService.list(new LambdaQueryWrapper<ProductPartExtend>().eq(ProductPartExtend::getProductId, id));
        if (extendProperties != null && extendProperties.size() > 0) {
            //写入扩展属性
            productPartExtendService.remove(new LambdaQueryWrapper<ProductPartExtend>().eq(ProductPartExtend::getProductId, id));
            List<ProductPartExtend> productPartExtends = extendProperties.stream().map(extendProperty -> {
                ProductPartExtend productExtendProperty = new ProductPartExtend();
                productExtendProperty.setProductId(id);
                productExtendProperty.setDefinedKey(extendProperty.getDefinedKey());
                productExtendProperty.setDefinedValue(extendProperty.getDefinedValue());
                return productExtendProperty;
            }).collect(Collectors.toList());
            productPartExtendService.saveBatch(productPartExtends);
        }
    }

    /**
     * 查询产品bom与工艺bom属性差异
     *
     * @param code 产品编码或代号
     * @return 返回结果
     */
    @Override
    public HashMap<String, Object> getDiffProperty(String code) {
        // 产品BOM属性
        ProductListDTO productListDTO = new ProductListDTO();
        ProductTree productTree = productTreeMapper.selectOne(new LambdaQueryWrapper<ProductTree>()
                .eq(ProductTree::getCode, code)
                .eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode()));
        ProductPart productPart = productPartMapper.selectById(productTree.getId());
        BeanUtils.copyProperties(productTree, productListDTO);
        BeanUtils.copyProperties(productPart, productListDTO);

        HashMap<String, Object> map = new HashMap<>(16);
        map.put("source", productListDTO);

        // 工艺BOM属性
        ProductListDTO processListDTO = new ProductListDTO();
        ProductTree processTree = productTreeMapper.selectOne(new LambdaQueryWrapper<ProductTree>()
                .eq(ProductTree::getCode, code)
                .eq(ProductTree::getNodeType, NodeTypeEnum.PROCESS_TREE.getCode()));
        ProductPart processPart = productPartMapper.selectById(productTree.getId());
        BeanUtils.copyProperties(processTree, processListDTO);
        BeanUtils.copyProperties(processPart, processListDTO);
        map.put("target", productListDTO);
        return map;
    }

    /**
     * 新增工艺bom总装/零件
     *
     * @param addProductPartVO 零件属性信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addProcessBomPart(AddProductPartVO addProductPartVO) {
        log.info("添加工艺总装/零部件，req：{}", addProductPartVO);
        ProductTree parentNode = productTreeService.getById(addProductPartVO.getPid());
        // 当前层级
        Integer level = parentNode.getLevel() == null ? 1 : parentNode.getLevel() + 1;
        Integer parentNodeType = parentNode.getType();
        //当前节点类型
        Integer currNodeType;
        //父级类型必须是产品种类、总装、零部件
        if (parentNodeType.equals(ProductTreeTypeEnum.PRODUCT_KIND.getCode())) {
            currNodeType = ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode();
        } else if (parentNodeType.equals(ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode()) || parentNodeType.equals(ProductTreeTypeEnum.PRODUCT_PART.getCode())) {
            currNodeType = ProductTreeTypeEnum.PRODUCT_PART.getCode();
        } else {
            throw new CdkitCloudException("添加了错误的节点类型");
        }
        //校验名称不能重复
        if (productTreeMapper
                .selectCount(new LambdaQueryWrapper<ProductTree>()
                        .eq(ProductTree::getName, addProductPartVO.getName())
                        .eq(ProductTree::getPid, parentNode.getId())) > 0) {
            log.error("物料名称不能重复，req：{}", addProductPartVO);
            throw new CdkitCloudException(String.format("物料名称不能重复，名称：%s", addProductPartVO.getName()));
        }
        MdMaterialPage mdMaterialPage = mdMaterialApi.queryMdMaterial(addProductPartVO.getMaterialCode());
        if (StringUtils.isEmpty(addProductPartVO.getName())) {
            addProductPartVO.setName(mdMaterialPage.getMaterialName());
        }

        addProductPartVO.setMaterialName(mdMaterialPage.getMaterialName());
        //产品编码写入后不能变更
        String code = DidUtil.getDid(CodeTypeEnum.PRODUCT_CODE.toString(), MapUtil.empty());
        addProductPartVO.setCode(code);
        if (StrUtil.isNotBlank(code) && productTreeMapper.selectCount(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, code)) > 0) {
            throw new CdkitCloudException(String.format("物料名称不能重复，名称：%s", addProductPartVO.getName()));
        }
        ProductTree productTree = BeanUtil.copyProperties(addProductPartVO, ProductTree.class);
        if (ProductTreeTypeEnum.PRODUCT_KIND.getCode().equals(parentNode.getType())
                || ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode().equals(parentNode.getType())
                || ProductTreeTypeEnum.PRODUCT_PART.getCode().equals(parentNode.getType())) {
            productTree.setLevel(level);
        }
        productTree.setType(currNodeType);
        String pid = productTree.getPid();
        //先保存获取id，用于拼接path
        productTreeService.save(productTree);
        ProductTree byId = productTreeService.getById(pid);
        if (byId == null) {
            throw new CdkitCloudException(String.format("找不到指定的父级产品种类，pid：%s", pid));
        }
        productTree.setPath(byId.getPath() + productTree.getId() + ",");
        productTree.setSort(productTree.getId());
        productTree.setConverted(1);
        productTree.setNodeType(NodeTypeEnum.PROCESS_TREE.getCode());
        productTreeService.updateById(productTree);
        ProductPart productPartParent = productPartService.getById(byId.getId());
        ProductPart productPart = BeanUtil.copyProperties(addProductPartVO, ProductPart.class);
        productPart.setId(productTree.getId());
        BigDecimal standardQuantity = productPartParent == null || productPartParent.getStandardQuantity() == null ? new BigDecimal(1) : productPartParent.getStandardQuantity();
        BigDecimal assembleQuantity = productPart.getAssembleQuantity() == null ? new BigDecimal(1) : productPart.getAssembleQuantity();
        productPart.setStandardQuantity(standardQuantity.multiply(assembleQuantity));
        productPartMapper.insert(productPart);

        //写入扩展属性
        List<MdMaterialExtend> extendProperties = addProductPartVO.getProductPartExtendList();
        if (extendProperties != null && extendProperties.size() > 0) {
            List<ProductPartExtend> productPartExtends = extendProperties.stream().map(extendPropertyVO -> {
                ProductPartExtend productExtendProperty = new ProductPartExtend();
                productExtendProperty.setProductId(productTree.getId());
                productExtendProperty.setDefinedKey(extendPropertyVO.getDefinedKey());
                productExtendProperty.setDefinedValue(extendPropertyVO.getDefinedValue());
                return productExtendProperty;
            }).collect(Collectors.toList());
            productPartExtendService.saveBatch(productPartExtends);
        }

        //写入定制属性
        List<MdMaterialExtend> customizedProperties = addProductPartVO.getProductPartCustomizedList();
        if (customizedProperties != null && customizedProperties.size() > 0) {
            List<ProductPartExtend> productPartExtends = customizedProperties.stream().map(extendPropertyVO -> {
                ProductPartExtend productExtendProperty = new ProductPartExtend();
                BeanUtil.copyProperties(extendPropertyVO, productExtendProperty);
                productExtendProperty.setId(null);
                productExtendProperty.setProductId(productTree.getId());
                productExtendProperty.setDefinedType("2");
                return productExtendProperty;
            }).collect(Collectors.toList());
            productPartExtendService.saveBatch(productPartExtends);
        }


        operationLogUtil.insertOperationLog(OperationTypeEnum.ADD.getType() + "工艺总装/零部件", addProductPartVO.getName());
        return productTree.getId();
    }


}
