package com.cdkit.modules.plm.product.thirdparty.zwcad.dto;

import lombok.Data;

import java.util.List;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：SignSaveReqVO
 * @Date：2024/4/25 09:54
 */
@Data
public class SignSaveReqVO {
    private String dwgPath = this.docId + ".dwg";
    private String docId;
    private List<SignReqVO> data;

    @Data
    public static class SignReqVO {
        private String attrValue;
        private Integer attrHandle;
        private String signId;
        private Integer type;
        private String attrName;
        private Integer blockRefHandle;
        private String signFileId;
    }
}
