package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.map.MapUtil;
import com.cdkit.modules.plm.enums.CodeTypeEnum;
import com.cdkit.modules.plm.process.entity.MdProcessRoute;
import com.cdkit.modules.plm.process.entity.MdProcessRouteDetail;
import com.cdkit.modules.plm.process.mapper.MdProcessRouteDetailMapper;
import com.cdkit.modules.plm.process.mapper.MdProcessRouteMapper;
import com.cdkit.modules.plm.process.service.IMdProcessRouteService;
import com.cdkit.modules.plm.util.DidUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 工艺路线主数据
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Service
public class MdProcessRouteServiceImpl extends ServiceImpl<MdProcessRouteMapper, MdProcessRoute> implements IMdProcessRouteService {

	@Autowired
	private MdProcessRouteMapper mdProcessRouteMapper;
	@Autowired
	private MdProcessRouteDetailMapper mdProcessRouteDetailMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(MdProcessRoute mdProcessRoute, List<MdProcessRouteDetail> mdProcessRouteDetailList) {
		mdProcessRoute.setProcessRouteCode(DidUtil.getDid(CodeTypeEnum.PROCESS_ROUTE_CODE.toString(), MapUtil.empty()));
		mdProcessRouteMapper.insert(mdProcessRoute);
		if(mdProcessRouteDetailList!=null && mdProcessRouteDetailList.size()>0) {
			for(MdProcessRouteDetail entity:mdProcessRouteDetailList) {
				//外键设置
				entity.setProcessRouteId(mdProcessRoute.getId());
				mdProcessRouteDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(MdProcessRoute mdProcessRoute,List<MdProcessRouteDetail> mdProcessRouteDetailList) {
		mdProcessRouteMapper.updateById(mdProcessRoute);

		//1.先删除子表数据
		mdProcessRouteDetailMapper.deleteByMainId(mdProcessRoute.getId());

		//2.子表数据重新插入
		if(mdProcessRouteDetailList!=null && mdProcessRouteDetailList.size()>0) {
			for(MdProcessRouteDetail entity:mdProcessRouteDetailList) {
				//外键设置
				entity.setProcessRouteId(mdProcessRoute.getId());
				mdProcessRouteDetailMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		mdProcessRouteDetailMapper.deleteByMainId(id);
		mdProcessRouteMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			mdProcessRouteDetailMapper.deleteByMainId(id.toString());
			mdProcessRouteMapper.deleteById(id);
		}
	}

}
