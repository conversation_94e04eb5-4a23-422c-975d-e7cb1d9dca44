package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.cdkit.modules.plm.common.Constants;
import com.cdkit.modules.plm.enums.OperationTypeEnum;
import com.cdkit.modules.plm.operation.service.IOperationLogService;
import com.cdkit.modules.plm.process.vo.req.AddBaseKnowledgeVO;
import com.cdkit.modules.plm.process.vo.req.EditBaseKnowledgeVO;
import com.cdkit.modules.plm.process.vo.resp.BaseKnowledgeTreeVO;
import com.cdkit.modules.plm.process.entity.BaseKnowledge;
import com.cdkit.modules.plm.process.mapper.BaseKnowledgeMapper;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdkit.modules.plm.process.service.BaseKnowledgeService;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BaseKnowledgeServiceImpl extends ServiceImpl<BaseKnowledgeMapper, BaseKnowledge> implements BaseKnowledgeService {
    @Resource
    private IOperationLogService operationLogUtil;
    @Override
    public void add(AddBaseKnowledgeVO addBaseKnowledgeVO) {
        BaseKnowledge baseKnowledge = BeanUtil.copyProperties(addBaseKnowledgeVO, BaseKnowledge.class);
        if (StrUtil.isBlank(baseKnowledge.getPid())) {
            baseKnowledge.setPid(Constants.ROOT_PID);
        }
        this.save(baseKnowledge);

        operationLogUtil.insertOperationLog(OperationTypeEnum.ADD.getType() + "工艺知识库", addBaseKnowledgeVO.getName());
    }

    @Override
    public void edit(EditBaseKnowledgeVO baseKnowledgeVO) {
        BaseKnowledge baseKnowledge = this.getById(baseKnowledgeVO.getId());
        BeanUtil.copyProperties(baseKnowledgeVO, baseKnowledge);
        this.updateById(baseKnowledge);
    }

    @Override
    public List<BaseKnowledgeTreeVO> tree(List<String> rootIds) {
        List<BaseKnowledge> baseKnowledgeList = this.list();
        if (CollUtil.isEmpty(baseKnowledgeList)) {
            return Collections.emptyList();
        }
        //所有节点
        List<BaseKnowledgeTreeVO> allNodes = baseKnowledgeList.stream().map(x -> BeanUtil.copyProperties(x, BaseKnowledgeTreeVO.class)).collect(Collectors.toList());
        //根节点
        List<BaseKnowledgeTreeVO> rootNodes;
        if (CollUtil.isEmpty(rootIds)) {
            rootNodes = allNodes.stream().filter(x -> x.getPid().equals(Constants.ROOT_PID)).collect(Collectors.toList());
        } else {
            rootNodes = allNodes.stream().filter(x -> rootIds.contains(x.getId())).collect(Collectors.toList());
        }
        generateTree(rootNodes, allNodes);
        return rootNodes;
    }

    private List<BaseKnowledgeTreeVO> generateTree(List<BaseKnowledgeTreeVO> rootNodes, List<BaseKnowledgeTreeVO> allNodes) {
        return rootNodes.stream().peek(node -> node.setChildren(getChild(node, allNodes)))
                .sorted(Comparator.comparing(BaseKnowledgeTreeVO::getCreateBy))
                .collect(Collectors.toList());
    }

    private List<BaseKnowledgeTreeVO> getChild(BaseKnowledgeTreeVO root, List<BaseKnowledgeTreeVO> all) {
        return all.stream()
                .filter(node -> node.getPid().equals(root.getId()))
                .peek(node -> node.setChildren(getChild(node, all)))
                .collect(Collectors.toList());
    }
}
