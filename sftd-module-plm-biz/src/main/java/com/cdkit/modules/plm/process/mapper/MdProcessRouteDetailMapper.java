package com.cdkit.modules.plm.process.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.plm.process.entity.MdProcessRouteDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 工艺路线明细
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
public interface MdProcessRouteDetailMapper extends BaseMapper<MdProcessRouteDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<MdProcessRouteDetail>
   */
	public List<MdProcessRouteDetail> selectByMainId(@Param("mainId") String mainId);
}
