package com.cdkit.modules.plm.process.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;

import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 工艺零件
 * @Author: mc
 * @Date:   2024-04-01
 * @Version: V1.0
 */
@Data
@TableName("process_part")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="process_part对象", name="process_part")
public class ProcessPart implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
	/**关联产品树下总装或零件ID*/
	@Excel(name = "关联产品树下总装或零件ID", width = 15)
    @Schema(description = "关联产品树下总装或零件ID")
    private String productId;
	/**工艺名称*/
	@Excel(name = "工艺名称", width = 15)
    @Schema(description = "工艺名称")
    private String processName;
	/**工艺类型*/
	@Excel(name = "工艺类型", width = 15)
    @Schema(description = "工艺类型")
    private String processType;
	/**工艺状态 1设计 2待发布 3发布 4归档*/
	@Excel(name = "工艺状态 1设计 2待发布 3发布 4归档", width = 15)
    @Schema(description = "工艺状态 1设计 2待发布 3发布 4归档")
    private String processStatus;
	/**不同工艺类型的属性值(键值对表示)*/
	@Excel(name = "不同工艺类型的属性值(键值对表示)", width = 15)
    @Schema(description = "不同工艺类型的属性值(键值对表示)")
    private String proccesProperty;
	/**版本号，a、b、c、d增长*/
	@Excel(name = "版本号，a、b、c、d增长", width = 15)
    @Schema(description = "版本号，a、b、c、d增长")
    private String version;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
	/**创建人*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "创建时间")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private String tenantId;
}
