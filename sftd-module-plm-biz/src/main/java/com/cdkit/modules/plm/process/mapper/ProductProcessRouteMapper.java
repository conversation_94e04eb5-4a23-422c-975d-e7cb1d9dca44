package com.cdkit.modules.plm.process.mapper;

import org.apache.ibatis.annotations.Param;
import com.cdkit.modules.plm.process.entity.ProductProcessRoute;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.plm.process.vo.resp.ScheduleProcessRouteResp;

import java.util.List;

/**
 * @Description: 产品工艺路线表
 * @Author: cdkit-boot
 * @Date:   2024-08-05
 * @Version: V1.0
 */
public interface ProductProcessRouteMapper extends BaseMapper<ProductProcessRoute> {

    /**
     * 删除当前节点工艺路线
     * @param currProductId 当前节点ID
     */
    void removeByCurProductId(@Param("currProductId") String currProductId);

    /**
     * 删除当前节点工艺路线
     * @param formulationId 配方ID
     */
    void removeByFormulationId(@Param("formulationId") String formulationId);

    List<ScheduleProcessRouteResp> selectProcessRouteDetails(List<String> processCodeList);
}
