package com.cdkit.modules.plm.process.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkit.common.aspect.annotation.Dict;
import com.cdkitframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 工序分类主数据
 * @Author: mc
 * @Date:   2024-07-26
 * @Version: V1.0
 */
@Data
@TableName("md_process_type")
@Schema(description="md_process_type对象", name="工序分类主数据")
public class MdProcessType implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**分类编码*/
	@Excel(name = "分类编码", width = 15)
    @Schema(description = "分类编码")
    private String typeCode;
	/**分类名称*/
	@Excel(name = "分类名称", width = 15)
    @Schema(description = "分类名称")
    private String typeName;
	/**所有层级父节点*/
	@Excel(name = "所有层级父节点", width = 15)
    @Schema(description = "所有层级父节点")
    private String ancestors;
	/**是否排产*/
	@Excel(name = "是否排产", width = 15)
    @Schema(description = "是否排产")
    private Integer scheduling;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
	/**父级节点*/
	@Excel(name = "父级节点", width = 15)
    @Schema(description = "父级节点")
    private String pid;
	/**是否有子节点*/
	@Excel(name = "是否有子节点", width = 15, dicCode = "yn")
	@Dict(dicCode = "yn")
    @Schema(description = "是否有子节点")
    private String hasChild;
}
