package com.cdkit.modules.plm.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import com.cdkit.common.util.MinioUtil;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * @Author：<PERSON><PERSON> yang
 * @name：MinioConfig
 * @Date：2024/4/5 16:55
 */
@Configuration
@ConfigurationProperties(prefix = "sftd.minio")
@Data
public class CustomMinioConfig {
    private String minioUrl;
    private String minioName;
    private String minioPass;
    private String bucketName;
    private String backendUrl;
    //private String url;
    //private String accessKey;
    //private String secretKey;
    //
    //@Bean
    //public MinioClient minioClient() {
    //    return MinioClient.builder().endpoint(url).credentials(accessKey, secretKey).build();
    //}

    @PostConstruct
    public void init() {
        MinioUtil.setMinioUrl(minioUrl);
        MinioUtil.setMinioName(minioName);
        MinioUtil.setMinioPass(minioPass);
        MinioUtil.setBucketName(bucketName);
        MinioUtil.setBackendUrl(backendUrl);
    }
}
