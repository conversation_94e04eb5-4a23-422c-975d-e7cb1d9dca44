package com.cdkit.modules.plm.process.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.entity.ProcessTemplate;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTemplateAddVO;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTemplateEditVO;
import com.cdkit.modules.plm.process.vo.resp.RespProcessTemplateVO;

import java.util.List;

/**
 * @Description: 工艺模板相关接口
 * @Author: mc
 * @Date:   2024-04-01
 * @Version: V1.0
 */
public interface IProcessTemplateService extends IService<ProcessTemplate> {
    /**
     * 按工艺类型查询工艺模板
     *
     * @return 返回结果
     */
    List<RespProcessTemplateVO> listGroupByType();

    /**
     * 根据ID删除工艺模板
     * @param id 模板ID
     */
    void deleteById(String id);

    /**
     * 批量删除工艺模板
     * @param ids 主键ID
     */
    void deleteByIds(List<String> ids);

    /**
     * 新增工艺模板
     * @param reqProcessTemplateAddVO 工艺模板
     */
    void add(ReqProcessTemplateAddVO reqProcessTemplateAddVO);

    /**
     * 修改工艺模板
     * @param reqProcessTemplateEditVO 工艺模板
     */
    void edit(ReqProcessTemplateEditVO reqProcessTemplateEditVO);
}
