package com.cdkit.modules.plm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：CadParseKey
 * @Date：2024/4/28 16:05
 */
@AllArgsConstructor
@Getter
public enum CadParseKey {

    MATERIAL_CODE("物料编码_机打信息"),
    MATERIAL_NAME("材料编码_机打信息"),
    PART_NAME("零部件名称_机打信息"),
    PART_CODE("零部件编码_机打信息"),
    PROJECT_NAME("项目名称_机打信息"),
    WEIGHT("重量_机打信息"),
    RADIO("比例_机打信息"),
    SJWCRQ("设计完成日期_机打信息"),
    SHWCRQ("审核完成日期_机打信息"),
    GYWCRQ("工艺完成日期_机打信息"),
    PZWCRQ("批准完成日期_机打信息"),
    SJQM("设计_DWG签名"),
    SHQM("审核_DWG签名"),
    GYQM("工艺_DWG签名"),
    PZQM("批准_DWG签名"),
    SJWF("设计"),
    SHWF("审核"),
    GYWF("工艺"),
    PZWF("批准");
    private String CAD_KEY;
}
