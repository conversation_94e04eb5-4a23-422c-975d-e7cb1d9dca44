package com.cdkit.modules.plm.statistics.vo.resp;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/5/11
 */
@Data
@Schema(description ="首页-概览")
public class RespIndexSummaryVo {
    @Schema(description = "分类数量")
    private Long categoryNum;

    @Schema(description = "产品数量")
    private Long productNum;

    @Schema(description = "总装数量")
    private Long assemblyNum;

    @Schema(description = "零件数量")
    private Long partNum;

    @Schema(description = "工艺卡片数量")
    private Long cardNum;

    @Schema(description = "出库数量")
    private Long deliveryNum;
}
