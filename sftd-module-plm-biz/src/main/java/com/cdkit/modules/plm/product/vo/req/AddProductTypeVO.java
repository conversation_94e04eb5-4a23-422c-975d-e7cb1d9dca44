package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：ProductNodeVO
 * @Date：2024/3/26 15:44
 */
@Data
@Schema(description = "新增产品大类vo")
public class AddProductTypeVO {

    @NotBlank(message = "产品大类不能为空")
    @Schema(description = "名称")
    private String name;

    @NotBlank(message = "产品种类代号不能为空")
    @Schema(description = "编码/代号")
    private String code;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "父id，新增时候没指定父id则传null")
    private String pid;

}
