package com.cdkit.modules.plm.process.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.entity.MdProcessDetail;

import java.util.List;

/**
 * @Description: 工序定义明细
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
public interface IMdProcessDetailService extends IService<MdProcessDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<MdProcessDetail>
	 */
	public List<MdProcessDetail> selectByMainId(String mainId);
}
