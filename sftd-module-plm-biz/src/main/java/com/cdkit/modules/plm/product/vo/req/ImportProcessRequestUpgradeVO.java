package com.cdkit.modules.plm.product.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/24
 */
@Data
public class ImportProcessRequestUpgradeVO {
    /**
     * 原产成品ID(bomId）
     */
    @Schema(description = "原产成品ID(bomId)")
    private String productIdOrigin;
    /**
     * 产成品ID(bomId）
     */
    @Schema(description = "产成品ID(bomId)")
    private String productId;

    /**
     * 配方ID
     */
    @Schema(description = "配方ID")
    private String productFormulationId;

    /**
     * 配方ID
     */
    @Schema(description = "原配方ID")
    private String productFormulationIdOrigin;
}
