package com.cdkit.modules.plm.product.dto;

import lombok.Data;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：WriteSignDTO
 * @Date：2024/4/25 15:37
 */
@Data
public class WriteSignDTO {
    /**
     * 提前在图框里制作好的key，例如：设计-机打信息、审核_签章图，后续需要在工作流程里配置，根据这个key去properties里的attrName去匹配找到对应的属性数据
     */
    private String signKey;
    /**
     * 1: 签名（此时对应username）   2: 机打信息（文本）  3：dwg签名
     */
    private Integer signType;
    /**
     * 用户名，传了用户名代表写入该用户对应的签章图
     * 机打信息
     */
    private String value;
}
