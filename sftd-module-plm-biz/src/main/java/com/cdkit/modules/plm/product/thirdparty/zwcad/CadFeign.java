package com.cdkit.modules.plm.product.thirdparty.zwcad;

import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import com.cdkit.modules.plm.product.thirdparty.zwcad.dto.*;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Author：zhao yang
 * @name：CadService
 * @Date：2024/4/25 08:41
 */
@FeignClient(name = "zwcad", url = "${thirdparty.zwcad.url:}", fallbackFactory = CadFeign.CadFallbackFactory.class)
public interface CadFeign {
    /**
     * 图纸上传
     *
     * @param folderId
     * @param file
     * @return
     */
    @PostMapping(value = "/api/file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    CadResultDTO<UploadRespDTO> upload(
            @RequestParam("folderId") String folderId,
            @RequestPart("file") MultipartFile file);

    /**
     * 签名上传
     *
     * @param dwgPath
     * @param docId
     * @param uploadId 当前的时间戳（秒值）
     * @param file
     * @return
     */
    @PostMapping(value = "/api/sign/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    CadResultDTO<UploadSignRespDTO> uploadSign(
            @RequestParam("dwgPath") String dwgPath,
            @RequestParam("docId") String docId,
            @RequestParam("uploadId") Integer uploadId,
            @RequestPart("file") MultipartFile file);

    /**
     * 获取图纸属性
     *
     * @param dwgPath
     * @param docId
     * @return
     */
    @GetMapping(value = "/api/sign/properties")
    CadResultDTO<List<BlockDataDTO>> getProperties(
            @RequestParam("dwgPath") String dwgPath,
            @RequestParam("docId") String docId
    );

    /**
     * 写入机打信息、签章图、dwg签章
     *
     * @param signSaveReqVO
     * @return
     */
    @PostMapping(value = "/api/sign/save")
    void saveSign(@RequestBody SignSaveReqVO signSaveReqVO);

    /**
     * 图纸pdf转换（也支持png、jpg），以及打印不同规格的pdf，目前用a4
     *
     * @param jsonObject 请求参数过大，不通过实体定义
     * @return pdf文件流，目前返回的pdf有乱码
     */
    @PostMapping(value = "/api/document/print")
    byte[] print(@RequestBody JSONObject jsonObject);

    /**
     * 根据docId下载dwg文件
     *
     * @param docId
     * @return
     */
    @GetMapping(value = "/api/document/download")
    byte[] download(@RequestParam(name = "docId") String docId);

    /**
     * 提交图纸转化json的任务，返回docId，再去getProperties接口获取json信息
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/api/jsonToEntity/uploadDwg", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    CadResultDTO<UploadDwgRespDTO> submitTransJsonTask(
            @RequestPart("file") MultipartFile file);

    /**
     * 获取图纸转换的结果
     *
     * @param docId uploadDwg返回的docId
     * @return
     */
    @GetMapping(value = "/api/jsonToEntity/downloadJson")
    byte[] getProperties(
            @RequestParam("docId") String docId
    );

    /**
     * 获取dwg签名的缩略图
     *
     * @param docId uploadDwg返回的docId
     * @return
     */
    @GetMapping(value = "/api/file/thumbnail/{docId}")
    byte[] thumbnail(
            @PathVariable("docId") String docId);

    /**
     * 签名转化成图片
     *
     * @param jsonObject
     * @return
     */
    @PostMapping(value = "/api/document/formatTransfer")
    byte[] formatTransfer(JSONObject jsonObject);


    @Component
    @Slf4j
    class CadFallbackFactory implements FallbackFactory<CadFeignFallBack> {

        @Override

        public CadFeign.CadFeignFallBack create(Throwable cause) {
            return new CadFeign.CadFeignFallBack(cause);
        }

    }

    @Slf4j
    @Data
    @AllArgsConstructor
    class CadFeignFallBack implements CadFeign {

        private Throwable cause;

        @Override
        public CadResultDTO<UploadRespDTO> upload(String folderId, MultipartFile file) {
            log.error("调用中望cad图纸上传接口报错", cause);
            return CadResultDTO.<UploadRespDTO>builder().code(500).msg(cause.getMessage()).build();
        }

        @Override
        public CadResultDTO<UploadSignRespDTO> uploadSign(String dwgPath, String docId, Integer uploadId, MultipartFile file) {
            log.error("调用中望cad签章上传接口报错", cause);
            return CadResultDTO.<UploadSignRespDTO>builder().code(500).msg(cause.getMessage()).build();
        }

        @Override
        public CadResultDTO<List<BlockDataDTO>> getProperties(String dwgPath, String docId) {
            log.error("调用中望cad获取图纸属性接口报错", cause);
            return CadResultDTO.<List<BlockDataDTO>>builder().code(500).msg(cause.getMessage()).build();
        }

        @Override
        public void saveSign(SignSaveReqVO signSaveReqVO) {
            log.error("调用中望cad写入机打信息、签章报错", cause);
        }

        @Override
        public byte[] print(JSONObject jsonObject) {
            log.error("调用中望cad图纸pdf转换报错", cause);
            return new byte[0];
        }

        @Override
        public byte[] download(String docId) {
            log.error("调用中望cad图纸下载接口报错", cause);
            return new byte[0];
        }

        @Override
        public CadResultDTO<UploadDwgRespDTO> submitTransJsonTask(MultipartFile file) {
            log.error("上传图纸获取json接口报错", cause);
            return CadResultDTO.<UploadDwgRespDTO>builder().code(500).msg(cause.getMessage()).build();
        }

        @Override
        public byte[] getProperties(String docId) {
            log.error("解析图纸属性失败", cause);
            return new byte[0];
        }

        @Override
        public byte[] thumbnail(String docId) {
            return new byte[0];
        }

        @Override
        public byte[] formatTransfer(JSONObject jsonObject) {
            log.error("图纸格式化成png失败", cause);
            return new byte[0];
        }
    }
}
