package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.plm.process.entity.ProcessTemplate;
import com.cdkit.modules.plm.process.entity.ProcessType;
import com.cdkit.modules.plm.process.mapper.ProcessTemplateMapper;
import com.cdkit.modules.plm.process.mapper.ProcessTypeMapper;
import com.cdkit.modules.plm.process.service.IProcessTypeService;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTypeAddVO;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTypeEditVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import jakarta.annotation.Resource;
import java.util.Collection;

/**
 * @Description: process_type
 * @Author: mc
 * @Date:   2024-03-27
 * @Version: V1.0
 */
@Service
@Slf4j
public class ProcessTypeServiceImpl extends ServiceImpl<ProcessTypeMapper, ProcessType> implements IProcessTypeService {
    @Resource
    private ProcessTemplateMapper processTemplateMapper;

    /**
     * 删除工艺类型
     *
     * @param id 主键ID
     */
    @Override
    public void deleteById(String id) {
        log.info("删除工艺类型ID:{}", id);
        Long count = processTemplateMapper.selectCount(new LambdaQueryWrapper<ProcessTemplate>().eq(ProcessTemplate::getProcessTypeId, id));
        if (count > 0L) {
            throw new CdkitCloudException("此工艺类型下有工艺模板，禁止删除");
        }
        this.removeById(id);
    }

    /**
     * 批量删除工艺类型
     *
     * @param ids 主键ID
     */
    @Override
    public void deleteByIds(Collection<String> ids) {
        log.info("批量删除工艺类型ID:{}", JSON.toJSONString(ids));
        for (String id : ids) {
            Long count = processTemplateMapper.selectCount(new LambdaQueryWrapper<ProcessTemplate>().eq(ProcessTemplate::getProcessTypeId, id));
            if (count > 0L) {
                ProcessType processType = this.getById(id);
                throw new CdkitCloudException("工艺类型【" + processType.getName() + "】下有工艺模板，禁止删除");
            }
        }
        this.removeByIds(ids);
    }

    /**
     * 新增工艺类型
     *
     * @param reqProcessTypeAddVO 工艺类型
     */
    @Override
    public void add(ReqProcessTypeAddVO reqProcessTypeAddVO) {
        log.info("新增工艺类型:{}", JSON.toJSONString(reqProcessTypeAddVO));
        ProcessType processTypeOrg = this.getOne(new LambdaQueryWrapper<ProcessType>().eq(ProcessType::getName, reqProcessTypeAddVO.getName()));
        if (processTypeOrg != null) {
            throw new CdkitCloudException("工艺类型【"+ reqProcessTypeAddVO.getName()+"】已存在");
        }
        ProcessType processType = BeanUtil.toBean(reqProcessTypeAddVO, ProcessType.class);
        this.save(processType);
    }

    /**
     * 编辑工艺类型
     *
     * @param reqProcessTypeEditVO 工艺类型
     */
    @Override
    public void edit(ReqProcessTypeEditVO reqProcessTypeEditVO) {
        log.info("编辑工艺类型:{}", JSON.toJSONString(reqProcessTypeEditVO));
        ProcessType processType = BeanUtil.toBean(reqProcessTypeEditVO, ProcessType.class);
        ProcessType processTypeOrg = this.getById(reqProcessTypeEditVO.getId());
        if (!processTypeOrg.getName().equals(reqProcessTypeEditVO.getName())) {
            ProcessType processTypeOld = this.getOne(new LambdaQueryWrapper<ProcessType>().eq(ProcessType::getName, reqProcessTypeEditVO.getName()));
            if (processTypeOld != null) {
                throw new CdkitCloudException("工艺类型【"+ reqProcessTypeEditVO.getName()+"】已存在");
            }
        }
        this.updateById(processType);
    }
}
