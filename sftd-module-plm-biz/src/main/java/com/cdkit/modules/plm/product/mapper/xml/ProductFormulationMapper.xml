<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.plm.product.mapper.ProductFormulationMapper">

    <select id="queryFormulationList" resultType="com.cdkit.modules.plm.product.entity.ProductFormulation">
        select pf.id,pfm.id as productFormulationMasterId,pf.material_code,pf.material_name,pf.material_type_id,pf.formula_code,pf.formula_type,pf.item_type,pf.enable_status,pf.formula_status,pf.remark,pf.formula_version,pf.boom_id,pf.create_by,pf.create_time
        from product_formulation_master pfm inner join product_formulation pf on pfm.product_formulation_id=pf.id
        <where>
            <if test="productFormulation.materialTypeId != null and productFormulation.materialTypeId != ''">
                pf.material_type_id = #{productFormulation.materialTypeId}
            </if>
            <if test="productFormulation.materialCode != null and productFormulation.materialCode != ''">
                and pf.material_code  like concat('%',#{productFormulation.materialCode},'%')
            </if>
            <if test="productFormulation.materialName != null and productFormulation.materialName != ''">
                and pf.material_name like concat('%',#{productFormulation.materialName},'%') ESCAPE '\\'
            </if>
            <if test="productFormulation.formulaCode != null and productFormulation.formulaCode != ''">
                and pf.formula_code  like concat('%',#{productFormulation.formulaCode},'%')
            </if>
            <if test="productFormulation.itemType != null and productFormulation.itemType != ''">
                and pf.item_type = #{productFormulation.itemType}
            </if>
            <if test="productFormulation.formulaType != null and productFormulation.formulaType != ''">
                and pf.formula_type = #{productFormulation.formulaType}
            </if>
            <if test="productFormulation.formulaStatus != null and productFormulation.formulaStatus != ''">
                and pf.formula_status = #{productFormulation.formulaStatus}
            </if>
            and pf.del_flag=0
        </where>
        order by pfm.create_time desc
    </select>

    <select id="queryByMaterialName" resultType="com.cdkit.modules.plm.product.vo.resp.FormulationReplaceVo">
        SELECT pfd.id,pfd.material_code,pf.formula_code,pf.formula_version,pf.formula_type,pfd.boom_id,pfd.product_formulation_id
        FROM product_formulation_detail pfd
            inner join product_formulation pf on pfd.product_formulation_id=pf.id
        WHERE
            pfd.material_name =  #{materialName}
           OR FIND_IN_SET(#{materialName}, fungible_material_name) > 0;
    </select>
</mapper>