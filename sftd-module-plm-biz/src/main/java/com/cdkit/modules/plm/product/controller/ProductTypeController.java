package com.cdkit.modules.plm.product.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.AddProductTypeVO;
import com.cdkit.modules.plm.product.vo.req.EditProductTypeVO;
import com.cdkit.modules.plm.product.vo.resp.ProductTypeVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * @Description: product_tree
 * @Author: <PERSON><PERSON> yang
 * @Date: 2024-03-26
 * @Version: V1.0
 */
@Tag(name = "设计bom-产品分类")
@RestController
@RequestMapping("productType")
@Slf4j
@AllArgsConstructor
public class ProductTypeController extends CdkitController<ProductTree, IProductTreeService> {

    private final IProductTreeService productTreeService;

    /**
     * 添加
     *
     * @param addProductTypeVO
     * @return
     */
    @Operation(summary = "添加产品分类", description = "添加产品分类")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody @Valid AddProductTypeVO addProductTypeVO) {
        productTreeService.addProductType(addProductTypeVO);
        return Result.OK("操作成功！");
    }

    /**
     * 编辑
     *
     * @param editProductTypeVO
     * @return
     */
    @Operation(summary = "编辑产品分类", description = "编辑产品分类")
    @PostMapping(value = "/edit")
    public Result<String> edit(@RequestBody @Validated EditProductTypeVO editProductTypeVO) {
        productTreeService.editProductType(editProductTypeVO);
        return Result.OK("操作成功！");
    }


    /**
     * 通过id删除产品分类
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id删除产品分类", description = "通过id删除产品分类")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id, @RequestParam(name = "cascade", required = false) boolean cascade) {
        productTreeService.cascadeDeleteNode(id, cascade);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询产品分类
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id查询产品分类", description = "通过id查询产品分类")
    @GetMapping(value = "queryById")
    public Result<ProductTypeVO> queryById(@RequestParam(name = "id") String id) {
        ProductTree productTree = productTreeService.getById(id);
        if (productTree == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(BeanUtil.copyProperties(productTree, ProductTypeVO.class));
    }

}
