package com.cdkit.modules.plm.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.modules.plm.product.entity.ProductFormulation;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import com.cdkit.modules.plm.enums.FactoryEnum;
import com.cdkit.modules.plm.enums.ProductTreeTypeEnum;
import com.cdkit.modules.plm.process.service.IFactoryStrategy;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/31
 */
@Component(FactoryEnum.DEFAULT_BEAN_NAME)
@Slf4j
public class DefaultFactoryStrategyImpl implements IFactoryStrategy {

    @Resource @Lazy
    private IProductTreeService productTreeService;
    /**
     * 查询产品档案列表
     *
     * @return 返回结果
     */
    @Override
    public List<ProductTree> getProductFileList(String materialCodeOrName) {
        return productTreeService.list(new LambdaQueryWrapper<ProductTree>()
                .eq(ProductTree::getConverted, 1)
                .eq(ProductTree::getType, ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode())
                .eq(ProductTree::getEnable,0));
    }

    @Override
    public void createTree(ProductFormulation productFormulation) {

    }

    @Override
    public BigDecimal getAssembleQuantity() {
        return BigDecimal.valueOf(1);
    }

    @Override
    public void createApproval(String productFormulationId, HttpServletRequest request) {

    }
}
