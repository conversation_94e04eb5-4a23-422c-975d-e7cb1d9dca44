package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author：mr
 * @name：TechnicalRequirementsVO
 * @Date：2024/4/16 18:00
 */
@Data
@Schema(description ="检验技术要求书")
public class TechnicalRequirementsVO {
    //配方id
    private String id;
    @Schema(description ="代替")
    private String replace;
    @Schema(description ="检验技术要求书文件")
    private String technicalRequirementsFile;
    //规范性引用文件
    private String normativeReferenceDocuments;
    //试验方法
    private String testMethod;
    //检验规则
    private String inspectionRules;

    //是否更新 1-新增 2-更新
    private String updateFlag;
}
