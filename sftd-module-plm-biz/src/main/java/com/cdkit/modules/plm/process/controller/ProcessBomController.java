package com.cdkit.modules.plm.process.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.aspect.annotation.AutoLog;
import com.cdkit.common.system.api.ISysBaseAPI;
import com.cdkit.common.system.vo.DictModel;
import com.cdkit.modules.plm.common.CommonUtil;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.process.service.IProcessBomService;
import com.cdkit.modules.plm.process.vo.req.ReqProductBomConvertVO;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.BomHistory;
import com.cdkit.modules.plm.product.entity.ProductPartExtend;
import com.cdkit.modules.plm.product.service.IBomHistoryService;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.AddProductPartVO;
import com.cdkit.modules.plm.product.vo.req.EditProductPartVO;
import com.cdkit.modules.plm.product.vo.req.FrozenBomVO;
import com.cdkit.modules.plm.product.vo.resp.BomHistoryVO;
import com.cdkit.modules.plm.product.vo.resp.DesignDocumentVO;
import com.cdkit.modules.plm.product.vo.resp.ProductPartVO;
import com.fasterxml.jackson.core.JsonProcessingException;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工艺BOM
 *
 * <AUTHOR>
 * @date 2024/3/28
 */
@Tag(name = "工艺BOM")
@RestController
@RequestMapping("/processBom")
@Slf4j
public class ProcessBomController {
    @Autowired
    private IProcessBomService processBomService;
    @Autowired
    private IProductTreeService productTreeService;
    @Autowired
    private IBomHistoryService historyService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;

    /**
     * 通过产品编码或代号查询产品bom
     *
     * @param code 产品编码或代号
     * @return 返回结果
     */
    @Operation(summary = "通过产品编码或代号查询产品bom", description = "通过产品编码或代号查询产品bom")
    @GetMapping(value = "/getProductBomList")
    public Result<ProductTreeDTO> getProductBomList(@RequestParam(name = "code") String code) {
        ProductTreeDTO dto = processBomService.getProductBomList(code);
        if (dto == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(dto);
    }

    /**
     * 通过产品编码或代号查询工艺bom
     *
     * @param code 产品编码或代号
     * @return 返回结果
     */
    @Operation(summary = "通过产品编码或代号查询工艺bom", description = "通过产品编码或代号查询工艺bom")
    @GetMapping(value = "/getProcessBomList")
    public Result<ProductTreeDTO> getProcessBomList(@RequestParam(name = "code") String code) {
        ProductTreeDTO vo = processBomService.getProcessBomList(code);
        if (vo == null) {
            return Result.OK();
        }
        return Result.OK(vo);
    }

    /**
     * 查询产品bom与工艺bom属性差异
     *
     * @param code 产品编码或代号
     * @return 返回结果
     */
    @Operation(summary = "查询产品bom与工艺bom属性差异", description = "查询产品bom与工艺bom属性差异")
    @GetMapping(value = "/getDiffProperty")
    public Result<HashMap<String, Object>> getDiffProperty(@RequestParam(name = "code") String code) {
        HashMap<String, Object> map = processBomService.getDiffProperty(code);
        if (map == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(map);
    }

    /**
     * 完成BOM转换
     *
     * @param reqProductBomConvertVO 请求参数
     * @return 返回结果
     */
    @AutoLog(value = "完成BOM转换")
    @Operation(summary = "完成BOM转换", description = "完成BOM转换")
    @PostMapping(value = "/finishBomConvert")
    public Result<String> finishBomConvert(@RequestBody List<ReqProductBomConvertVO> reqProductBomConvertVO) {
        processBomService.finishBomConvert(reqProductBomConvertVO);
        return Result.OK("BOM转换成功！");
    }

    /**
     * 查询工艺树
     *
     * @param rootIds 节点ID
     * @return 返回结果
     */
    @Operation(summary = "工艺树", description = "根据根节点id查询树形，不传则查询全部,如果展示产品bom则前端过滤掉文件节点")
    @GetMapping(value = "/tree")
    public Result<List<ProductTreeDTO>> tree(@RequestParam(required = false) List<String> rootIds,@RequestParam(required = false) String materialCodeOrName) {
        List<ProductTreeDTO> dto = productTreeService.tree(rootIds, NodeTypeEnum.PROCESS_TREE,materialCodeOrName);
        return Result.OK(dto);
    }

    /**
     * 查询工艺树
     *
     * @param rootIds 节点ID
     * @return 返回结果
     */
    @Operation(summary = "工艺树", description = "根据根节点id查询树形，不传则查询全部,如果展示产品bom则前端过滤掉文件节点")
    @GetMapping(value = "/treeLazy")
    public Result<List<ProductTreeDTO>> treeLazy(@RequestParam(required = false) String rootIds,@RequestParam(required = false) String materialCodeOrName) {
        List<ProductTreeDTO> dto = productTreeService.treeLazy(rootIds, NodeTypeEnum.PROCESS_TREE,materialCodeOrName);
        List<DictModel> materialItemType = sysBaseAPI.getDictItems("material_item_type");
        List<DictModel> materialsOpType = sysBaseAPI.getDictItems("materials_op_type");
        Map<String, String> materialItemMap = materialItemType.stream().collect(
                Collectors.toMap(DictModel::getValue, DictModel::getText));
        Map<String, String> materialsOpMap = materialsOpType.stream().collect(
                Collectors.toMap(DictModel::getValue, DictModel::getText));
        for (ProductTreeDTO productTreeDTO : dto) {
            productTreeDTO.setPartTypeName(materialItemMap.get(productTreeDTO.getPartType()));
            productTreeDTO.setManufactureTypeName(materialsOpMap.get(productTreeDTO.getManufactureType()));
        }
        return Result.OK(dto);
    }

    /**
     * 查询零件属性
     *
     * @param id 零件ID
     * @return 返回结果
     */
    @Operation(summary = "查询零件属性", description = "查询零件属性")
    @GetMapping(value = "/getPartInfo")
    public Result<ProductPartVO> tree(@RequestParam(name = "id") String id) {
        ProductPartVO dto = productTreeService.queryProductPartById(id);
        if (dto == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(dto);
    }

    /**
     * 新增工艺bom总装/零件
     *
     * @param vo 零件属性信息
     * @return 返回结果
     */
    @AutoLog(value = "新增工艺bom总装/零件")
    @Operation(summary = "新增工艺bom总装/零件", description = "新增工艺bom总装/零件")
    @PostMapping(value = "/addProcessBomPart")
    public Result<ProductPartVO> addProcessBomPart(@RequestBody @Validated AddProductPartVO vo) {
        processBomService.addProcessBomPart(vo);
        return Result.OK("保存成功");
    }

    /**
     * 修改工艺bom零件属性
     *
     * @param vo 零件属性信息
     * @return 返回结果
     */
    @AutoLog(value = "修改工艺bom零件属性")
    @Operation(summary = "修改工艺bom零件属性", description = "修改工艺bom零件属性")
    @PostMapping(value = "/editProcessBomPart")
    public Result<String> editProcessBomPart(@RequestBody @Validated EditProductPartVO vo) {
        vo.setNodeType(NodeTypeEnum.PROCESS_TREE.getCode());
        productTreeService.editProductPart(vo);
        return Result.OK("修改成功");
    }

    /**
     * 查询产品定制属性
     *
     * @return 定制属性列表
     */
    @Operation(summary="查询产品定制属性--新增专属", description="查询产品定制属性-新增专属")
    @GetMapping(value = "/queryProductPartExtendForAdd")
    public Result<List<ProductPartExtend>> queryProductPartExtendForAdd() {
        List<ProductPartExtend> productPartExtends = productTreeService.queryProductPartExtendForAdd();
        return Result.OK(productPartExtends);
    }

    /**
     * 删除工艺bom总装/零件
     *
     * @param id 主键ID
     * @return 返回结果
     */
    @AutoLog(value = "删除工艺bom总装/零件")
    @Operation(summary = "删除工艺bom总装/零件", description = "删除工艺bom总装/零件")
    @DeleteMapping(value = "/deleteProcessBomPart")
    public Result<String> deleteProcessBomPart(@RequestParam(name = "id") String id,
                                               @RequestParam(name = "cascade", required = false) boolean cascade) {
        productTreeService.cascadeDeleteNode(id, cascade);
        return Result.OK("删除成功!");
    }

    /**
     * 固化工艺BOM
     *
     * @param frozenBomVO 请求参数
     * @return 返回结果
     * @throws JsonProcessingException 异常抛出
     */
    @PostMapping("frozenBOM")
    @Operation(summary = "固化工艺BOM", description = "总装或者零部件节点可以进行固化BOM")
    public Result<String> frozenBom(@RequestBody @Validated FrozenBomVO frozenBomVO,@RequestParam(required = false) String materialCodeOrName) throws JsonProcessingException {
        frozenBomVO.setNodeType(NodeTypeEnum.PROCESS_TREE.getCode());
        historyService.frozenBomVO(frozenBomVO,materialCodeOrName);
        return Result.OK("操作成功");
    }

    /**
     * 历史BOM查询
     *
     * @param id 节点ID
     * @return 返回结果
     */
    @Operation(summary = "历史boom-分页列表查询", description = "历史boom-分页列表查询")
    @GetMapping(value = "/listProcessHistoryBom")
    public Result<IPage<BomHistoryVO>> queryPageList(@RequestParam String id,
                                                     @RequestParam(name = "queryCondition", required = false) String queryCondition,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        LambdaQueryWrapper<BomHistory> wrapper = new LambdaQueryWrapper<BomHistory>()
                .eq(BomHistory::getProductTreeId, id)
                .like(StrUtil.isNotBlank(queryCondition), BomHistory::getName, queryCondition).orderByDesc(BomHistory::getCreateTime);
        Page<BomHistory> page = new Page<>(pageNo, pageSize);
        IPage<BomHistory> pageList = historyService.page(page, wrapper);
        IPage<BomHistoryVO> voPage = new Page<>(pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
        voPage.setRecords(pageList.getRecords().stream().map(this::do2vo).collect(Collectors.toList()));
        return Result.OK(voPage);
    }

    /**
     * 通过id查询历史BOM
     *
     * @param id 主键ID
     * @return 返回结果
     */
    @Operation(summary = "历史boom-通过id查询", description = "历史boom-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<BomHistoryVO> queryById(@RequestParam(name = "id") String id) {
        BomHistory bomHistory = historyService.getById(id);
        if (bomHistory == null) {
            return Result.error("未找到对应数据");
        }
        BomHistoryVO bomHistoryVO = do2vo(bomHistory);
        return Result.OK(bomHistoryVO);
    }

    /**
     * BOM比较
     *
     * @param ids 节点ID
     * @return 返回结果
     */
    @Operation(summary = "工艺BOM比较", description = "一种选中一个bom版本同当前结构树上被固化的零件进行比较，另一种情况选中两个BOM版本进行比较")
    @GetMapping(value = "/compareBom")
    public Result<HashMap<String, Object>> compareBom(@RequestParam(name = "ids") String ids,@RequestParam(required = false) String materialCodeOrName) {
        HashMap<String, Object> map = historyService.compareBom(ids, NodeTypeEnum.PROCESS_TREE,materialCodeOrName);
        if (map == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(map);
    }


    private BomHistoryVO do2vo(BomHistory bomHistory) {
        BomHistoryVO data = BeanUtil.copyProperties(bomHistory, BomHistoryVO.class, "version");
        data.setVersion(CommonUtil.numberToLetter(bomHistory.getVersion()));
        return data;
    }

    /**
     * 查询图纸列表
     *
     * @param pid            父ID
     * @param queryCondition 查询条件
     * @param category       图纸类型
     * @param pageNo         页码
     * @param pageSize       页数
     * @return 返回结果
     */
    @GetMapping("listAllDesignDocumentByPid")
    @Operation(summary = "获取父节点下的图纸列表", description = "获取父节点下的图纸列表")
    public Result<Page<DesignDocumentVO>> listAllDesignDocumentByPid(
            @RequestParam String pid,
            @RequestParam(name = "queryCondition", required = false) String queryCondition,
            @RequestParam(name = "category", required = false) String category,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<DesignDocumentVO> designDocumentVOPage = productTreeService.listAllDesignDocumentByPid(pid, queryCondition, category, pageNo, pageSize, NodeTypeEnum.PROCESS_TREE.getCode());
        return Result.OK(designDocumentVOPage);
    }

    @PostMapping("importProcessRoute")
    @Operation(summary = "导入工艺路线", description = "导入工艺路线")
    public void importProcessRoute(@RequestParam("productTreeId") String productTreeId, @RequestParam("file") MultipartFile file) throws IOException {
        productTreeService.importProcessRoute(productTreeId, file);
    }
}
