package com.cdkit.modules.plm.process.service;

import com.cdkit.modules.plm.process.vo.req.ReqProductBomConvertVO;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.vo.req.AddProductPartVO;

import java.util.HashMap;
import java.util.List;

/**
 * 工艺BOM接口
 */
public interface IProcessBomService {
    /**
     * 通过产品编码或代号查询产品bom
     * @param code 产品编码或代号
     * @return 产品BOM树
     */
    ProductTreeDTO getProductBomList(String code);

    /**
     * 通过产品编码或代号查询产品bom
     * @param code 产品编码或代号
     * @return 工艺BOM树
     */
    ProductTreeDTO getProcessBomList(String code);

    /**
     * 完成BOM转换
     * @param reqProductBomConvertVO 请求参数
     */
    void finishBomConvert(List<ReqProductBomConvertVO> reqProductBomConvertVO);

    /**
     * 查询产品bom与工艺bom属性差异
     *
     * @param code 产品编码或代号
     * @return 返回结果
     */
    HashMap<String, Object> getDiffProperty(String code);

    /**
     * 新增工艺bom总装/零件
     * @param vo 零件属性信息
     */
    String addProcessBomPart(AddProductPartVO vo);
}
