package com.cdkit.modules.plm.product.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.enums.ProductTreeTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.common.CommonUtil;
import com.cdkit.modules.plm.product.entity.DesignDocumentHistory;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.DesignDocumentHistoryService;
import com.cdkit.modules.plm.product.service.IDesignDocumentService;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.ChangeWorkVersionReqVO;
import com.cdkit.modules.plm.product.vo.resp.DesignDocumentHistoryVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author：zhao yang
 * @name：DesignDocumentHistoryController
 * @Date：2024/4/10 10:06
 */
@Tag(name = "设计bom-产品图纸历史")
@RestController
@RequestMapping("designDocumentHistory")
@Slf4j
@AllArgsConstructor
public class DesignDocumentHistoryController extends CdkitController<DesignDocumentHistory, DesignDocumentHistoryService> {

    private final IDesignDocumentService designDocumentService;
    private final IProductTreeService productTreeService;

    /**
     * 通过id删除设置图纸历史
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id删除图纸历史", description = "通过id删除图纸历史")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        service.removeById(id);
        return Result.OK("删除成功!");
    }

    @GetMapping("designDocumentHistoryList")
    @Operation(summary = "图纸历史列表", description = "图纸历史列表")
    public Result<Page<DesignDocumentHistoryVO>> list(@RequestParam(name = "designDocumentId") String designDocumentId,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      @RequestParam(name = "queryCondition", required = false) String queryCondition
    ) {
        ProductTree productTree = productTreeService.getById(designDocumentId);
        String sourceId = productTree.getSourceId();
        if (sourceId != null) {
            designDocumentId = sourceId;
        }
        Page<DesignDocumentHistory> page = service.page(new Page<>(pageNo, pageSize),
                new LambdaQueryWrapper<DesignDocumentHistory>()
                        .eq(DesignDocumentHistory::getDesignDocumentId, designDocumentId)
                        .like(StrUtil.isNotBlank(queryCondition), DesignDocumentHistory::getName, queryCondition)
                        .orderByDesc(DesignDocumentHistory::getId)
        );
        Page<DesignDocumentHistoryVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        voPage.setRecords(page.getRecords().stream().map(x -> {
            DesignDocumentHistoryVO vo = BeanUtil.copyProperties(x, DesignDocumentHistoryVO.class, "version");
            vo.setVersion(CommonUtil.numberToLetter(x.getVersion()));
            return vo;
        }).collect(Collectors.toList()));
        return Result.OK(voPage);
    }

    @PostMapping("changeWorkVersion")
    @Operation(summary = "变更工作版本", description = "变更工作版本")
    public void changeWorkVersion(@RequestBody ChangeWorkVersionReqVO changeWorkVersionReqVO) {
        designDocumentService.changeWorkVersion(changeWorkVersionReqVO);
    }

    @GetMapping("designDocumentHistoryListAll")
    @Operation(summary = "图纸历史列表", description = "图纸历史列表")
    public Result<Page<DesignDocumentHistoryVO>> designDocumentHistoryListAll(
            @RequestParam(name = "pid") String pid,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(name = "queryCondition", required = false) String queryCondition
    ) {
        // 直属下级的文件
        LambdaQueryWrapper<ProductTree> wrapper = new LambdaQueryWrapper<ProductTree>()
                .eq(ProductTree::getPid, pid)
                .like(StrUtil.isNotBlank(queryCondition), ProductTree::getName, queryCondition)
                .eq(ProductTree::getType, ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode())
                .eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode());
        //所有下级文件
        List<ProductTree> productTrees = productTreeService.list(wrapper);
        if(null != productTrees && !productTrees.isEmpty()) {
            List<String> designDocumentIds = productTrees.stream().map(ProductTree::getId).collect(Collectors.toList()); // 收集到列表中
            Page<DesignDocumentHistory> page = service.page(new Page<>(pageNo, pageSize),
                    new LambdaQueryWrapper<DesignDocumentHistory>()
                            .in(DesignDocumentHistory::getDesignDocumentId, designDocumentIds)
                            .like(StrUtil.isNotBlank(queryCondition), DesignDocumentHistory::getName, queryCondition)
                            .orderByDesc(DesignDocumentHistory::getId)
            );
            Page<DesignDocumentHistoryVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
            voPage.setRecords(page.getRecords().stream().map(x -> {
                DesignDocumentHistoryVO vo = BeanUtil.copyProperties(x, DesignDocumentHistoryVO.class);
                return vo;
            }).collect(Collectors.toList()));
            return Result.OK(voPage);
        }else{
            return Result.OK(new Page<>(pageNo, pageSize));
        }
    }
}
