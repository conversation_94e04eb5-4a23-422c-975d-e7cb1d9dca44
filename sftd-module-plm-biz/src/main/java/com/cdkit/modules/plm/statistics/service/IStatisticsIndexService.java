package com.cdkit.modules.plm.statistics.service;

import com.cdkit.modules.plm.statistics.vo.resp.RespIndexSummaryVo;

import java.util.concurrent.ExecutionException;

public interface IStatisticsIndexService {
    /**
     * 首页顶部概览查询
     * @return 返回结果
     * @throws ExecutionException 异常抛出
     * @throws InterruptedException 异常抛出
     */
    RespIndexSummaryVo getIndexSummary() throws ExecutionException, InterruptedException;
}
