package com.cdkit.modules.plm.process.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.entity.ProcessExperimentTerm;
import com.cdkit.modules.plm.process.vo.req.ReqProcessExperimentTermVO;

import java.util.List;

/**
 * @Description: 工艺实验项
 * @Author: cdkit-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
public interface IProcessExperimentTermService extends IService<ProcessExperimentTerm> {

    /**
     * 批量保存实验项
     * @param reqProcessExperimentTermVO 实验项列表
     */
    void addBatch(ReqProcessExperimentTermVO reqProcessExperimentTermVO);

    /**
     * 根据refKey查询检测项
     * @param refKey refKey
     * @return 检测项
     */
    List<ProcessExperimentTerm> listTermByRefKey(String refKey);
}
