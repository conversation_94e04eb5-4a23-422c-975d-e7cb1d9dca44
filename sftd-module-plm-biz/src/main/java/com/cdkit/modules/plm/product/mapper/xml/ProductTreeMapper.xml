<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.plm.product.mapper.ProductTreeMapper">

    <insert id="insertOrUpdate">
        insert into product_tree(id, name, type, code, description, belong_factory, pid, path, sort, converted, status, node_type, del_flag, create_by, create_time, update_by, update_time, sys_org_code, tenant_id)
        values
            (#{id}, #{name}, #{type}, #{code}, #{description}, #{belongFactory}, #{pid}, #{path}, #{sort}, #{converted}, #{status}, #{nodeType}, #{delFlag}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{sysOrgCode}, #{tenantId})
        on duplicate key update
        name = values(name),
        type = values(type),
        code = values(code),
        description = values(description),
        belong_factory = values(belong_factory),
        pid = values(pid),
        path = values(path),
        sort = values(sort),
        converted = values(converted),
        status = values(status),
        node_type = values(node_type),
        del_flag = values(del_flag),
        create_by = values(create_by),
        create_time = values(create_time),
        update_by = values(update_by),
        update_time = values(update_time),
        sys_org_code = values(sys_org_code),
        tenant_id = values(tenant_id)

    </insert>
    <delete id="deleteDesignDocument">
        delete from product_tree where pid = #{pid} and type = #{type} and node_type = #{nodeType}
    </delete>
    <select id="countByTypeAndCode" resultType="java.lang.Long">
        SELECT
            sum( num )
        FROM
            ( SELECT count( CODE ) AS num FROM product_tree WHERE `type` = #{type} and del_flag = 0  GROUP BY CODE ) a
    </select>
    <select id="selectTreeList" resultType="com.cdkit.modules.plm.product.dto.ProductTreeDTO">
        SELECT
        pt.id,
        pt.`name`,
        pt.`type`,
        pt.code,
        pt.description,
        pt.belong_factory,
        pt.pid,
        pt.`path`,
        pt.source_id,
        pt.sort,
        pt.converted,
        pt.`status`,
        pt.node_type,
        pt.material_code,
        pt.level, pt.del_flag, pt.create_by, pt.create_time, pt.update_by, pt.update_time, pt.sys_org_code, pt.converted_from_id, pt.copy_from_id,pt.tenant_id,
        pp.`material_name`, pp.`borrow_id`, pp.`manufacture_type`, pp.`part_type`, pp.`handle_type`, pp.`struct_type`, pp.`weight`, pp.`specs`, pp.`version`, pp.`assemble_quantity`, pp.`assemble_sort`, pp.`assemble_unit`, pp.`standard_quantity`, pp.`loss_ratio`, pp.`extend_product_id`, pp.`extend_product_name`, pp.`binding_product_order_num`
        FROM product_tree pt
        left join product_part pp on pp.id = pt.id
        WHERE pt.del_flag = 0 AND (pt.`type` IN (1, 2) OR (pt.`type` IN (3, 4) AND pt.node_type = #{nodeType}))
            AND pt.pid = #{pid}
            AND pt.binding_product_order_num IS NULL
            <if test="materialCodeOrName != null and materialCodeOrName != ''">
                and (pt.name LIKE CONCAT('%', #{materialCodeOrName}, '%') or (pt.material_code = #{materialCodeOrName} ))
            </if>
    </select>

    <select id="selectTreeListAll" resultType="com.cdkit.modules.plm.product.dto.ProductTreeDTO">
        SELECT
        pt.id,
        pt.`name`,
        pt.`type`,
        pt.code,
        pt.description,
        pt.belong_factory,
        pt.pid,
        pt.`path`,
        pt.source_id,
        pt.sort,
        pt.converted,
        pt.`status`,
        pt.node_type,
        pt.material_code,
        pt.level, pt.del_flag, pt.create_by, pt.create_time, pt.update_by, pt.update_time, pt.sys_org_code, pt.converted_from_id, pt.copy_from_id,pt.tenant_id,
        pp.`material_name`, pp.`borrow_id`, pp.`manufacture_type`, pp.`part_type`, pp.`handle_type`, pp.`struct_type`, pp.`weight`, pp.`specs`, pp.`version`, pp.`assemble_quantity`, pp.`assemble_sort`, pp.`assemble_unit`, pp.`standard_quantity`, pp.`loss_ratio`, pp.`extend_product_id`, pp.`extend_product_name`, pp.`binding_product_order_num`,
        dd.file_path,dd.category,dd.file_type,dd.red_batch,dd.sign,dd.file_size,dd.version
        FROM product_tree pt
        left join product_part pp on pp.id = pt.id
        left join design_document dd on dd.id = pt.id and pt.type = 5
        WHERE pt.del_flag = 0
          AND (pt.`type` IN (#{type1}, #{type2}) OR (pt.`type` IN (#{type3}, #{type4}) AND pt.node_type = #{nodeType}))
        AND pt.binding_product_order_num IS NULL
        <if test="!rootIdsSize">
            AND pt.binding_product_order_num is null
        </if>
        <if test="materialCodeOrName != null and materialCodeOrName != ''">
            and (pt.name LIKE CONCAT('%', #{materialCodeOrName}, '%') or (pt.material_code = #{materialCodeOrName} ))
        </if>
    </select>

    <select id="selectTreeListByIds" resultType="com.cdkit.modules.plm.product.dto.ProductTreeDTO">
        SELECT
        pt.id,
        pt.`name`,
        pt.`type`,
        pt.code,
        pt.description,
        pt.belong_factory,
        pt.pid,
        pt.`path`,
        pt.source_id,
        pt.sort,
        pt.converted,
        pt.`status`,
        pt.node_type,
        pt.material_code,
        pt.level, pt.del_flag, pt.create_by, pt.create_time, pt.update_by, pt.update_time, pt.sys_org_code, pt.converted_from_id, pt.copy_from_id,pt.tenant_id,
        pp.`material_name`, pp.`borrow_id`, pp.`manufacture_type`, pp.`part_type`, pp.`handle_type`, pp.`struct_type`, pp.`weight`, pp.`specs`, pp.`version`, pp.`assemble_quantity`, pp.`assemble_sort`, pp.`assemble_unit`, pp.`standard_quantity`, pp.`loss_ratio`, pp.`extend_product_id`, pp.`extend_product_name`, pp.`binding_product_order_num`,
        dd.file_path,dd.category,dd.file_type,dd.red_batch,dd.sign,dd.file_size,dd.version
        FROM product_tree pt
        left join product_part pp on pp.id = pt.id
        left join design_document dd on dd.id = pt.id and pt.type = 5
        WHERE pt.del_flag = 0
        AND (pt.`type` IN (#{type1}, #{type2}) OR (pt.`type` IN (#{type3}, #{type4}) AND pt.node_type = #{nodeType}))
        AND pt.binding_product_order_num IS NULL
        AND pt.id in ( #{ids} )
        <if test="!rootIdsSize">
            AND pt.binding_product_order_num is null
        </if>
        <if test="materialCodeOrName != null and materialCodeOrName != ''">
            and (pt.name LIKE CONCAT('%', #{materialCodeOrName}, '%') or (pt.material_code = #{materialCodeOrName} ))
        </if>
    </select>
</mapper>
