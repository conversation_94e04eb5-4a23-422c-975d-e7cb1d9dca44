package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.md.api.IMdMaterialTypeApi;
import com.cdkit.md.entity.MdMaterialType;
import com.cdkit.modules.plm.common.Constants;
import com.cdkit.modules.plm.product.entity.ProductFormulation;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.cdkit.md.api.IMdMaterialApi;
import com.cdkit.md.api.IMdProductionLineApi;
import com.cdkit.md.entity.MdLineSideInventory;
import com.cdkit.md.entity.MdMaterialPage;
import com.cdkit.modules.plm.enums.FactoryEnum;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.process.service.IFactoryStrategy;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/31
 */
@Component(FactoryEnum.GJS_BEAN_NAME)
@Slf4j
public class GjsFactoryStrategyImpl implements IFactoryStrategy {

    @Resource @Lazy
    private IProductTreeService productTreeService;

    @Resource
    private IMdMaterialTypeApi mdMaterialTypeApi;
    /**
     * 查询产品档案列表
     *
     * @return 返回结果
     */
    @Override
    public List<ProductTree> getProductFileList(String materialCodeOrName) {
        List<ProductTreeDTO> tree = productTreeService.tree(null, NodeTypeEnum.PROCESS_TREE,materialCodeOrName);
        List<ProductTreeDTO> treeList= new ArrayList<>();
        treeToList(tree.get(0),treeList);
        List<ProductTreeDTO> finished = treeList.stream().filter(v -> !StringUtils.isEmpty(v.getPartType()) && v.getPartType().equals("FINISHED")).collect(Collectors.toList());
        List<ProductTree> list = new ArrayList<>();

        for (ProductTreeDTO item : finished) {
            ProductTree productTree = new ProductTree();
            BeanUtil.copyProperties(item, productTree);
            list.add(productTree);
        }

        return list;
    }

    @Override
    public void createTree(ProductFormulation productFormulation) {
        String Fname = FactoryEnum.YHC.getName();
        //1-1创建产品分类
        ProductTree productTree = new ProductTree();
        productTree.setName(Fname);
        productTree.setType(1);
        productTree.setPid("0");
        productTree.setCode(FactoryEnum.YHC_BEAN_NAME);
        productTree.setSort(String.valueOf(1));
        productTree.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        productTree.setConverted(1);
        productTree.setDelFlag(0);
        productTree.setEnable(0);
        productTreeService.save(productTree);
        productTree.setPath("0"+ Constants.PATH_SPILT+ productTree.getId() + Constants.PATH_SPILT);
        productTree.setSort("1");
        productTreeService.updateById(productTree);

        //1-2创建产品种类

        ProductTree pt = new ProductTree();
        MdMaterialType mdMaterialType = mdMaterialTypeApi.queryMaterialType(productFormulation.getMaterialTypeId());
        pt.setName(mdMaterialType.getTypeName());
        pt.setType(2);
        pt.setPid(productTree.getId());
        pt.setCode(mdMaterialType.getId());
        pt.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        pt.setConverted(1);
        pt.setDelFlag(0);
        pt.setEnable(0);
        productTreeService.save(pt);
        pt.setPath(productTree.getPath()+ pt.getId()+ Constants.PATH_SPILT);
        pt.setSort(pt.getId());
        productTreeService.updateById(pt);

    }

    @Override
    public BigDecimal getAssembleQuantity() {
        return BigDecimal.valueOf(1);
    }

    @Override
    public void createApproval(String productFormulationId, HttpServletRequest request) {

    }

    /**
     * 产品树结构转为列表结构
     * @param productTreeDTO 树结构
     * @param treeList 列表结构
     */
    private void treeToList(ProductTreeDTO productTreeDTO,List<ProductTreeDTO> treeList) {
        ProductTreeDTO vo = new ProductTreeDTO();
        BeanUtil.copyProperties(productTreeDTO, vo);
        treeList.add(vo);
        if (productTreeDTO.getChildren() != null && productTreeDTO.getChildren().size() > 0) {
            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
                treeToList(item, treeList);
            }
        }
    }
}
