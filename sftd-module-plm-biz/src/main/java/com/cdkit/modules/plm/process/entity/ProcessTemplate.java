package com.cdkit.modules.plm.process.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;

import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 工艺模板
 * @Author: mc
 * @Date:   2024-04-01
 * @Version: V1.0
 */
@Data
@TableName("process_template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="process_template对象", name="process_template")
public class ProcessTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
	/**模板名称*/
	@Excel(name = "模板名称", width = 15)
    @Schema(description = "模板名称")
    private String templateName;
	/**模板编号*/
	@Excel(name = "模板编号", width = 15)
    @Schema(description = "模板编号")
    private String templateCode;
	/**工艺类型ID*/
	@Excel(name = "工艺类型ID", width = 15)
    @Schema(description = "工艺类型ID")
    private String processTypeId;
    @Excel(name = "工艺类型ID", width = 15)
    @Schema(description = "工艺类型名称")
    @TableField(exist = false)
    private String processTypeName;
	/**模板内容（xml）*/
	@Excel(name = "模板内容（xml）", width = 15)
    @Schema(description = "模板内容（xml）")
    private String templateContent;
	/**模板路径*/
	@Excel(name = "模板路径", width = 15)
    @Schema(description = "模板路径")
    private String templateUrl;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private String tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
}
