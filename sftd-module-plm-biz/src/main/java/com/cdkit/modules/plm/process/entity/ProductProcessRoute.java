package com.cdkit.modules.plm.process.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;

import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 产品工艺路线表
 * @Author: cdkit-boot
 * @Date:   2024-08-05
 * @Version: V1.0
 */
@Data
@TableName("product_process_route")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="product_process_route对象", name="产品工艺路线表")
public class ProductProcessRoute implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**产品ID*/
	@Excel(name = "产品ID", width = 15)
    @Schema(description = "产品ID")
    private String productId;
    /**
     * 物料编码
     */
    @Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;
	/**通过对应节点导入工艺路线*/
	@Excel(name = "通过对应节点导入工艺路线", width = 15)
    @Schema(description = "通过对应节点导入工艺路线")
    private String currProductId;
    /**配方ID*/
    @Excel(name = "配方ID", width = 15)
    @Schema(description = "配方ID")
    private String formulationId;
	/**工艺路线编码*/
	@Excel(name = "工艺路线ID", width = 15)
    @Schema(description = "工艺路线ID")
    private String processRouteId;
	/**工序编码*/
	@Excel(name = "工序编码", width = 15)
    @Schema(description = "工序编码")
    private String processCode;
    /**工序ID*/
    @Excel(name = "工序ID", width = 15)
    @Schema(description = "工序ID")
    private String processId;
    /**工序名称*/
    @Excel(name = "工序名称", width = 15)
    @Schema(description = "工序名称")
    @TableField(exist = false)
    private String processName;
	/**工序序号*/
	@Excel(name = "工序序号", width = 15)
    @Schema(description = "工序序号")
    private String processNumber;
	/**工序内容*/
	@Excel(name = "工序内容", width = 15)
    @Schema(description = "工序内容")
    private String processContent;
    /**工时*/
    @Excel(name = "工时", width = 15)
    @Schema(description = "工时")
    private BigDecimal workHour;
    /**工艺路线明细ID*/
    @Excel(name = "工艺路线明细ID", width = 15)
    @Schema(description = "工艺路线明细ID")
    private String processRouteDetailId;
    @Excel(name = "最新记录 1是 0否", width = 15)
    @Schema(description = "最新记录 1是 0否")
    private Integer isNew;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
}
