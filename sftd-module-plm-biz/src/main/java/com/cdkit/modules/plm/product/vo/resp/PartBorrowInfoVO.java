package com.cdkit.modules.plm.product.vo.resp;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：BorrowInfoVO
 * @Date：2024/3/28 16:20
 */
@Data
@Schema
public class PartBorrowInfoVO {
    @Schema(description = "借用件的id")
    private String targetId;
    @Schema(description = "原件的id")
    private String sourceId;
    @Schema(description = "描述（原件/借用件）")
    private String desc;
    @Schema(description = "产品种类名称")
    private String productKindName;
    @Schema(description = "装配路径中文描述（从产品种类到当前节点）")
    private String assemblePathDesc;
    @Schema(description = "装配路径")
    private String assemblePath;
    @Schema(description = "装配数量")
    private BigDecimal assembleQuantity;
}
