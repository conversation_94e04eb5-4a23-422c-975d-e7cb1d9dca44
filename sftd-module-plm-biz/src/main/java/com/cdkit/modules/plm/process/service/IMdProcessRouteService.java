package com.cdkit.modules.plm.process.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.entity.MdProcessRoute;
import com.cdkit.modules.plm.process.entity.MdProcessRouteDetail;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 工艺路线主数据
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
public interface IMdProcessRouteService extends IService<MdProcessRoute> {

	/**
	 * 添加一对多
	 *
	 * @param mdProcessRoute
	 * @param mdProcessRouteDetailList
	 */
	public void saveMain(MdProcessRoute mdProcessRoute, List<MdProcessRouteDetail> mdProcessRouteDetailList) ;

	/**
	 * 修改一对多
	 *
   * @param mdProcessRoute
   * @param mdProcessRouteDetailList
	 */
	public void updateMain(MdProcessRoute mdProcessRoute, List<MdProcessRouteDetail> mdProcessRouteDetailList);

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain(String id);

	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain(Collection<? extends Serializable> idList);

}
