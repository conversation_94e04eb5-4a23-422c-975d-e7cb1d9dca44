package com.cdkit.modules.plm.product.controller;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.AddProductKindVO;
import com.cdkit.modules.plm.product.vo.req.EditProductKindVO;
import com.cdkit.modules.plm.product.vo.resp.ProductKindVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: product_tree
 * @Author: zhao yang
 * @Date: 2024-03-26
 * @Version: V1.0
 */
@Tag(name = "设计bom-产品种类")
@RestController
@RequestMapping("productKind")
@Slf4j
@AllArgsConstructor
public class ProductKindController extends CdkitController<ProductTree, IProductTreeService> {

    private final IProductTreeService productTreeService;

    /**
     * 添加
     *
     * @param addProductKindVO
     * @return
     */
    @Operation(summary = "添加产品种类", description = "添加产品种类")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody @Validated AddProductKindVO addProductKindVO) {
        productTreeService.addProductKind(addProductKindVO);
        return Result.OK("操作成功！");
    }

    /**
     * 编辑
     *
     * @param editProductVO
     * @return
     */
    @Operation(summary = "编辑产品种类", description = "编辑产品种类")
    @PostMapping(value = "/edit")
    public Result<String> edit(@RequestBody @Validated EditProductKindVO editProductVO) {
        productTreeService.editProductKind(editProductVO);
        return Result.OK("操作成功！");
    }

    /**
     * 通过id删除产品种类
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id删除产品种类", description = "通过id删除产品种类")
    @DeleteMapping(value = "delete")
    public Result<String> delete(@RequestParam(name = "id") String id, @RequestParam(name = "cascade", required = false) boolean cascade) {
        productTreeService.cascadeDeleteNode(id, cascade);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询产品种类
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id查询产品种类", description = "通过id查询产品种类")
    @GetMapping(value = "/queryById")
    public Result<ProductKindVO> queryById(@RequestParam(name = "id") String id) {
        ProductTree productTree = productTreeService.getById(id);
        if (productTree == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(BeanUtil.copyProperties(productTree, ProductKindVO.class));
    }
}
