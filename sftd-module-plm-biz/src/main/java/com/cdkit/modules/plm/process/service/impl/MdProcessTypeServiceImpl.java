package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.system.vo.SelectTreeModel;
import com.cdkit.common.util.oConvertUtils;
import com.cdkit.modules.plm.enums.CodeTypeEnum;
import com.cdkit.modules.plm.process.entity.MdProcessType;
import com.cdkit.modules.plm.process.mapper.MdProcessTypeMapper;
import com.cdkit.modules.plm.process.service.IMdProcessTypeService;
import com.cdkit.modules.plm.util.DidUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * @Description: 工序分类主数据
 * @Author: mc
 * @Date:   2024-07-26
 * @Version: V1.0
 */
@Service
public class MdProcessTypeServiceImpl extends ServiceImpl<MdProcessTypeMapper, MdProcessType> implements IMdProcessTypeService {

	@Override
	public void addMdProcessType(MdProcessType mdProcessType) {
	   //新增时设置hasChild为0
	    mdProcessType.setHasChild(IMdProcessTypeService.NOCHILD);
		if(oConvertUtils.isEmpty(mdProcessType.getPid())){
			mdProcessType.setPid(IMdProcessTypeService.ROOT_PID_VALUE);
		}else{
			//如果当前节点父ID不为空 则设置父节点的hasChildren 为1
			MdProcessType parent = baseMapper.selectById(mdProcessType.getPid());
			if(parent!=null && !"1".equals(parent.getHasChild())){
				parent.setHasChild("1");
				baseMapper.updateById(parent);
			}
		}
        mdProcessType.setTypeCode(DidUtil.getDid(CodeTypeEnum.PROCESS_TYPE_CODE.toString(), MapUtil.empty()));
		baseMapper.insert(mdProcessType);
	}

	@Override
	public void updateMdProcessType(MdProcessType mdProcessType) {
		MdProcessType entity = this.getById(mdProcessType.getId());
		if(entity==null) {
			throw new CdkitCloudException("未找到对应实体");
		}
		String old_pid = entity.getPid();
		String new_pid = mdProcessType.getPid();
		if(!old_pid.equals(new_pid)) {
			updateOldParentNode(old_pid);
			if(oConvertUtils.isEmpty(new_pid)){
				mdProcessType.setPid(IMdProcessTypeService.ROOT_PID_VALUE);
			}
			if(!IMdProcessTypeService.ROOT_PID_VALUE.equals(mdProcessType.getPid())) {
				baseMapper.updateTreeNodeStatus(mdProcessType.getPid(), IMdProcessTypeService.HASCHILD);
			}
		}
		baseMapper.updateById(mdProcessType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteMdProcessType(String id) throws CdkitCloudException {
		//查询选中节点下所有子节点一并删除
        id = this.queryTreeChildIds(id);
        if(id.indexOf(",")>0) {
            StringBuffer sb = new StringBuffer();
            String[] idArr = id.split(",");
            for (String idVal : idArr) {
                if(idVal != null){
                    MdProcessType mdProcessType = this.getById(idVal);
                    String pidVal = mdProcessType.getPid();
                    //查询此节点上一级是否还有其他子节点
                    List<MdProcessType> dataList = baseMapper.selectList(new QueryWrapper<MdProcessType>().eq("pid", pidVal).notIn("id", Arrays.asList(idArr)));
                    boolean flag = (dataList == null || dataList.size() == 0) && !Arrays.asList(idArr).contains(pidVal) && !sb.toString().contains(pidVal);
                    if(flag){
                        //如果当前节点原本有子节点 现在木有了，更新状态
                        sb.append(pidVal).append(",");
                    }
                }
            }
            //批量删除节点
            baseMapper.deleteBatchIds(Arrays.asList(idArr));
            //修改已无子节点的标识
            String[] pidArr = sb.toString().split(",");
            for(String pid : pidArr){
                this.updateOldParentNode(pid);
            }
        }else{
            MdProcessType mdProcessType = this.getById(id);
            if(mdProcessType==null) {
                throw new CdkitCloudException("未找到对应实体");
            }
            updateOldParentNode(mdProcessType.getPid());
            baseMapper.deleteById(id);
        }
	}

	@Override
    public List<MdProcessType> queryTreeListNoPage(QueryWrapper<MdProcessType> queryWrapper) {
        List<MdProcessType> dataList = baseMapper.selectList(queryWrapper);
        List<MdProcessType> mapList = new ArrayList<>();
        for(MdProcessType data : dataList){
            String pidVal = data.getPid();
            //递归查询子节点的根节点
            if(pidVal != null && !IMdProcessTypeService.NOCHILD.equals(pidVal)){
                MdProcessType rootVal = this.getTreeRoot(pidVal);
                if(rootVal != null && !mapList.contains(rootVal)){
                    mapList.add(rootVal);
                }
            }else{
                if(!mapList.contains(data)){
                    mapList.add(data);
                }
            }
        }
        return mapList;
    }

    @Override
    public List<SelectTreeModel> queryListByCode(String parentCode) {
        String pid = ROOT_PID_VALUE;
        if (oConvertUtils.isNotEmpty(parentCode)) {
            LambdaQueryWrapper<MdProcessType> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MdProcessType::getPid, parentCode);
            List<MdProcessType> list = baseMapper.selectList(queryWrapper);
            if (list == null || list.size() == 0) {
                throw new CdkitCloudException("该编码【" + parentCode + "】不存在，请核实!");
            }
            if (list.size() > 1) {
                throw new CdkitCloudException("该编码【" + parentCode + "】存在多个，请核实!");
            }
            pid = list.get(0).getId();
        }
        return baseMapper.queryListByPid(pid, null);
    }

    @Override
    public List<SelectTreeModel> queryListByPid(String pid) {
        if (oConvertUtils.isEmpty(pid)) {
            pid = ROOT_PID_VALUE;
        }
        return baseMapper.queryListByPid(pid, null);
    }

	/**
	 * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
	 * @param pid
	 */
	private void updateOldParentNode(String pid) {
		if(!IMdProcessTypeService.ROOT_PID_VALUE.equals(pid)) {
			Long count = baseMapper.selectCount(new QueryWrapper<MdProcessType>().eq("pid", pid));
			if(count==null || count<=1) {
				baseMapper.updateTreeNodeStatus(pid, IMdProcessTypeService.NOCHILD);
			}
		}
	}

	/**
     * 递归查询节点的根节点
     * @param pidVal
     * @return
     */
    private MdProcessType getTreeRoot(String pidVal){
        MdProcessType data =  baseMapper.selectById(pidVal);
        if(data != null && !IMdProcessTypeService.ROOT_PID_VALUE.equals(data.getPid())){
            return this.getTreeRoot(data.getPid());
        }else{
            return data;
        }
    }

    /**
     * 根据id查询所有子节点id
     * @param ids
     * @return
     */
    private String queryTreeChildIds(String ids) {
        //获取id数组
        String[] idArr = ids.split(",");
        StringBuffer sb = new StringBuffer();
        for (String pidVal : idArr) {
            if(pidVal != null){
                if(!sb.toString().contains(pidVal)){
                    if(sb.toString().length() > 0){
                        sb.append(",");
                    }
                    sb.append(pidVal);
                    this.getTreeChildIds(pidVal,sb);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 递归查询所有子节点
     * @param pidVal
     * @param sb
     * @return
     */
    private StringBuffer getTreeChildIds(String pidVal, StringBuffer sb){
        List<MdProcessType> dataList = baseMapper.selectList(new QueryWrapper<MdProcessType>().eq("pid", pidVal));
        if(dataList != null && dataList.size()>0){
            for(MdProcessType tree : dataList) {
                if(!sb.toString().contains(tree.getId())){
                    sb.append(",").append(tree.getId());
                }
                this.getTreeChildIds(tree.getId(),sb);
            }
        }
        return sb;
    }

}
