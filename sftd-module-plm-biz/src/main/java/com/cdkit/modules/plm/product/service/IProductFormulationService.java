package com.cdkit.modules.plm.product.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.vo.resp.RespProcessVO;
import com.cdkit.modules.plm.product.entity.ProductFormulation;
import com.cdkit.modules.plm.product.entity.ProductFormulationDetail;
import com.cdkit.modules.plm.product.vo.req.ImportProcessRequestUpgradeVO;
import com.cdkit.modules.plm.product.vo.req.ReplaceMainMaterialVO;
import com.cdkit.modules.plm.product.vo.resp.FormulationReplaceVo;
import jakarta.servlet.http.HttpServletRequest;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.cdkit.modules.plm.product.vo.req.ImportProcessRequestVO;

/**
 * @Description: product_formulation
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
public interface IProductFormulationService extends IService<ProductFormulation> {

    /**
     * 工序与配方绑定
     *
     * @param importProcessRequestVO 请求参数
	 * @param request   HttpServletRequest
     */
    void processRouteImporter(ImportProcessRequestVO importProcessRequestVO, HttpServletRequest request);

	/**
	 * 工序与配方绑定升级
	 *
	 * @param importProcessRequestUpgradeVO 请求参数
	 */
	void processRouteImporterUpgrade(ImportProcessRequestUpgradeVO importProcessRequestUpgradeVO);
	/**
	 * 添加一对多
	 *
	 * @param productFormulation
	 * @param productFormulationDetailList
	 */
	public ProductFormulation saveMain(ProductFormulation productFormulation, List<ProductFormulationDetail> productFormulationDetailList) ;

	/**
	 * 修改一对多
	 *
   * @param productFormulation
   * @param productFormulationDetailList
	 */
	public void updateMain(ProductFormulation productFormulation,List<ProductFormulationDetail> productFormulationDetailList,List<ProductFormulationDetail> deleteFormulationDetailList,HttpServletRequest request);

	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);

	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);

	void updateStatus(List<ProductFormulation> productFormulation);

	IPage<ProductFormulation> queryTask(Page<ProductFormulation> page, ProductFormulation productFormulation);

	void initiateApproval(String productFormulationId, HttpServletRequest request) throws Exception;

	void approvalPass(ProductFormulation productFormulation, HttpServletRequest request);

	void reject(ProductFormulation productFormulation, HttpServletRequest request);

	/**
	 * 查询工序步骤
	 *
	 * @param processId 工序ID
	 * @param productId 产成品ID(bomId)
	 * @return RespProcessVO
	 */
	RespProcessVO getProcessDetail(String processId, String productId);

	/**
	 * 根据配方ID查询工序绑定信息
	 *
	 * @param id    配方ID
	 * @param boomId 原boomId
	 * @return 工序绑定信息
	 */
	ImportProcessRequestVO getProcessRouteImporter(String id, String boomId);

	/**
	 * 替换产品工序中的物料编码
	 * @param oldMaterialCode 原物料编码
	 * @param newMaterialCode 新物料编码
	 * @param formulationIdList 配方ID
	 */
	void replaceProductProcessMaterial(String oldMaterialCode, String newMaterialCode, List<String> formulationIdList);

	ProductFormulation queryById(String id);

	ProductFormulation formulaUpgrade(String id);

	IPage<FormulationReplaceVo>  queryByMaterialName(Page<FormulationReplaceVo> page,String materialName);

	List<Map<String,String>> queryListByMaterialName(String materialName);

	ProductFormulation formulaCopy(String id);

	void replaceMainMaterial(ReplaceMainMaterialVO replaceMainMaterialVO);

	void replaceAuxiliaryMaterials(ReplaceMainMaterialVO replaceMainMaterialVO);

	void InitializeData();

	/**
	 * 根据物料编码查询配方字典数据
	 * @param materialCode 物料编码
	 * @return 返回字典数据，key是material_code或fungible_material_codes，value是material_name或fungible_material_name
	 */
	Map<String, String> queryFormulationDictByMaterialCode(String materialCode);
}
