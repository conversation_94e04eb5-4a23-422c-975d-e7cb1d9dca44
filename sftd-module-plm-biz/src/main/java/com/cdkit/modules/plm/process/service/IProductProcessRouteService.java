package com.cdkit.modules.plm.process.service;

import com.cdkit.modules.plm.process.entity.ProductProcessRoute;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.vo.req.MdProcessVo;
import com.cdkit.modules.plm.process.vo.req.ReqProductProcessRouteVo;
import com.cdkit.modules.plm.process.vo.resp.RespProcessVO;
import com.cdkit.modules.plm.product.dto.ProductListDTO;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.vo.resp.ProductFileVO;

import java.util.List;

/**
 * @Description: 产品工艺路线表
 * @Author: cdkit-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
public interface IProductProcessRouteService extends IService<ProductProcessRoute> {

    /**
     * 导入工艺路线
     *
     * @param reqProductProcessRouteVo 请求参数
     */
    void importProcessRoute(ReqProductProcessRouteVo reqProductProcessRouteVo);

    /**
     * 查询节点对应工艺路线
     *
     * @param id 节点ID
     * @return 返回结果
     */
    List<ProductProcessRoute> listProcessRoute(String id);

    /**
     * 查询节点对应工艺
     *
     * @param id 节点ID
     * @return 返回结果
     */
    RespProcessVO getProcessDetail(String id);

    /**
     * 保存节点对应工艺内容或步骤
     *
     * @param respProcessVO 请求参数
     */
    void saveProcessDetail(RespProcessVO respProcessVO);


    /**
     * 通过产品编码或代号查询产品档案
     *
     * @param code 产品编码或代号
     * @return 返回结果
     */
    ProductFileVO getProductFile(String code);

    /**
     * 通过产品BOM ID查询产品档案
     *
     * @param id 产品BOM ID
     * @return 返回结果
     */
    ProductFileVO getProductFileById(String id);

    /**
     * 查询产品档案列表
     *
     * @return 返回结果
     */
    List<ProductTree> getProductFileList();

    /**
     * 产品树结构转为列表结构--如果不包含子节点则不平铺
     *
     * @param productTreeDTO 树结构
     * @param productListDTO 列表结构
     */
    void treeToListWithOutChildren(ProductTreeDTO productTreeDTO, List<ProductListDTO> productListDTO);

    void add(MdProcessVo productProcessRouteVo);

    /**
     * 通过代号查询产品档案
     *
     * @param code 产品代号
     * @return 返回结果
     */
    ProductFileVO getProductFileByCode(String code);

    /**
     * 查询配方对应工艺路线
     *
     * @param formulationId 配方ID
     * @param productId 产品ID
     * @return 返回结果
     */
    List<ProductProcessRoute> listProcessRouteByFormulationIdAndProductId(String formulationId, String productId);
}
