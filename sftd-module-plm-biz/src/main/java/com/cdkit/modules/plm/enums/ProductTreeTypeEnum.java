package com.cdkit.modules.plm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：ProductTreeType
 * @Date：2024/3/29 11:01
 */
@AllArgsConstructor
@Getter
public enum ProductTreeTypeEnum {
    PRODUCT_TYPE(1, "产品分类"),
    PRODUCT_KIND(2, "产品种类"),
    PRODUCT_FINAL_ASSEMBLE(3, "产品总装"),
    PRODUCT_PART(4, "产品零部件"),
    DESIGN_DOCUMENT(5, "设计文档");

    private final Integer code;
    private final String desc;

}
