package com.cdkit.modules.plm.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.product.entity.DesignDocument;
import com.cdkit.modules.plm.product.vo.req.ChangeWorkVersionReqVO;

/**
 * @Description: design_document
 * @Author: mc
 * @Date: 2024-03-26
 * @Version: V1.0
 */
public interface IDesignDocumentService extends IService<DesignDocument> {
    /**
     * 变更工作版本
     *
     * @param changeWorkVersionReqVO
     */
    void changeWorkVersion(ChangeWorkVersionReqVO changeWorkVersionReqVO);

}
