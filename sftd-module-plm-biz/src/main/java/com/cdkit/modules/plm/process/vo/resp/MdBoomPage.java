package com.cdkit.modules.plm.process.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkitframework.poi.excel.annotation.ExcelCollection;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: boom模板
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Data
@Schema(name="boom模板对象", description="boom模板对象")
public class MdBoomPage {

	/**id*/
	@Schema(description = "id")
    private String id;
	/**物料名称*/
	@Excel(name = "物料名称", width = 15)
	@Schema(description = "物料名称")
    private String materialName;
	/**物料编码*/
	@Excel(name = "物料编码", width = 15)
	@Schema(description = "物料编码")
	private String materialCode;
	/**父级物料名称*/
	@Excel(name = "父级物料名称", width = 15)
	@Schema(description = "父级物料名称")
    private String pName;
	/**父级物料名称*/
	@Excel(name = "父级物料编码", width = 15)
	@Schema(description = "父级物料编码")
	private String pCode;
	/**装备数量*/
	@Excel(name = "装备数量", width = 15)
	@Schema(description = "装备数量")
    private BigDecimal assembleQuantity;
	/**生产类型*/
	@Excel(name = "生产类型", width = 15)
	@Schema(description = "生产类型")
	private String manufactureType;
	/**料件类型*/
	@Excel(name = "料件类型", width = 15)
	@Schema(description = "料件类型")
    private String partType;
	/**工序名称*/
	@Excel(name = "工序名称", width = 15)
	@Schema(description = "工序名称")
    private String processName;
	/**工序名称*/
	@Excel(name = "工序编码", width = 15)
	@Schema(description = "工序编码")
	private String processCode;

	@ExcelCollection(name="工艺步骤")
	@Schema(description = "工艺步骤")
	private List<MdStepVO> mdProcessDetailList;

}
