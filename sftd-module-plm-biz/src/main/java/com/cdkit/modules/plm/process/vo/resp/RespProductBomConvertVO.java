package com.cdkit.modules.plm.process.vo.resp;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@Data
@Schema(description = "bom转换")
public class RespProductBomConvertVO {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;
    /**
     * 父ID
     */
    @Schema(description = "父ID")
    private String pid;
    /**
     * 编码/代号
     */
    @Schema(description = "编码/代号")
    private String code;
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
    /**
     * 产品类型：产品大类、产品、文件夹、文件、总装、零部件
     */
    @Schema(description = "产品类型：产品大类、产品、总装、零部件")
    private String type;
    /**
     * 对比不同
     */
    @Schema(description = "对比不同：1橙色代表当前层级下未找到相同零件 2黄色表示零件相同，但属性不同 3蓝色表示图纸有差异 4白色表示零件一致")
    private String diff;
    /**
     * 子节点
     */
    @Schema(description = "子节点")
    private List<RespProductBomConvertVO> childrenList;

}
