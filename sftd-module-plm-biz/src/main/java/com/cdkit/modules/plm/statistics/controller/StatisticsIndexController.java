package com.cdkit.modules.plm.statistics.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.plm.operation.entity.OperationLog;
import com.cdkit.modules.plm.operation.service.IOperationLogService;
import com.cdkit.modules.plm.statistics.service.IStatisticsIndexService;
import com.cdkit.modules.plm.statistics.vo.resp.RespIndexSummaryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2024/5/11
 */
@Tag(name="首页统计")
@RestController
@RequestMapping("/statisticsIndex")
@Slf4j
public class StatisticsIndexController {
    @Resource
    private IStatisticsIndexService statisticsIndexService;
    @Resource
    private IOperationLogService operationLogService;

    /**
     * 首页顶部概览查询
     * @return 返回结果
     */
    @Operation(summary="首页顶部概览查询", description="首页顶部概览查询")
    @GetMapping(value = "/getIndexSummary")
    public Result<RespIndexSummaryVo> getIndexSummary() throws ExecutionException, InterruptedException {
        RespIndexSummaryVo vo = statisticsIndexService.getIndexSummary();
        if(vo == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(vo);
    }

    /**
     * 首页最新动态查询
     * @return 返回结果
     */
    @Operation(summary="首页最新动态查询", description="首页最新动态查询")
    @GetMapping(value = "/getLatestNews")
    public Result<List<OperationLog>> getLatestNews() {
        List<OperationLog> list = operationLogService.list(new LambdaQueryWrapper<OperationLog>().orderByDesc(OperationLog::getCreateTime).last("limit 6"));
        return Result.OK(list);
    }
}
