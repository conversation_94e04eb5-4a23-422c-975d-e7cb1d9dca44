package com.cdkit.modules.plm.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 设计文档
 */
@Schema(description = "设计文档")
@Data
@TableName(value = "design_document_history")
public class DesignDocumentHistory implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    @Size(max = 36, message = "id最大长度要小于 36")
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 设计文档id
     */
    @TableField(value = "design_document_id")
    @Schema(description = "设计文档id")
    @Size(max = 36, message = "设计文档id最大长度要小于 36")
    @NotBlank(message = "设计文档id不能为空")
    private String designDocumentId;

    /**
     * 图纸名称(冗余，主表页也有一个)
     */
    @TableField(value = "`name`")
    @Schema(description = "图纸名称(冗余，主表页也有一个)")
    @Size(max = 255, message = "图纸名称(冗余，主表页也有一个)最大长度要小于 255")
    private String name;

    /**
     * 文件id，根据文件id再去filecenter的minio的文件
     */
    @TableField(value = "file_path")
    @Schema(description = "文件id，根据文件id再去filecenter的minio的文件")
    @Size(max = 255, message = "文件id，根据文件id再去filecenter的minio的文件最大长度要小于 255")
    private String filePath;

    /**
     * 中望cad的图纸id
     */
    @TableField(value = "doc_id")
    @Schema(description = "中望cad的图纸id")
    @Size(max = 100, message = "中望cad的图纸id最大长度要小于 100")
    private String docId;

    /**
     * 文档分类，图纸/
     */
    @TableField(value = "category")
    @Schema(description = "文档分类，图纸/")
    @Size(max = 50, message = "文档分类，图纸/最大长度要小于 50")
    private String category;

    /**
     * 文档后缀类型，exb
     */
    @TableField(value = "file_type")
    @Schema(description = "文档后缀类型，exb")
    @Size(max = 50, message = "文档后缀类型，exb最大长度要小于 50")
    private String fileType;

    /**
     * 是否已红批
     */
    @TableField(value = "red_batch")
    @Schema(description = "是否已红批")
    private Boolean redBatch;

    /**
     * 是否已签名
     */
    @TableField(value = "sign")
    @Schema(description = "是否已签名")
    private Boolean sign;

    /**
     * 文件大小
     */
    @TableField(value = "file_size")
    @Schema(description = "文件大小")
    private Integer fileSize;

    /**
     * 轻量化文件id
     */
    @TableField(value = "light_file_path")
    @Schema(description = "轻量化文件id")
    @Size(max = 255, message = "轻量化文件id最大长度要小于 255")
    private String lightFilePath;

    /**
     * 轻量化文件名称
     */
    @TableField(value = "light_file_name")
    @Schema(description = "轻量化文件名称")
    @Size(max = 255, message = "轻量化文件名称最大长度要小于 255")
    private String lightFileName;

    /**
     * 锁定人
     */
    @TableField(value = "locked_by")
    @Schema(description = "锁定人")
    @Size(max = 36, message = "锁定人最大长度要小于 36")
    private String lockedBy;

    /**
     * 出库时间
     */
    @TableField(value = "locked_time")
    @Schema(description = "出库时间")
    private Date lockedTime;

    /**
     * 版本号
     */
    @TableField(value = "version")
    @Schema(description = "版本号")
    private double version;

    private static final long serialVersionUID = 1L;
}
