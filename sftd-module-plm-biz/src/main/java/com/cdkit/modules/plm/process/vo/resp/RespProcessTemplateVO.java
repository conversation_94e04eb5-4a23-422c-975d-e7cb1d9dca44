package com.cdkit.modules.plm.process.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import com.cdkit.modules.plm.process.entity.ProcessTemplate;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/3
 */
@Data
@Schema(description = "工艺模板")
public class RespProcessTemplateVO {
    /**主键ID*/
    @Schema(description = "主键ID")
    private String id;
    /**工艺类型名称*/
    @Schema(description = "工艺类型名称")
    private String name;

    /**
     * 工艺模板
     */
    private List<ProcessTemplate> processTemplateList;
}
