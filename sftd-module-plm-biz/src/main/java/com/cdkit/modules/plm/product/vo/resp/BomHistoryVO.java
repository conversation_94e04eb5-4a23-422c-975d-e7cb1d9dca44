package com.cdkit.modules.plm.product.vo.resp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Date;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：BomHistoryVO
 * @Date：2024/4/6 00:02
 */
@Data
@Schema(description = "bom历史vo")
public class BomHistoryVO {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    @Size(max = 36, message = "id最大长度要小于 36")
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 产品树id
     */
    @TableField(value = "product_tree_id")
    @Schema(description = "产品树id")
    @Size(max = 36, message = "产品树id最大长度要小于 36")
    @NotBlank(message = "产品树id不能为空")
    private String productTreeId;

    /**
     * bom版本名称
     */
    @TableField(value = "`name`")
    @Schema(description = "bom版本名称")
    @Size(max = 60, message = "bom版本名称最大长度要小于 60")
    @NotBlank(message = "bom版本名称不能为空")
    private String name;

    /**
     * 历史bom json信息，以json格式存储product_tree_id节点对应的bom清单
     */
    @TableField(value = "history_bom_json")
    @Schema(description = "历史bom json信息，以json格式存储product_tree_id节点对应的bom清单")
    @Size(max = 1000, message = "历史bom json信息，以json格式存储product_tree_id节点对应的bom清单最大长度要小于 1000")
    private String historyBomJson;

    /**
     * bom版本
     */
    @TableField(value = "version")
    @Schema(description = "bom版本")
    @NotNull(message = "bom版本不能为null")
    private String version;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description = "描述")
    @Size(max = 800, message = "描述最大长度要小于 800")
    private String description;

    /**
     * 是否删除
     */
    @TableField(value = "del_flag")
    @Schema(description = "是否删除")
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    @Schema(description = "更新人")
    @Size(max = 50, message = "更新人最大长度要小于 50")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 节点类型 1:产品树 2工艺树
     */
    @TableField(value = "node_type")
    @Schema(description = "节点类型 1:产品树 2工艺树")
    private Integer nodeType;

    private static final long serialVersionUID = 1L;
}
