package com.cdkit.modules.plm.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：ThreadPoolConfig
 * @Date：2024/6/3 17:16
 */
@Configuration
public class ThreadPoolConfig {

    private static final int CORE_POOL_SIZE = 5;
    private static final int MAX_POOL_SIZE = 10;
    private static final long KEEP_ALIVE_TIME = 60L;
    private static final TimeUnit UNIT = TimeUnit.SECONDS;
    private static final BlockingQueue<Runnable> WORK_QUEUE = new LinkedBlockingQueue<>(200);


    @Bean(destroyMethod = "shutdown")
    public ExecutorService threadPoolTaskExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                UNIT,
                WORK_QUEUE,
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        // 可选：设置线程工厂，用于自定义线程名等
        executor.setThreadFactory(new NamedThreadFactory("DidGenThreadPool-"));
        return executor;
    }

    // 简单的线程工厂类，用于命名线程
    private static class NamedThreadFactory implements ThreadFactory {
        private final String namePrefix;
        private int threadId;

        NamedThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + threadId++);
            t.setDaemon(true);
            return t;
        }
    }
}
