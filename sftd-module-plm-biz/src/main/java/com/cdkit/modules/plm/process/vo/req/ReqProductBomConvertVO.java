package com.cdkit.modules.plm.process.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@Data
@Schema(description = "bom转换")
public class ReqProductBomConvertVO {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;
    /**
     * 父ID
     */
    @Schema(description = "父ID")
    private String pid;
    /**
     * 编码/代号
     */
    @Schema(description = "编码/代号")
    private String code;
}
