package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：DesignDocumentPublishVO
 * @Date：2024/4/5 15:18
 */
@Data
@Schema(description = "设计文档发布、取消发布、重发布、6:提交并签名、审核中")
public class DesignDocumentPublishVO {
    @NotBlank
    @Schema(description = "设计文档id")
    private String id;
    @Schema(description = "1:设计中，2:已发布，3:重发布，6:提交并签名")
    @NotNull
    private Integer status;
}
