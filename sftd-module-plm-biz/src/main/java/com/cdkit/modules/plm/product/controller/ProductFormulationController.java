package com.cdkit.modules.plm.product.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.cdkit.modules.plm.process.vo.req.ReqProductProcessRouteVo;
import com.cdkit.modules.plm.process.vo.resp.RespProcessVO;
import com.cdkit.modules.plm.product.entity.ProductFormulation;
import com.cdkit.modules.plm.product.entity.ProductFormulationDetail;
import com.cdkit.modules.plm.product.service.IProductFormulationDetailService;
import com.cdkit.modules.plm.product.service.IProductFormulationService;
import com.cdkit.modules.plm.product.vo.req.ImportProcessRequestVO;
import com.cdkit.modules.plm.product.vo.ProductFormulationPage;
import com.cdkit.modules.plm.product.vo.req.ReplaceMainMaterialVO;
import com.cdkit.modules.plm.product.vo.resp.FormulationReplaceVo;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.cdkitframework.poi.excel.ExcelImportUtil;
import com.cdkitframework.poi.excel.def.NormalExcelConstants;
import com.cdkitframework.poi.excel.entity.ExportParams;
import com.cdkitframework.poi.excel.entity.ImportParams;
import com.cdkitframework.poi.excel.view.CdkitEntityExcelView;
import com.cdkit.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.query.QueryGenerator;
import com.cdkit.common.util.oConvertUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.cdkit.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;


 /**
 * @Description: product_formulation
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
@Tag(name="配方管理")
@RestController
@RequestMapping("/product/productFormulation")
@Slf4j
public class ProductFormulationController {
	@Autowired
	private IProductFormulationService productFormulationService;
	@Autowired
	private IProductFormulationDetailService productFormulationDetailService;


	/**
	 * 分页列表查询
	 *
	 * @param productFormulation
	 * @param pageNo
	 * @param pageSize
	 * @param
	 * @return
	 */
	//@AutoLog(value = "product_formulation-分页列表查询")
	@Operation(summary="product_formulation-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ProductFormulation>> queryPageList(
			ProductFormulation productFormulation,
			@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
			@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {

		// 参数验证
		if (productFormulation == null) {
			log.warn("查询参数为空");
			return Result.error("查询参数错误");
		}

		// 验证页码和每页记录数
		if (pageNo <= 0) {
			log.warn("页码不合法，重置为1: {}", pageNo);
			pageNo = 1;
		}

		if (pageSize <= 0 || pageSize > 100) {
			log.warn("每页记录数不合法，重置为默认值: {}", pageSize);
			pageSize = 10;
		}

		// 创建分页对象
		Page<ProductFormulation> page = new Page<>(pageNo, pageSize);

		try {
			// 执行查询
			log.info("开始查询配方列表，页码: {}, 每页记录数: {}", pageNo, pageSize);
			String name = escapeLikeParam(productFormulation.getMaterialName());
			productFormulation.setMaterialName(name);
			IPage<ProductFormulation> result = productFormulationService.queryTask(page, productFormulation);

			// 记录查询结果
			log.info("查询完成，总记录数: {}, 当前页记录数: {}", result.getTotal(), result.getRecords().size());

			return Result.OK(result);
		} catch (Exception e) {
			log.error("查询配方列表异常", e);
			return Result.error("查询失败，请稍后重试");
		}
	}

	/**
	 *   添加
	 *
	 * @param productFormulationPage
	 * @return
	 */
	@AutoLog(value = "product_formulation-添加")
	@Operation(summary="product_formulation-添加")
	@PostMapping(value = "/add")
	public Result<ProductFormulation> add(@RequestBody ProductFormulationPage productFormulationPage) {
		ProductFormulation productFormulation = new ProductFormulation();
		BeanUtils.copyProperties(productFormulationPage, productFormulation);
		ProductFormulation pf = productFormulationService.saveMain(productFormulation, productFormulationPage.getProductFormulationDetailList());
		return Result.OK(pf);
	}

	/**
	 *  编辑
	 *
	 * @param productFormulationPage
	 * @return
	 */
	@AutoLog(value = "product_formulation-编辑")
	@Operation(summary="product_formulation-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ProductFormulationPage productFormulationPage, HttpServletRequest request) {
		ProductFormulation productFormulation = new ProductFormulation();
		BeanUtils.copyProperties(productFormulationPage, productFormulation);
		ProductFormulation productFormulationEntity = productFormulationService.getById(productFormulation.getId());
		if(productFormulationEntity==null) {
			return Result.error("未找到对应数据");
		}
		productFormulationService.updateMain(productFormulation, productFormulationPage.getProductFormulationDetailList(),productFormulationPage.getDeleteFormulationDetailList(),request);
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "product_formulation-通过id删除")
	@Operation(summary="product_formulation-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		productFormulationService.delMain(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "product_formulation-批量删除")
	@Operation(summary="product_formulation-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.productFormulationService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "product_formulation-通过id查询")
	@Operation(summary="product_formulation-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ProductFormulation> queryById(@RequestParam(name="id",required=true) String id) {
		ProductFormulation productFormulation = productFormulationService.queryById(id);
		if(productFormulation==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(productFormulation);

	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "product_formulation_detail通过主表ID查询")
	@Operation(summary="product_formulation_detail主表ID查询")
	@GetMapping(value = "/queryProductFormulationDetailByMainId")
	public Result<List<ProductFormulationDetail>> queryProductFormulationDetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<ProductFormulationDetail> productFormulationDetailList = productFormulationDetailService.selectByMainId(id);
		return Result.OK(productFormulationDetailList);
	}

	 /**
	  * 工序与配方绑定
	  *
	  * @param importProcessRequestVO 请求参数
	  * @return 返回结果
	  */
	 @Operation(summary = "第二步保存", description = "第二步保存")
	 @PostMapping(value = "/processRouteImporter")
	 public Result<String> processRouteImporter(@RequestBody ImportProcessRequestVO importProcessRequestVO, HttpServletRequest request) {
		 productFormulationService.processRouteImporter(importProcessRequestVO, request);
		 return Result.OK("保存成功！");
	 }

	 /**
	  * 根据工序查询物料绑定信息
	  *
	  * @param processId 工序ID
	  * @param productId 产成品ID(bomId)
	  * @return 物料组成ID
	  */
	 @Operation(summary="根据工序查询物料绑定信息")
	 @GetMapping(value = "/processProductList")
	 public Result<List<String>> processProductList(@RequestParam(name="processId") @Parameter(description = "工序ID") String processId,
													@RequestParam(name="productId") @Parameter(description = "产成品ID(bomId)") String productId) {
		 List<String> processProductList = productFormulationDetailService.processProductList(processId, productId);
		 return Result.OK(processProductList);
	 }

	 /**
	  * 根据工序查询绑定的产出物
	  *
	  * @param processId 工序ID
	  * @param productId 产成品ID(bomId)
	  * @return 产出物ID
	  */
	 @Operation(summary="根据工序查询绑定的产出物")
	 @GetMapping(value = "/processOutputProduct")
	 public Result<String> processOutputProduct(@RequestParam(name="processId") @Parameter(description = "工序ID") String processId,
													@RequestParam(name="productId") @Parameter(description = "产成品ID(bomId)") String productId) {
		 return Result.OK(productFormulationDetailService.processOutputProduct(processId, productId));
	 }

	 /**
	  * 根据配方ID查询工序绑定信息
	  *
	  * @param id 配方ID
	  * @param boomId 原bomId
	  * @return 工序绑定信息
	  */
	 @Operation(summary="根据配方ID查询工序绑定信息")
	 @GetMapping(value = "/getProcessRouteImporter")
	 public Result<ImportProcessRequestVO> getProcessRouteImporter(@RequestParam(name="id") @Parameter(description = "配方ID") String id,
																   @RequestParam(name="boomId") @Parameter(description = "原boomId") String boomId) {
		 return Result.OK(productFormulationService.getProcessRouteImporter(id, boomId));
	 }

	 /**
	  * 查询工序步骤
	  *
	  * @param processId 工序ID
	  * @param productId 产成品ID(bomId)
	  * @return RespProcessVO
	  */
	 @Operation(summary="查询工序步骤")
	 @GetMapping(value = "/getProcessDetail")
	 public Result<RespProcessVO> getProcessDetail(@RequestParam(name="processId") @Parameter(description = "工序ID") String processId,
												   @RequestParam(name="productId") @Parameter(description = "产成品ID(bomId)") String productId) {
		 return Result.OK(productFormulationService.getProcessDetail(processId, productId));
	 }


	 /**
     * 导出excel
     *
     * @param request
     * @param productFormulation
     */
    @RequiresPermissions("product:product_formulation:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProductFormulation productFormulation) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<ProductFormulation> queryWrapper = QueryGenerator.initQueryWrapper(productFormulation, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
         List<String> selectionList = Arrays.asList(selections.split(","));
         queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<ProductFormulation> productFormulationList = productFormulationService.list(queryWrapper);

      // Step.3 组装pageList
      List<ProductFormulationPage> pageList = new ArrayList<ProductFormulationPage>();
      for (ProductFormulation main : productFormulationList) {
          ProductFormulationPage vo = new ProductFormulationPage();
          BeanUtils.copyProperties(main, vo);
          List<ProductFormulationDetail> productFormulationDetailList = productFormulationDetailService.selectByMainId(main.getId());
          vo.setProductFormulationDetailList(productFormulationDetailList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "product_formulation列表");
      mv.addObject(NormalExcelConstants.CLASS, ProductFormulationPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("product_formulation数据", "导出人:"+sysUser.getRealname(), "product_formulation"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("product:product_formulation:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<ProductFormulationPage> list = ExcelImportUtil.importExcel(file.getInputStream(), ProductFormulationPage.class, params);
              for (ProductFormulationPage page : list) {
                  ProductFormulation po = new ProductFormulation();
                  BeanUtils.copyProperties(page, po);
                  productFormulationService.saveMain(po, page.getProductFormulationDetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }




	 /**
	  * 启用停用
	  *
	  * @param
	  * @return
	  */
	 @Operation(summary = "变更配方状态")
	 @RequestMapping(value = "/editFormulationStatus", method = {RequestMethod.POST})
	 public Result<String> editOrderStatus(@RequestBody List<ProductFormulation> productFormulation) {
		 productFormulationService.updateStatus(productFormulation);
		 return Result.OK("变更成功!");
	 }


	 @Operation(summary = "发起审核")
	 @RequestMapping(value = "/initiateApproval", method = {RequestMethod.POST})
	 public Result<String> initiateApproval(@RequestBody ProductFormulation productFormulation, HttpServletRequest request) throws Exception {
		 productFormulationService.initiateApproval(productFormulation.getId(),request);
		 return Result.OK("发起审核成功!");
	 }

	 @Operation(summary = "审核通过")
	 @RequestMapping(value = "/approvalPass", method = {RequestMethod.POST})
	 public Result<String> approvalPass(@RequestBody ProductFormulation productFormulation, HttpServletRequest request) throws Exception {
		 productFormulationService.approvalPass(productFormulation,request);
		 return Result.OK("发起审核成功!");
	 }


	 @Operation(summary = "审核驳回")
	 @RequestMapping(value = "/reject", method = {RequestMethod.POST})
	 public Result<String> reject(@RequestBody ProductFormulation productFormulation, HttpServletRequest request) throws Exception {
		 productFormulationService.reject(productFormulation,request);
		 return Result.OK("驳回成功!");
	 }


	 /**
	  * 配方升级接口
	  *
	  * @param id
	  * @return
	  */
	 @Operation(summary="配方升级接口")
	 @GetMapping(value = "/formulaUpgrade")
	 public Result<?> formulaUpgrade(@RequestParam(name="id",required=true) String id) {
		 ProductFormulation productFormulation = productFormulationService.formulaUpgrade(id);
		 return Result.OK(productFormulation);

	 }


	 /**
	  * 配方复制
	  *
	  * @param id
	  * @return
	  */
	 @Operation(summary="配方复制接口")
	 @GetMapping(value = "/formulaCopy")
	 public Result<?> formulaCopy(@RequestParam(name="id",required=true) String id) {
		 ProductFormulation productFormulation = productFormulationService.formulaCopy(id);
		 return Result.OK(productFormulation);

	 }



	 /**
	  * 根据物料名称查询配方数据
	  *
	  * @param
	  * @return
	  */
	 @Operation(summary="根据物料名称查询配方数据")
	 @GetMapping(value = "/queryByMaterialName")
	 public Result<IPage<FormulationReplaceVo>> queryByMaterialName(ProductFormulation productFormulation,
																	@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
																	@RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
		 Page<FormulationReplaceVo> page = new Page<>(pageNo, pageSize);
		 IPage<FormulationReplaceVo> list = productFormulationService.queryByMaterialName(page, productFormulation.getMaterialName());
		 return Result.OK(list);

	 }



	 /**
	  * 根据物料名称查询配方数据
	  *
	  * @param materialName
	  * @return
	  */
	 @Operation(summary="根据物料名称查询配方数据map")
	 @GetMapping(value = "/queryListByMaterialName")
	 public Result<?> queryListByMaterialName(@RequestParam(name="materialName") String materialName) {
		 List<Map<String, String>> maps = productFormulationService.queryListByMaterialName(materialName);
		 return Result.OK(maps);

	 }



	 /**
	  * 替换主料
	  *
	  * @param replaceMainMaterialVO
	  * @return
	  */
	 @Operation(summary="替换主料")
	 @PostMapping(value = "/replaceMainMaterial")
	 public Result<?> replaceMainMaterial(@RequestBody ReplaceMainMaterialVO replaceMainMaterialVO) {
		  productFormulationService.replaceMainMaterial(replaceMainMaterialVO);
		 return Result.OK("替换完成");

	 }


	 /**
	  * 替换辅料
	  *
	  * @param replaceMainMaterialVO
	  * @return
	  */
	 @Operation(summary="替换辅料")
	 @PostMapping(value = "/replaceAuxiliaryMaterials")
	 public Result<?> replaceAuxiliaryMaterials(@RequestBody ReplaceMainMaterialVO replaceMainMaterialVO) {
		 productFormulationService.replaceAuxiliaryMaterials(replaceMainMaterialVO);
		 return Result.OK("替换完成");

	 }


	 /**
	  * 配方管理数据初始化
	  *
	  * @param
	  * @return
	  */
	 @Operation(summary="配方管理数据初始化")
	 @GetMapping(value = "/InitializeData")
	 public Result<?> InitializeData() {
		 productFormulationService.InitializeData();
		 return Result.OK("配方管理数据初始化完成");

	 }

	 /**
	  * 根据物料编码查询配方字典数据
	  *
	  * @param materialCode 物料编码
	  * @return 返回字典数据，key是material_code或fungible_material_codes，value是material_name或fungible_material_name
	  */
	 @Operation(summary="根据物料编码查询配方字典数据")
	 @GetMapping(value = "/api/queryFormulationDictByMaterialCode")
	 public Result<Map<String, String>> queryFormulationDictByMaterialCode(@RequestParam(name = "materialCode") String materialCode) {
		 Map<String, String> result = productFormulationService.queryFormulationDictByMaterialCode(materialCode);
		 return Result.OK(result);
	 }


	 // 工具方法：转义模糊查询中的特殊字符
	 public static String escapeLikeParam(String param) {
		 if (param == null) {
			 return null;
		 }
		 // 顺序不能乱：先转义反斜杠，再转义%和_
		 return param.replace("\\", "\\\\")
				 .replace("%", "\\%")
				 .replace("_", "\\_");
	 }



}
