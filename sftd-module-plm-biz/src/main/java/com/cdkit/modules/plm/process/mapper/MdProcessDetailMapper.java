package com.cdkit.modules.plm.process.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.plm.process.entity.MdProcessDetail;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 工序定义明细
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
public interface MdProcessDetailMapper extends BaseMapper<MdProcessDetail> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<MdProcessDetail>
   */
	public List<MdProcessDetail> selectByMainId(@Param("mainId") String mainId);
}
