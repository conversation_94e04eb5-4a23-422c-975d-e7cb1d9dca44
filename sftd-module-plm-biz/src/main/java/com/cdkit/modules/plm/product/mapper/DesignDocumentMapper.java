package com.cdkit.modules.plm.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.plm.product.entity.DesignDocument;
import com.cdkit.modules.plm.product.vo.req.QueryDesignDocumentVO;
import com.cdkit.modules.plm.product.vo.resp.OutboundDocumentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DesignDocumentMapper extends BaseMapper<DesignDocument> {
    Page<OutboundDocumentVO> outboundDocumentList(Page<OutboundDocumentVO> page, @Param("param") QueryDesignDocumentVO queryDesignDocumentVO);
}
