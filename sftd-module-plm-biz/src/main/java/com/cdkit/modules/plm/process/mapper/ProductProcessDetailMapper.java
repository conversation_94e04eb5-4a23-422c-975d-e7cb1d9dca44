package com.cdkit.modules.plm.process.mapper;

import java.util.List;

import com.cdkit.modules.plm.process.entity.ProductProcessDetail;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 产品工序步骤
 * @Author: cdkit-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
public interface ProductProcessDetailMapper extends BaseMapper<ProductProcessDetail> {

    /**
     * 删除工序步骤
     * @param processId 工序ID
     * @param productId 产品ID
     */
    void removeByProcessId(@Param("processId") String processId, @Param("productId") String productId);

    /**
     * 查询工序步骤
     * @param processId 工序ID
     */
    List<ProductProcessDetail> selectByProcessId(@Param("processId") String processId);
}
