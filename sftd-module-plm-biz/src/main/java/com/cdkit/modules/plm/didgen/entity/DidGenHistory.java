package com.cdkit.modules.plm.didgen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务单号生成历史标
 */
@Data
@TableName(value = "did_gen_history")
public class DidGenHistory implements Serializable {
    /**
     * 前缀key
     */
    @TableId(value = "prefix_key", type = IdType.ASSIGN_ID)
    private String prefixKey;

    /**
     * 编码
     */
    @TableField(value = "did_code")
    private String didCode;

    /**
     * 当前号码，下个号码为curr_num+1
     */
    @TableField(value = "curr_num")
    private Long currNum;

    /**
     * 生成时间
     */
    @TableField(value = "gen_time")
    private Date genTime;

    private static final long serialVersionUID = 1L;
}
