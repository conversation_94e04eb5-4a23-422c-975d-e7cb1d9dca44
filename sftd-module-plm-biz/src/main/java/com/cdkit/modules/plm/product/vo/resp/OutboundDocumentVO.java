package com.cdkit.modules.plm.product.vo.resp;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：OutboundDocumentVO
 * @Date：2024/4/9 09:41
 */
@Data
@Schema(description = "出库文档")
public class OutboundDocumentVO {
    private String id;
    @Schema(description ="名称")
    private String name;
    @Schema(description ="位置")
    private String path;
    @Schema(description ="位置中文")
    private String pathDesc;
    @Schema(description ="文档类型")
    private String fileType;
    @Schema(description ="出库人")
    private String lockedBy;
    @Schema(description ="出库时间")
    private String lockedTime;
    @Schema(description ="cad docId")
    private String docId;
}
