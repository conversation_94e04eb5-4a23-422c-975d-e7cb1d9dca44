package com.cdkit.modules.plm.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.common.workflow.WorkFlowUtil;
import com.cdkit.common.workflow.entity.WorkflowProcessEntity;
import com.cdkit.md.api.IMdMaterialTypeApi;
import com.cdkit.md.entity.MdMaterialType;
import com.cdkit.modules.plm.common.Constants;
import com.cdkit.modules.plm.enums.FactoryEnum;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.enums.ProductTreeTypeEnum;
import com.cdkit.modules.plm.process.service.IFactoryStrategy;
import com.cdkit.modules.plm.product.entity.ProductFormulation;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.enums.FormulaStatusEnum;
import com.cdkit.modules.plm.product.mapper.ProductFormulationMapper;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/31
 */
@Component(FactoryEnum.YHC_BEAN_NAME)
@Slf4j
public class YhcFactoryStrategyImpl implements IFactoryStrategy {

    @Resource
    @Lazy
    private IProductTreeService productTreeService;

    @Resource
    private IMdMaterialTypeApi mdMaterialTypeApi;

    @Resource
    private ProductFormulationMapper productFormulationMapper;

    @Resource
    WorkFlowUtil workFlowUtil;
    /**
     * 查询产品档案列表
     *
     * @return 返回结果
     */
    @Override
    public List<ProductTree> getProductFileList(String materialCodeOrName) {
        return productTreeService.list(new LambdaQueryWrapper<ProductTree>()
                .eq(ProductTree::getConverted, 1)
                .eq(ProductTree::getType, ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode())
                .eq(ProductTree::getEnable,0));
    }

    @Override
    public void createTree(ProductFormulation productFormulation) {
        String Fname = FactoryEnum.YHC.getName();
        //1-1创建产品分类
        ProductTree productTree = new ProductTree();
        productTree.setName(Fname);
        productTree.setType(1);
        productTree.setPid("0");
        productTree.setCode(FactoryEnum.YHC_BEAN_NAME);
        productTree.setSort(String.valueOf(1));
        productTree.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        productTree.setConverted(1);
        productTree.setDelFlag(0);
        productTree.setEnable(0);
        productTreeService.save(productTree);
        productTree.setPath("0"+ Constants.PATH_SPILT+ productTree.getId() + Constants.PATH_SPILT);
        productTree.setSort("1");
        productTreeService.updateById(productTree);

        //1-2创建产品种类

        ProductTree pt = new ProductTree();
        MdMaterialType mdMaterialType = mdMaterialTypeApi.queryMaterialType(productFormulation.getMaterialTypeId());
        pt.setName(mdMaterialType.getTypeName());
        pt.setType(2);
        pt.setPid(productTree.getId());
        pt.setCode(mdMaterialType.getId());
        pt.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        pt.setConverted(1);
        pt.setDelFlag(0);
        pt.setEnable(0);
        productTreeService.save(pt);
        pt.setPath(productTree.getPath()+ pt.getId()+ Constants.PATH_SPILT);
        pt.setSort(pt.getId());
        productTreeService.updateById(pt);

    }

    @Override
    public BigDecimal getAssembleQuantity() {
        return BigDecimal.valueOf(1);
    }

    @Override
    public void createApproval(String productFormulationId, HttpServletRequest request) {
        initiateApproval(productFormulationId, request);
    }

    @Transactional(rollbackFor = Exception.class)
    public void initiateApproval(String productFormulationId, HttpServletRequest request)  {
        ProductFormulation pf = productFormulationMapper.selectById(productFormulationId);
        List<WorkflowProcessEntity> processEntities = null;
        try {
            processEntities = workFlowUtil.getMyStartProcess(request);
            WorkflowProcessEntity entity = processEntities.stream().filter(it -> "配方审核".equals(it.getAppName())).findFirst().orElse(null);
            if (entity != null) {

                //todo 流程标题暂时不确认是否用文件名
                String	wiid = workFlowUtil.startWorkflow(entity.getAppId(), pf.getId(), "配方审核", "", request);
                //log.info( "审批发起流程完成:{}", wiid);
                pf.setWiid(wiid);
                pf.setFormulaStatus(FormulaStatusEnum.INREVIEW.getCode());
                productFormulationMapper.updateById(pf);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
