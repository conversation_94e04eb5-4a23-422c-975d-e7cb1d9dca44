package com.cdkit.modules.plm.process.service.impl;

import com.cdkit.modules.plm.process.entity.MdProcess;
import com.cdkit.modules.plm.process.entity.MdProcessRouteDetail;
import com.cdkit.modules.plm.process.mapper.MdProcessMapper;
import com.cdkit.modules.plm.process.mapper.MdProcessRouteDetailMapper;
import com.cdkit.modules.plm.process.service.IMdProcessRouteDetailService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 工艺路线明细
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Service
public class MdProcessRouteDetailServiceImpl extends ServiceImpl<MdProcessRouteDetailMapper, MdProcessRouteDetail> implements IMdProcessRouteDetailService {

	@Autowired
	private MdProcessRouteDetailMapper mdProcessRouteDetailMapper;
	@Resource
	private MdProcessMapper mdProcessMapper;

	@Override
	public List<MdProcessRouteDetail> selectByMainId(String mainId) {
		List<MdProcessRouteDetail> mdProcessRouteDetails = mdProcessRouteDetailMapper.selectByMainId(mainId);
		List<String> list = mdProcessRouteDetails.stream().map(MdProcessRouteDetail::getProcessId).toList();
		// 查询工序列表
		List<MdProcess> mdProcesses = mdProcessMapper.selectBatchIds(list);
		Map<String, String> processNameMap = mdProcesses.stream().collect(
				Collectors.toMap(MdProcess::getId, MdProcess::getProcessName));
		for (MdProcessRouteDetail mdProcessRouteDetail : mdProcessRouteDetails) {
			mdProcessRouteDetail.setProcessName(processNameMap.get(mdProcessRouteDetail.getProcessId()));
		}
		return mdProcessRouteDetails;
	}
}
