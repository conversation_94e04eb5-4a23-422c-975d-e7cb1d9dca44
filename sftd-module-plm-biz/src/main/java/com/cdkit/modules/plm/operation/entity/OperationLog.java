package com.cdkit.modules.plm.operation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.cdkitframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: operation_log
 * @Author: mc
 * @Date:   2024-05-14
 * @Version: V1.0
 */
@Data
@TableName("operation_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="operation_log对象", name="operation_log")
public class OperationLog implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
	/**操作人*/
	@Excel(name = "操作人", width = 15)
    @Schema(description = "操作人")
    private String name;
	/**操作类型*/
	@Excel(name = "操作类型", width = 15)
    @Schema(description = "操作类型")
    private String type;
    /**操作内容*/
    @Excel(name = "操作内容", width = 15)
    @Schema(description = "操作内容")
    private String content;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
}
