package com.cdkit.modules.plm.product.entity;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 产品树
 */
@Schema(description = "产品树")
@Data
@TableName(value = "product_tree")
public class ProductTree implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    @Size(max = 36, message = "id最大长度要小于 36")
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 名称
     */
    @TableField(value = "`name`")
    @Schema(description = "名称")
    @Size(max = 255, message = "名称最大长度要小于 255")
    private String name;

    /**
     * 产品类型：产品分类1、产品种类2、总装3、零部件4、设计文件5
     */
    @TableField(value = "`type`")
    @Schema(description = "产品类型：产品分类1、产品种类2、总装3、零部件4、设计文件5")
    @NotNull(message = "产品类型：产品分类1、产品种类2、总装3、零部件4、设计文件5不能为null")
    private Integer type;

    /**
     * 编码/代号
     */
    @TableField(value = "code")
    @Schema(description = "编码/代号")

    @Size(max = 255, message = "编码/代号最大长度要小于 255")
    private String code;

    /**
     * 物料编码
     */
    @Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Schema(description = "描述")

    @Size(max = 800, message = "描述最大长度要小于 800")
    private String description;

    /**
     * 产品树所属工厂（例如加工制造）
     */
    @TableField(value = "belong_factory")
    @Schema(description = "产品树所属工厂（例如加工制造）")

    @Size(max = 50, message = "产品树所属工厂（例如加工制造）最大长度要小于 50")
    private String belongFactory;

    /**
     * 父id
     */
    @TableField(value = "pid")
    @Schema(description = "父id")
    @Size(max = 32, message = "父id最大长度要小于 32")
    private String pid;

    /**
     * 存储id路径，用于like查询子节点
     */
    @TableField(value = "`path`")
    @Schema(description = "存储id路径，用于like查询子节点")
    @Size(max = 255, message = "存储id路径，用于like查询子节点最大长度要小于 255")
    private String path;

    /**
     * 原件id（总装、零部件、图纸）
     */
    @TableField(value = "source_id")
    @Schema(description = "原件id（总装、零部件、图纸）")
    @Size(max = 36, message = "原件id（总装、零部件、图纸）最大长度要小于 36")
    private String sourceId;

    /**
     * 排序号
     */
    @TableField(value = "sort")
    @Schema(description = "排序号")

    @Size(max = 50, message = "排序号最大长度要小于 50")
    private String sort;

    /**
     * 是否已转换到工艺树，产品大类和产品默认为true
     */
    @TableField(value = "converted")

    @Schema(description = "是否已转换到工艺树，产品大类和产品默认为true")
    private Integer converted = 0;

    /**
     * 状态(产品大类的该字段为空)、设计1、发布2、入库3、出库4、归档5
     */
    @TableField(value = "`status`")

    @Schema(description = "状态(产品大类的该字段为空)、设计1、发布2、入库3、出库4、归档5、提交并签名6、审核中7")
    private Integer status;

    /**
     * 节点类型：1产品树 2工艺树
     */
    @TableField(value = "node_type")

    @Schema(description = "节点类型：1产品树 2工艺树")
    private Integer nodeType = 1;

    /**
     * 层级号
     */
    @TableField(value = "level")

    @Schema(description = "层级号")
    private Integer level;

    /**
     * 是否删除
     */
    @TableField(value = "del_flag")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @Schema(description = "创建人")

    @Size(max = 50, message = "创建人最大长度要小于 50")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")

    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    @Schema(description = "更新人")

    @Size(max = 50, message = "更新人最大长度要小于 50")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")

    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 所属部门编码
     */
    @TableField(value = "sys_org_code")
    @Schema(description = "所属部门编码")

    @Size(max = 64, message = "所属部门编码最大长度要小于 64")
    private String sysOrgCode;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    @Schema(description = "租户号")
    @Size(max = 32, message = "租户号最大长度要小于 32")

    private String tenantId;

    /**
     * 工艺树来自于产品树ID
     */
    @TableField(value = "converted_from_id")
    @Schema(description = "工艺树来自于产品树ID")
    private String convertedFromId;


    private static final long serialVersionUID = 1L;

    // 绑定的生产订单号
    private String bindingProductOrderNum;
    private String copyFromId;


    private String remark;

    private Integer enable;
}
