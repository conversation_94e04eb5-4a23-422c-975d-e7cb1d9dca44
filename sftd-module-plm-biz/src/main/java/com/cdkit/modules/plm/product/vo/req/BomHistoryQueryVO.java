package com.cdkit.modules.plm.product.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import com.cdkit.modules.plm.enums.NodeTypeEnum;

import lombok.Data;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：BomHistoryQueryVO
 * @Date：2024/4/5 23:59
 */
@Schema(description = "bom清单查询")
@Data
public class BomHistoryQueryVO {
    @Schema(description = "名称")
    private String name;
    @Schema(description = "节点类型")
    private Integer NodeType = NodeTypeEnum.PRODUCT_TREE.getCode();
    @Schema(description = "节点id")
    private String productTreeId;
}
