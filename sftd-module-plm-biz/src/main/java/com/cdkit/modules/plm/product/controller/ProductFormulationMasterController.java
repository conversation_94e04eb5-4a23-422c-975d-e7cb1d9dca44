package com.cdkit.modules.plm.product.controller;

import java.util.Arrays;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.plm.product.entity.ProductFormulationMaster;
import com.cdkit.modules.plm.product.service.IProductFormulationMasterService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.query.QueryGenerator;
import lombok.extern.slf4j.Slf4j;
import com.cdkit.common.system.base.controller.CdkitController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.cdkit.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 zhaoyang
 * @Description: product_formulation_master
 * @Author: cdkit-boot
 * @Date:   2025-06-10
 * @Version: V1.0
 */
@Tag(name="product_formulation_master")
@RestController
@RequestMapping("/product/productFormulationMaster")
@Slf4j
public class ProductFormulationMasterController extends CdkitController<ProductFormulationMaster, IProductFormulationMasterService> {
	@Autowired
	private IProductFormulationMasterService productFormulationMasterService;
	
	/**
	 * 分页列表查询
	 *
	 * @param productFormulationMaster
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "product_formulation_master-分页列表查询")
	@Operation(summary="product_formulation_master-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ProductFormulationMaster>> queryPageList(ProductFormulationMaster productFormulationMaster,
																 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
																 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
																 HttpServletRequest req) {
		QueryWrapper<ProductFormulationMaster> queryWrapper = QueryGenerator.initQueryWrapper(productFormulationMaster, req.getParameterMap());
		Page<ProductFormulationMaster> page = new Page<ProductFormulationMaster>(pageNo, pageSize);
		IPage<ProductFormulationMaster> pageList = productFormulationMasterService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param productFormulationMaster
	 * @return
	 */
	@AutoLog(value = "product_formulation_master-添加")
	@Operation(summary="product_formulation_master-添加")
	@RequiresPermissions("product:product_formulation_master:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ProductFormulationMaster productFormulationMaster) {
		productFormulationMasterService.save(productFormulationMaster);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param productFormulationMaster
	 * @return
	 */
	@AutoLog(value = "product_formulation_master-编辑")
	@Operation(summary="product_formulation_master-编辑")
	@RequiresPermissions("product:product_formulation_master:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ProductFormulationMaster productFormulationMaster) {
		productFormulationMasterService.updateById(productFormulationMaster);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "product_formulation_master-通过id删除")
	@Operation(summary="product_formulation_master-通过id删除")
	@RequiresPermissions("product:product_formulation_master:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		productFormulationMasterService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "product_formulation_master-批量删除")
	@Operation(summary="product_formulation_master-批量删除")
	@RequiresPermissions("product:product_formulation_master:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.productFormulationMasterService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "product_formulation_master-通过id查询")
	@Operation(summary="product_formulation_master-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ProductFormulationMaster> queryById(@RequestParam(name="id",required=true) String id) {
		ProductFormulationMaster productFormulationMaster = productFormulationMasterService.getById(id);
		if(productFormulationMaster==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(productFormulationMaster);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param productFormulationMaster
    */
    @RequiresPermissions("product:product_formulation_master:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProductFormulationMaster productFormulationMaster) {
        return super.exportXls(request, productFormulationMaster, ProductFormulationMaster.class, "product_formulation_master");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("product:product_formulation_master:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProductFormulationMaster.class);
    }

}
