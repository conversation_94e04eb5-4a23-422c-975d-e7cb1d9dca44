package com.cdkit.modules.plm.process.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.aspect.annotation.AutoLog;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.process.entity.ProductProcessRoute;
import com.cdkit.modules.plm.process.service.IProductProcessRouteService;
import com.cdkit.modules.plm.process.vo.req.MdProcessVo;
import com.cdkit.modules.plm.process.vo.req.ReqProductProcessRouteVo;
import com.cdkit.modules.plm.process.vo.resp.RespProcessVO;
import com.cdkit.modules.plm.product.vo.resp.ProductFileVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @Description: 产品工艺路线表
 * @Author: cdkit-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
@Tag(name = "产品工艺路线表")
@RestController
@RequestMapping("/process/productProcessRoute")
@Slf4j
public class ProductProcessRouteController extends CdkitController<ProductProcessRoute, IProductProcessRouteService> {
    @Resource
    private IProductProcessRouteService productProcessRouteService;

    /**
     * 导入工艺路线
     *
     * @param reqProductProcessRouteVo 请求参数
     * @return 返回结果
     */
    @Operation(summary = "导入工艺路线", description = "导入工艺路线")
    @PostMapping(value = "/importProcessRoute")
    public Result<String> importProcessRoute(@RequestBody ReqProductProcessRouteVo reqProductProcessRouteVo) {
        productProcessRouteService.importProcessRoute(reqProductProcessRouteVo);
        return Result.OK("导入成功！");
    }

    /**
     * 查询节点对应工艺路线
     *
     * @param id 节点ID
     * @return 返回结果
     */
    @Operation(summary = "查询节点对应工艺路线", description = "查询节点对应工艺路线")
    @GetMapping(value = "/listProcessRoute")
    public Result<List<ProductProcessRoute>> listProcessRoute(@RequestParam(name = "id") String id) {
        List<ProductProcessRoute> list = productProcessRouteService.listProcessRoute(id);
        return Result.OK(list);
    }

    /**
     * 查询节点对应工艺
     *
     * @param id 节点ID
     * @return 返回结果
     */
    @Operation(summary = "查询节点对应工艺", description = "查询节点对应工艺")
    @GetMapping(value = "/getProcessDetail")
    public Result<RespProcessVO> getProcessDetail(@RequestParam(name = "id") String id) {
        RespProcessVO process = productProcessRouteService.getProcessDetail(id);
        return Result.OK(process);
    }

    /**
     * 保存节点对应工艺内容或步骤
     *
     * @param respProcessVO 请求参数
     * @return 返回结果
     */
    @Operation(summary = "保存节点对应工艺内容或步骤", description = "保存节点对应工艺内容或步骤")
    @PostMapping(value = "/saveProcessDetail")
    public Result<String> saveProcessDetail(@RequestBody RespProcessVO respProcessVO) {
        productProcessRouteService.saveProcessDetail(respProcessVO);
        return Result.OK("导入成功！");
    }


    /**
     * 通过产品编码或代号查询产品档案
     *
     * @param code 产品编码或代号
     * @return 返回结果
     */
    @Operation(summary = "通过产品编码查询产品档案", description = "通过产品编码查询产品档案")
    @GetMapping(value = "/getProductFile")
    public Result<ProductFileVO> getProductFile(@RequestParam(name = "code") String code) {
        ProductFileVO vo = productProcessRouteService.getProductFile(code);
        if (vo == null) {
            return Result.OK();
        }
        return Result.OK(vo);
    }

    /**
     * 工艺类型新增
     *
     * @return 返回结果
     */
    @AutoLog(value = "工艺-新增")
    @Operation(summary = "工艺-新增", description = "工艺-新增")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody @Validated MdProcessVo mdProcessVo) {
        productProcessRouteService.add(mdProcessVo);
        return Result.OK("添加成功！");
    }

    /**
     * 通过产品代号查询产品档案
     *
     * @param code 产品代号
     * @return 返回结果
     */
    @Operation(summary = "通过产品代号查询产品档案", description = "通过产品代号查询产品档案")
    @GetMapping(value = "/getProductFileByCode")
    public Result<ProductFileVO> getProductFileByCode(@RequestParam(name = "code") String code) {
        ProductFileVO vo = productProcessRouteService.getProductFileByCode(code);
        if (vo == null) {
            return Result.OK();
        }
        return Result.OK(vo);
    }

}
