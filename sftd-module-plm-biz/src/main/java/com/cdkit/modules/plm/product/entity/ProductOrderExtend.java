package com.cdkit.modules.plm.product.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkit.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: product_order_extend
 * @Author: cdkit-boot
 * @Date:   2025-06-23
 * @Version: V1.0
 */
@Data
@TableName("product_order_extend")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="product_order_extend对象")
public class ProductOrderExtend implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
	/**自定义键*/
	@Excel(name = "自定义键", width = 15)
    @Schema(description = "自定义键")
    private String definedKey;
	/**自定义值*/
	@Excel(name = "自定义值", width = 15)
    @Schema(description = "自定义值")
    private String definedValue;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "创建时间")
    private Date createTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private String tenantId;
	/**订单号id*/
	@Excel(name = "订单号id", width = 15)
    @Schema(description = "订单号id")
    private String productOrderId;
	/**删除标志*/
	@Excel(name = "删除标志", width = 15)
    @Schema(description = "删除标志")
    @TableLogic
    private Integer delFlag;
	/**自定义名称*/
	@Excel(name = "自定义名称", width = 15)
    @Schema(description = "自定义名称")
    private String definedName;
	/**自定义类型：1扩展字段 2个性化定制字段*/
	@Excel(name = "自定义类型：1扩展字段 2个性化定制字段", width = 15)
    @Schema(description = "自定义类型：1扩展字段 2个性化定制字段")
    private String definedType;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @Schema(description = "排序")
    private Double sort;
	/**数据类型 string datetime int BigDecimal*/
	@Excel(name = "数据类型 string datetime int BigDecimal", width = 15)
    @Schema(description = "数据类型 string datetime int BigDecimal")
    private String dbType;
	/**控件类型 text switch*/
	@Excel(name = "控件类型 text switch", width = 15)
    @Schema(description = "控件类型 text switch")
    private String fieldShowType;
	/**是否只读 1是 0否*/
	@Excel(name = "是否只读 1是 0否", width = 15)
    @Schema(description = "是否只读 1是 0否")
    private Integer readOnly;
	/**是否必填 1是 0否*/
	@Excel(name = "是否必填 1是 0否", width = 15)
    @Schema(description = "是否必填 1是 0否")
    private Integer mustFlag;
	/**是否在列表中显示 1是 0否*/
	@Excel(name = "是否在列表中显示 1是 0否", width = 15)
    @Schema(description = "是否在列表中显示 1是 0否")
    private Integer requireFlag;
	/**扩展类型： 1.配方管理*/
	@Excel(name = "扩展类型： 1.配方管理", width = 15)
    @Schema(description = "扩展类型： 1.配方管理")
    private String extendType;
}
