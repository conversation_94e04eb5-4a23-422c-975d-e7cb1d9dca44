package com.cdkit.modules.plm.process.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;

import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 产品工序步骤
 * @Author: cdkit-boot
 * @Date:   2024-08-06
 * @Version: V1.0
 */
@Data
@TableName("product_process_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="product_process_detail对象", name="产品工序步骤")
public class ProductProcessDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键")
    private String id;
	/**产品ID*/
	@Excel(name = "产品ID", width = 15)
    @Schema(description = "产品ID")
    private String productId;
	/**工序ID*/
	@Excel(name = "工序ID", width = 15)
    @Schema(description = "工序ID")
    private String processId;
	/**序号*/
	@Excel(name = "序号", width = 15)
    @Schema(description = "序号")
    private Integer number;
    /**步骤编码*/
    @Schema(description = "步骤编码")
    private String stepCode;
	/**步骤名称*/
	@Excel(name = "步骤名称", width = 15)
    @Schema(description = "步骤名称")
    private String stepName;
	/**步骤内容*/
	@Excel(name = "步骤内容", width = 15)
    @Schema(description = "步骤内容")
    private String stepContent;
    /**pid*/
    @Excel(name = "pid", width = 15)
    @Schema(description = "pid")
    private String pid;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
    /**作业结果*/
    @Schema(description = "作业结果")
    private String pdo;
    /**最新记录*/
    @Schema(description = "最新记录")
    private Integer isNew;
    /**备注*/
    @Schema(description = "备注")
    private String remark;
}
