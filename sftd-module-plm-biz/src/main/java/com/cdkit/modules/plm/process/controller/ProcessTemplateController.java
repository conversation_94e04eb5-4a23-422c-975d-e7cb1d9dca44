package com.cdkit.modules.plm.process.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.aspect.annotation.AutoLog;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.process.entity.ProcessTemplate;
import com.cdkit.modules.plm.process.entity.ProcessType;
import com.cdkit.modules.plm.process.service.IProcessTemplateService;
import com.cdkit.modules.plm.process.service.IProcessTypeService;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTemplateAddVO;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTemplateDesignVO;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTemplateEditVO;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTemplateQueryVO;
import com.cdkit.modules.plm.process.vo.resp.RespProcessTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 工艺模板
 * @Author: mc
 * @Date:   2024-04-01
 * @Version: V1.0
 */
@Tag(name="工艺模板")
@RestController
@RequestMapping("/processTemplate")
@Slf4j
public class ProcessTemplateController extends CdkitController<ProcessTemplate, IProcessTemplateService> {
	@Autowired
	private IProcessTemplateService processTemplateService;
	@Autowired
	private IProcessTypeService processTypeService;

	/**
	 * 工艺模板分页列表查询
	 *
	 * @param reqProcessTemplateQueryVO 工艺模板查询参数
	 * @param pageNo 页码
	 * @param pageSize 每页条数
	 * @param req HttpServletRequest
	 * @return 工艺模板列表
	 */
	@Operation(summary="工艺模板分页列表查询", description="工艺模板分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ProcessTemplate>> queryPageList(ReqProcessTemplateQueryVO reqProcessTemplateQueryVO,
														@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
														@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
														HttpServletRequest req) {
		LambdaQueryWrapper<ProcessTemplate> queryWrapper  = new LambdaQueryWrapper<>();
		queryWrapper.eq(StringUtils.isNotBlank(reqProcessTemplateQueryVO.getProcessTypeId()),ProcessTemplate::getProcessTypeId, reqProcessTemplateQueryVO.getProcessTypeId());
		queryWrapper.like(StringUtils.isNotBlank(reqProcessTemplateQueryVO.getTemplateName()),ProcessTemplate::getTemplateName, reqProcessTemplateQueryVO.getTemplateName());
		Page<ProcessTemplate> page = new Page<>(pageNo, pageSize);
		IPage<ProcessTemplate> pageList = processTemplateService.page(page, queryWrapper);
		List<ProcessTemplate> records = pageList.getRecords();
		for (ProcessTemplate item : records) {
			ProcessType processType = Optional.ofNullable(processTypeService.getById(item.getProcessTypeId())).orElse(new ProcessType());
			item.setProcessTypeName(processType.getName());
		}
		return Result.OK(pageList);
	}

	/**
	 * 新增工艺模板
	 *
	 * @param reqProcessTemplateAddVO 请求参数
	 * @return 返回结果
	 */
	@AutoLog(value = "新增工艺模板")
	@Operation(summary="新增工艺模板", description="新增工艺模板")
	//@RequiresPermissions("process:processTemplate:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody @Validated ReqProcessTemplateAddVO reqProcessTemplateAddVO) {
		processTemplateService.add(reqProcessTemplateAddVO);
		return Result.OK("添加成功！");
	}

	/**
	 *  修改工艺模板
	 *
	 * @param reqProcessTemplateEditVO 请求参数
	 * @return 返回结果
	 */
	@AutoLog(value = "修改工艺模板")
	@Operation(summary="修改工艺模板", description="修改工艺模板")
	//@RequiresPermissions("process:processTemplate:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT})
	public Result<String> edit(@RequestBody @Validated ReqProcessTemplateEditVO reqProcessTemplateEditVO) {
		processTemplateService.edit(reqProcessTemplateEditVO);
		return Result.OK("编辑成功!");
	}

	 /**
	  *  工艺模板设计
	  *
	  * @param reqProcessTemplateDesignVO 请求参数
	  * @return 返回结果
	  */
	 @AutoLog(value = "工艺模板设计")
	 @Operation(summary="工艺模板设计", description="工艺模板设计")
	 //@RequiresPermissions("process:processTemplate:design")
	 @RequestMapping(value = "/design", method = {RequestMethod.POST})
	 public Result<String> design(@RequestBody @Validated ReqProcessTemplateDesignVO reqProcessTemplateDesignVO) {
		 ProcessTemplate processTemplate = BeanUtil.toBean(reqProcessTemplateDesignVO, ProcessTemplate.class);
		 processTemplateService.updateById(processTemplate);
		 return Result.OK("设计成功!");
	 }

	/**
	 *  通过id删除
	 *
	 * @param id 主键ID
	 * @return 返回结果
	 */
	@AutoLog(value = "通过id删除")
	@Operation(summary="通过id删除", description="通过id删除")
	//@RequiresPermissions("process:processTemplate:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id") String id) {
		processTemplateService.deleteById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids 主键ID
	 * @return 返回结果
	 */
	@AutoLog(value = "工艺模板-批量删除")
	@Operation(summary="工艺模板-批量删除", description="工艺模板-批量删除")
	//@RequiresPermissions("process:processTemplate:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids") String ids) {
		this.processTemplateService.deleteByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id 主键ID
	 * @return 返回结果
	 */
	@Operation(summary="工艺模板-通过id查询", description="工艺模板-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ProcessTemplate> queryById(@RequestParam(name="id") String id) {
		ProcessTemplate processTemplate = processTemplateService.getById(id);
		if(processTemplate == null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(processTemplate);
	}

	 /**
	  * 按工艺类型查询工艺模板
	  *
	  * @return 返回结果
	  */
	 @Operation(summary="按工艺类型查询工艺模板", description="按工艺类型查询工艺模板")
	 @GetMapping(value = "/listGroupByType")
	 public Result<List<RespProcessTemplateVO>> listGroupByType() {
		 List<RespProcessTemplateVO> processTemplate = processTemplateService.listGroupByType();
		 if(processTemplate.size() == 0) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(processTemplate);
	 }
}
