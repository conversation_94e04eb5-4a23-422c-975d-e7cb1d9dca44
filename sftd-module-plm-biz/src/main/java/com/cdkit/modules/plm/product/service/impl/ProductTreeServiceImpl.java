package com.cdkit.modules.plm.product.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jodd.io.FileUtil;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.util.Assert;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.system.api.ISysBaseAPI;
import com.cdkit.common.system.vo.LoginUser;
import com.cdkit.common.workflow.WorkFlowUtil;
import com.cdkit.common.workflow.entity.WorkflowProcessAction;
import com.cdkit.common.workflow.entity.WorkflowProcessEntity;
import com.cdkit.common.workflow.entity.WorkflowStatusEntity;
import com.cdkit.md.api.IMdMaterialApi;
import com.cdkit.md.entity.MdMaterialExtend;
import com.cdkit.md.entity.MdMaterialPage;
import com.cdkit.modules.plm.common.CommonUtil;
import com.cdkit.modules.plm.common.Constants;
import com.cdkit.modules.plm.common.LoginUtil;
import com.cdkit.modules.plm.enums.*;
import com.cdkit.modules.plm.operation.service.IOperationLogService;
import com.cdkit.modules.plm.process.entity.ProductProcessRoute;
import com.cdkit.modules.plm.process.service.IProductProcessRouteService;
import com.cdkit.modules.plm.process.vo.req.ReqProductProcessRouteVo;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.dto.WriteSignDTO;
import com.cdkit.modules.plm.product.entity.*;
import com.cdkit.modules.plm.product.event.DwgSyncEvent;
import com.cdkit.modules.plm.product.mapper.DesignDocumentMapper;
import com.cdkit.modules.plm.product.mapper.ProductPartMapper;
import com.cdkit.modules.plm.product.mapper.ProductTreeMapper;
import com.cdkit.modules.plm.product.service.*;
import com.cdkit.modules.plm.product.thirdparty.zwcad.CadFeign;
import com.cdkit.modules.plm.product.thirdparty.zwcad.dto.CadResultDTO;
import com.cdkit.modules.plm.product.thirdparty.zwcad.dto.UploadRespDTO;
import com.cdkit.modules.plm.product.vo.req.*;
import com.cdkit.modules.plm.product.vo.resp.*;
import com.cdkit.modules.plm.util.CustomMinioUtil;
import com.cdkit.modules.plm.util.DidUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: product_tree
 * @Author: zhao yang
 * @Date: 2024-03-26
 * @Version: V1.0
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProductTreeServiceImpl extends ServiceImpl<ProductTreeMapper, ProductTree> implements IProductTreeService {

    private final IDesignDocumentService designDocumentService;
    private final IProductPartService productPartService;
    private final ProductPartMapper productPartMapper;
    private final DesignDocumentMapper designDocumentMapper;
    private final DesignDocumentHistoryService designDocumentHistoryService;
    private final ApplicationEventPublisher eventPublisher;
    private final CadHandleService cadHandleService;
    private final CadFeign cadFeign;
    private final IProductPartExtendService productPartExtendService;
    private final IOperationLogService operationLogUtil;
    private final IMdMaterialApi mdMaterialApi;
    private final ISysBaseAPI sysBaseApi;
    @Resource
    private IProductProcessRouteService productProcessRouteService;
    @Autowired
    private ProductTreeMapper productTreeMapper;
    @Resource
    WorkFlowUtil workFlowUtil;

    @Override
    public List<ProductTreeDTO> tree(List<String> rootIds, NodeTypeEnum nodeType, String materialCodeOrName) {
        return handleTree(rootIds, nodeType, materialCodeOrName);
    }

    private List<ProductTreeDTO> handleTree(List<String> rootIds, NodeTypeEnum nodeType, String materialCodeOrName) {
        //根节点，不传查询整个树形
        List<ProductTree> rootNodes;
        if (CollUtil.isEmpty(rootIds)) {
            //产品树、工艺树共用产品分类、种类
            LambdaQueryWrapper<ProductTree> wrapper = new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getPid, Constants.ROOT_PID).isNull(ProductTree::getBindingProductOrderNum);
            rootNodes = this.list(wrapper);
        } else {
            rootNodes = this.listByIds(rootIds);
        }
        if (CollUtil.isEmpty(rootNodes)) {
            return Collections.emptyList();
        }
        for (int i = rootNodes.size() - 1; i >= 0; i--) {
            if (CollectionUtils.isEmpty(rootIds)) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(rootNodes.get(i).getBindingProductOrderNum())) {
                    //复制来的
                    rootNodes.remove(i);
                    continue;
                }
            }

            ProductTree rootNode = rootNodes.get(i);
            if (rootNode.getSourceId() != null) {
                rootNodes.set(i, this.getById(rootNode.getSourceId()));
            }
        }
        List<String> rootNodesIds = rootNodes.stream().map(ProductTree::getId).collect(Collectors.toList());
        Map<String, ProductPart> rootProductPartMap;
        if (CollUtil.isNotEmpty(rootNodesIds)) {
            List<ProductPart> rootProductPartList = productPartService.listByIds(rootNodesIds);
            rootProductPartMap = rootProductPartList.stream().collect(Collectors.toMap(ProductPart::getId, Function.identity()));
        } else {
            rootProductPartMap = Collections.emptyMap();
        }

        Map<String, DesignDocument> designDocumentMap;
        List<String> designDocumentIds = rootNodes.stream().filter(x -> ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode().equals(x.getType())).map(ProductTree::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(designDocumentIds)) {
            List<DesignDocument> designDocuments = designDocumentService.listByIds(designDocumentIds);
            designDocumentMap = designDocuments.stream().collect(Collectors.toMap(DesignDocument::getId, Function.identity()));
        } else {
            designDocumentMap = Collections.emptyMap();
        }


        //本次树形的所有节点
        boolean rootIdsSize = true;
        if(CollectionUtils.isEmpty(rootIds)){
            rootIdsSize = false;
        }
        //todo 此方法可以跟selectTreeList合并
        List<ProductTreeDTO> productTreeDTOList = productTreeMapper.selectTreeListAll(ProductTreeTypeEnum.PRODUCT_TYPE.getCode(),ProductTreeTypeEnum.PRODUCT_KIND.getCode(),ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode(),ProductTreeTypeEnum.PRODUCT_PART.getCode(),nodeType.getCode(),materialCodeOrName,rootIdsSize);



        if (null != materialCodeOrName && !materialCodeOrName.isEmpty()) {
            List<String> partIds = productTreeDTOList.stream().map(ProductTree::getPath).collect(Collectors.toList());
            if (null != partIds && !partIds.isEmpty()) {
                //查询所有的父节点 遍历减少in的无用查询
                String partIdStr = partIds.stream().flatMap(partId -> Arrays.stream(partId.split(","))).collect(Collectors.toList()).stream().distinct().collect(Collectors.joining(","));;
                List<ProductTreeDTO> partIdsDistinct = productTreeMapper.selectTreeListByIds(ProductTreeTypeEnum.PRODUCT_TYPE.getCode(),ProductTreeTypeEnum.PRODUCT_KIND.getCode(),ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode(),ProductTreeTypeEnum.PRODUCT_PART.getCode(),partIdStr,nodeType.getCode(),materialCodeOrName,rootIdsSize);

                List<ProductTreeDTO> missingNodesDTO = partIdsDistinct.stream()
                        .filter(node -> !productTreeDTOList.contains(node))
                        .collect(Collectors.toList());
                productTreeDTOList.addAll(missingNodesDTO);

                List<String> filterProductTreeIdList = filterProductTree(productTreeDTOList, productTreeDTOList);
                String filterProductTreeIdStr = filterProductTreeIdList.stream().flatMap(partId -> Arrays.stream(partId.split(","))).collect(Collectors.toList()).stream().distinct().collect(Collectors.joining(","));;
                List<ProductTreeDTO> filterProductTreeList = productTreeMapper.selectTreeListByIds(ProductTreeTypeEnum.PRODUCT_TYPE.getCode(),ProductTreeTypeEnum.PRODUCT_KIND.getCode(),ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode(),ProductTreeTypeEnum.PRODUCT_PART.getCode(),filterProductTreeIdStr,nodeType.getCode(),materialCodeOrName,rootIdsSize);
                //写递归的目的是path可能没有存pid,因为没有pid的情况少,所以不采用平铺
                while (null != filterProductTreeList && !filterProductTreeList.isEmpty()) {
                    productTreeDTOList.addAll(filterProductTreeList);
                    filterProductTreeIdList = filterProductTree(filterProductTreeList, filterProductTreeList);
                    filterProductTreeIdStr = filterProductTreeIdList.stream().flatMap(partId -> Arrays.stream(partId.split(","))).collect(Collectors.toList()).stream().distinct().collect(Collectors.joining(","));;
                    filterProductTreeList = productTreeMapper.selectTreeListByIds(ProductTreeTypeEnum.PRODUCT_TYPE.getCode(),ProductTreeTypeEnum.PRODUCT_KIND.getCode(),ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode(),ProductTreeTypeEnum.PRODUCT_PART.getCode(),filterProductTreeIdStr,nodeType.getCode(),materialCodeOrName,rootIdsSize);
                }
            }
        }


        // do转dto，所有节点附加扩展属性
        List<ProductTreeDTO> dtoNodes = productTreeDTOList;
        //do转dto，根节点附加扩展属性
        List<ProductTreeDTO> dtoRoots = rootNodes.stream().map(x -> do2DTO(x, rootProductPartMap, designDocumentMap)).collect(Collectors.toList());;
        return this.generateTree(dtoRoots, dtoNodes);
    }

    /**
     * 如果nodeDOSParent的pid不在nodeDOS的id里面则查询
     *
     * @param nodeDOSParent
     * @param nodeDOS
     * @return
     */
    public static List<String> filterProductTree(List<ProductTreeDTO> nodeDOSParent, List<ProductTreeDTO> nodeDOS) {
        List<String> ids = nodeDOS.stream().map(ProductTree::getId).collect(Collectors.toList());
        return nodeDOSParent.stream().map(ProductTree::getPid).filter(pid -> !ids.contains(pid)).collect(Collectors.toList());
    }

    private ProductTreeDTO do2DTO(ProductTree productTree, Map<String, ProductPart> productPartMap, Map<String, DesignDocument> designDocumentMap) {
        ProductTreeDTO productTreeDTO = new ProductTreeDTO();
        BeanUtil.copyProperties(productTree, productTreeDTO);

        Optional.ofNullable(productPartMap.get(productTree.getId())).ifPresent(x -> BeanUtil.copyProperties(x, productTreeDTO));
        Optional.ofNullable(designDocumentMap.get(productTree.getId())).ifPresent(x -> BeanUtil.copyProperties(x, productTreeDTO));
        return productTreeDTO;
    }

    private List<ProductTreeDTO> generateTree(List<ProductTreeDTO> rootNodes, List<ProductTreeDTO> all) {
        return rootNodes.stream().peek(node -> {
            List<ProductTreeDTO> child = getChild(node, all);
            node.setChildren(child);
        }).sorted(Comparator.comparing(ProductTreeDTO::getSort)).collect(Collectors.toList());
    }

    @SneakyThrows
    private List<ProductTreeDTO> getChild(ProductTreeDTO parent, List<ProductTreeDTO> all) {
        List<ProductTreeDTO> children;
        if (Boolean.TRUE.equals(parent.getBorrow())) {
            //父节点的源节点
            ProductTreeDTO sourceNode = all.stream().filter(x -> StrUtil.equals(parent.getSourceId(), x.getId())).findFirst().orElse(null);
            if (sourceNode == null) {
                children = all.stream().filter(node -> node.getPid().equals(parent.getId())).collect(Collectors.toList());
            } else {
                children = all.stream().filter(node -> node.getPid().equals(sourceNode.getId())).collect(Collectors.toList());
            }
        } else {
            children = all.stream().filter(node -> node.getPid().equals(parent.getId())).collect(Collectors.toList());
        }
        children = children.stream().sorted(Comparator.comparing(ProductTreeDTO::getSort)).collect(Collectors.toList());
        ObjectMapper objectMapper = new ObjectMapper();
        children = objectMapper.readValue(objectMapper.writeValueAsString(children), new TypeReference<List<ProductTreeDTO>>() {
        });
        if (Boolean.TRUE.equals(parent.getBorrow())) {
            children.forEach(node -> {
                node.setName(node.getName() + "(借用)");
                node.setBorrow(true);
                node.setPid(parent.getId());
                node.setPath(parent.getPath() + node.getId() + ",");
                //    记录原件的path
                all.stream().filter(x -> node.getId().equals(x.getId())).findFirst().ifPresent(x -> node.setSourcePath(x.getPath()));
            });
        }

        children.forEach(node -> {
            if (StrUtil.isNotBlank(node.getSourceId())) {
                node.setBorrow(true);
                node.setName(node.getName() + "(借用)");
                all.stream().filter(x -> node.getSourceId().equals(x.getId())).findFirst().ifPresent(x -> node.setSourcePath(x.getPath()));
            }
            node.setChildren(getChild(node, all));
        });
        return children;

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addProductType(AddProductTypeVO addProductTypeVO) {
        log.info("添加产品分类，req：{}", addProductTypeVO);
        //校验产品名称不能重复
        if (baseMapper.selectCount(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getName, addProductTypeVO.getName()).eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode())) > 0) {
            log.error("名称不能重复，req：{}", addProductTypeVO);
            throw new CdkitCloudException(String.format("名称不能重复，名称：%s", addProductTypeVO.getName()));
        }
        //编码写入后不能变更
        String code = addProductTypeVO.getCode();
        if (StrUtil.isNotBlank(code) && baseMapper.selectCount(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, code).eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode())) > 0) {
            throw new CdkitCloudException(String.format("产品分类编码不能重复，名称：%s", addProductTypeVO.getName()));
        }
        ProductTree productTree = BeanUtil.copyProperties(addProductTypeVO, ProductTree.class);
        productTree.setType(ProductTreeTypeEnum.PRODUCT_TYPE.getCode());
        productTree.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        productTree.setConverted(1);
        //先保存获取id，用于拼接path
        this.save(productTree);
        //更新pid和path字段
        String pid = productTree.getPid();
        if (StrUtil.isEmpty(pid)) {
            productTree.setPid(Constants.ROOT_PID);
            productTree.setPath(productTree.getId() + Constants.PATH_SPILT);
        } else {
            ProductTree byId = this.getById(pid);
            if (byId == null) {
                throw new CdkitCloudException(String.format("找不到指定的父级产品分类，pid：%s", pid));
            }
            productTree.setPath(byId.getPath() + productTree.getId() + Constants.PATH_SPILT);
        }
        // 用于排序，初始化取值和节点id一样
        productTree.setSort(productTree.getId());
        this.updateById(productTree);

        operationLogUtil.insertOperationLog(OperationTypeEnum.ADD.getType() + "产品分类", addProductTypeVO.getName());
    }

    @Override
    public void editProductType(EditProductTypeVO editProductTypeVO) {
        ProductTree productTree = this.getById(editProductTypeVO.getId());
        if (productTree == null) {
            throw new CdkitCloudException(String.format("找不到指定的产品分类，id：%s", editProductTypeVO.getId()));
        }
        BeanUtil.copyProperties(editProductTypeVO, productTree);
        this.updateById(productTree);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addProductKind(AddProductKindVO addProductKindVO) {
        log.info("添加产品种类，req：{}", addProductKindVO);
        //校验名称不能重复
        if (baseMapper.selectCount(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getName, addProductKindVO.getName()).eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode())) > 0) {
            log.error("产品种类名称不能重复，req：{}", addProductKindVO);
            throw new CdkitCloudException(String.format("产品种类名称不能重复，名称：%s", addProductKindVO.getName()));
        }
        //产品编码写入后不能变更
        String code = addProductKindVO.getCode();
        if (StrUtil.isNotBlank(code) && baseMapper.selectCount(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, code).eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode())) > 0) {
            throw new CdkitCloudException(String.format("产品种类编码不能重复，名称：%s", addProductKindVO.getName()));
        }
        ProductTree parentTree = this.getById(addProductKindVO.getPid());
        if (!Objects.equals(parentTree.getType(), ProductTreeTypeEnum.PRODUCT_TYPE.getCode())) {
            throw new CdkitCloudException("产品种类只能有一级并且只在产品分类下");
        }
        ProductTree productTree = BeanUtil.copyProperties(addProductKindVO, ProductTree.class);
        productTree.setType(ProductTreeTypeEnum.PRODUCT_KIND.getCode());
        productTree.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        productTree.setConverted(1);
        //先保存获取id，用于拼接path
        this.save(productTree);
        //更新pid和path字段
        ProductTree byId = this.getById(productTree.getPid());
        if (byId == null) {
            throw new CdkitCloudException(String.format("找不到指定的父级产品种类，pid：%s", productTree.getPid()));
        }
        productTree.setPath(byId.getPath() + productTree.getId() + Constants.PATH_SPILT);
        productTree.setSort(productTree.getId());
        this.updateById(productTree);

        operationLogUtil.insertOperationLog(OperationTypeEnum.ADD.getType() + "产品种类", addProductKindVO.getName());
    }

    @Override
    public void editProductKind(EditProductKindVO editProductVO) {
        ProductTree productTree = this.getById(editProductVO.getId());
        if (productTree == null) {
            throw new CdkitCloudException(String.format("找不到指定的产品种类，id：%s", editProductVO.getId()));
        }
        String pid = editProductVO.getPid();
        ProductTree parentNode = this.getById(pid);
        if (parentNode == null) {
            throw new CdkitCloudException(String.format("找不到指定的父级产品种类，pid：%s", pid));
        }
        BeanUtil.copyProperties(editProductVO, productTree);
        productTree.setPath(parentNode.getPath() + productTree.getId() + Constants.PATH_SPILT);
        this.updateById(productTree);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cascadeDeleteNode(String id, boolean cascade) {
        ProductTree productTree = this.getById(id);
        if (productTree == null) {
            throw new CdkitCloudException(String.format("找不到指定对象，id：%s", id));
        }
        if (StrUtil.equals(productTree.getPid(), Constants.ROOT_PID)) {
            throw new CdkitCloudException("根节点为系统预设，不能删除，请联系管理员");
        }
        List<ProductTree> list = this.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getSourceId, id));
        if (CollUtil.isNotEmpty(list)) {
            throw new CdkitCloudException("当前节点存在借用节点，如确定要删除，请先删除借用节点");
        }
        List<ProductTree> productTrees = this.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getNodeType, productTree.getNodeType()).like(ProductTree::getPath, productTree.getPath()));
        if (cascade) {
            //    级联删除
            List<String> idList = productTrees.stream().map(ProductTree::getId).collect(Collectors.toList());
            this.removeByIds(idList);
            //级联删除扩展属性
            productPartExtendService.remove(new LambdaQueryWrapper<ProductPartExtend>().in(ProductPartExtend::getProductId, idList));
            return;
        }
        //单个删除
        if (productTrees.size() > 1) {
            throw new CdkitCloudException("当前节点下存在子节点或图纸，不能删除");
        }
        this.removeById(id);
        //删除对应的扩展属性
        productPartExtendService.remove(new LambdaQueryWrapper<ProductPartExtend>().eq(ProductPartExtend::getProductId, id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addProductPart(AddProductPartVO addProductPartVO) {
        log.info("添加总装/零部件，req：{}", addProductPartVO);
        ProductTree parentNode = this.getById(addProductPartVO.getPid());
        // 当前层级
        Integer level = parentNode.getLevel() == null ? 1 : parentNode.getLevel() + 1;
        Integer parentNodeType = parentNode.getType();
        //当前节点类型
        Integer currNodeType;
        //父级类型必须是产品种类、总装、零部件
        if (parentNodeType.equals(ProductTreeTypeEnum.PRODUCT_KIND.getCode())) {
            currNodeType = ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode();
        } else if (parentNodeType.equals(ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode()) || parentNodeType.equals(ProductTreeTypeEnum.PRODUCT_PART.getCode())) {
            currNodeType = ProductTreeTypeEnum.PRODUCT_PART.getCode();
        } else {
            throw new CdkitCloudException("添加了错误的节点类型");
        }
        //校验名称不能重复
        if (baseMapper.selectCount(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getName, addProductPartVO.getName()).eq(ProductTree::getPid, parentNode.getId())) > 0) {
            log.error("物料名称不能重复，req：{}", addProductPartVO);
            throw new CdkitCloudException(String.format("物料名称不能重复，名称：%s", addProductPartVO.getName()));
        }
        //MdMaterialPage mdMaterialPage = mdMaterialApi.queryMdMaterial(addProductPartVO.getMaterialCode());
        addProductPartVO.setName(addProductPartVO.getName());
        addProductPartVO.setMaterialName(addProductPartVO.getMaterialName());
        //产品编码写入后不能变更
        String code = DidUtil.getDid(CodeTypeEnum.PRODUCT_CODE.toString(), MapUtil.empty());
        addProductPartVO.setCode(code);
        if (StrUtil.isNotBlank(code) && baseMapper.selectCount(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, code)) > 0) {
            throw new CdkitCloudException(String.format("物料名称不能重复，名称：%s", addProductPartVO.getName()));
        }
        ProductTree productTree = BeanUtil.copyProperties(addProductPartVO, ProductTree.class);
        if (ProductTreeTypeEnum.PRODUCT_KIND.getCode().equals(parentNode.getType()) || ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode().equals(parentNode.getType()) || ProductTreeTypeEnum.PRODUCT_PART.getCode().equals(parentNode.getType())) {
            productTree.setLevel(level);
        }

        productTree.setType(currNodeType);
        String pid = productTree.getPid();
        //先保存获取id，用于拼接path
        this.save(productTree);
        ProductTree byId = this.getById(pid);
        if (byId == null) {
            throw new CdkitCloudException(String.format("找不到指定的父级产品种类，pid：%s", pid));
        }
        productTree.setPath(byId.getPath() + productTree.getId() + Constants.PATH_SPILT);
        if (org.apache.commons.lang3.StringUtils.isEmpty(addProductPartVO.getSort())) {
            productTree.setSort(productTree.getId());
        }
        productTree.setConverted(0);
        productTree.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        this.updateById(productTree);
        ProductPart productPartParent = productPartService.getById(byId.getId());
        ProductPart productPart = BeanUtil.copyProperties(addProductPartVO, ProductPart.class);
        productPart.setId(productTree.getId());
        BigDecimal standardQuantity = productPartParent == null || productPartParent.getStandardQuantity() == null ? new BigDecimal(1) : productPartParent.getStandardQuantity();
        BigDecimal assembleQuantity = productPart.getAssembleQuantity() == null ? new BigDecimal(1) : productPart.getAssembleQuantity();
        productPart.setStandardQuantity(standardQuantity.multiply(assembleQuantity));
        productPartService.save(productPart);

        //写入扩展属性
        List<MdMaterialExtend> extendProperties = addProductPartVO.getProductPartExtendList();
        if (extendProperties != null && extendProperties.size() > 0) {
            List<ProductPartExtend> productPartExtends = extendProperties.stream().map(extendPropertyVO -> {
                ProductPartExtend productExtendProperty = new ProductPartExtend();
                productExtendProperty.setProductId(productTree.getId());
                productExtendProperty.setDefinedKey(extendPropertyVO.getDefinedKey());
                productExtendProperty.setDefinedValue(extendPropertyVO.getDefinedValue());
                productExtendProperty.setDefinedType("1");
                return productExtendProperty;
            }).collect(Collectors.toList());
            productPartExtendService.saveBatch(productPartExtends);
        }

        //写入定制属性
        List<MdMaterialExtend> customizedProperties = addProductPartVO.getProductPartCustomizedList();
        if (customizedProperties != null && customizedProperties.size() > 0) {
            List<ProductPartExtend> productPartExtends = customizedProperties.stream().map(extendPropertyVO -> {
                ProductPartExtend productExtendProperty = new ProductPartExtend();
                BeanUtil.copyProperties(extendPropertyVO, productExtendProperty);
                productExtendProperty.setId(null);
                productExtendProperty.setProductId(productTree.getId());
                productExtendProperty.setDefinedType("2");
                return productExtendProperty;
            }).collect(Collectors.toList());
            productPartExtendService.saveBatch(productPartExtends);
        }


        operationLogUtil.insertOperationLog(OperationTypeEnum.ADD.getType() + "产品总装/零部件", addProductPartVO.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editProductPart(EditProductPartVO editProductPartVO) {
        ProductTree productTree = this.getById(editProductPartVO.getId());
        if (productTree == null) {
            throw new CdkitCloudException(String.format("找不到指定的产品总装，id：%s", editProductPartVO.getId()));
        }
        ProductPart productPart = productPartService.getById(editProductPartVO.getId());

        String pid = editProductPartVO.getPid();
        ProductTree parentNode = this.getById(pid);
        if (parentNode == null) {
            throw new CdkitCloudException(String.format("找不到指定的父级，pid：%s", pid));
        }
        // MdMaterialPage mdMaterialPage = mdMaterialApi.queryMdMaterial(editProductPartVO.getMaterialCode());
        editProductPartVO.setName(editProductPartVO.getName());
        editProductPartVO.setMaterialName(editProductPartVO.getMaterialName());
        boolean isSource = StrUtil.isBlank(productTree.getSourceId());
        if (isSource) {
            //原件
            //更新主表数据
            BeanUtil.copyProperties(editProductPartVO, productTree);
            productTree.setPath(parentNode.getPath() + productTree.getId() + Constants.PATH_SPILT);
            BeanUtil.copyProperties(editProductPartVO, productPart);
            this.updateById(productTree);

            //联动把借用件也更新
            List<ProductTree> targetProductTrees = this.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getSourceId, editProductPartVO.getId()).eq(ProductTree::getNodeType, editProductPartVO.getNodeType()));
            for (ProductTree targetProductTree : targetProductTrees) {
                ProductPart targetProductPart = productPartService.getById(targetProductTree.getId());
                targetProductPart.setAssembleQuantity(editProductPartVO.getAssembleQuantity());
                targetProductPart.setAssembleSort(editProductPartVO.getAssembleSort());
                targetProductPart.setAssembleUnit(editProductPartVO.getAssembleUnit());
                targetProductPart.setExtendProductId(editProductPartVO.getExtendProductId());
                targetProductPart.setExtendProductName(editProductPartVO.getExtendProductName());
                productPartService.updateById(targetProductPart);
            }
        } else {
            //    借用件只更新装配属性
            productPart.setAssembleQuantity(editProductPartVO.getAssembleQuantity());
            productPart.setAssembleSort(editProductPartVO.getAssembleSort());
            productPart.setAssembleUnit(editProductPartVO.getAssembleUnit());
            productPart.setExtendProductId(editProductPartVO.getExtendProductId());
            productPart.setExtendProductName(editProductPartVO.getExtendProductName());
        }
        ProductPart productPartParent = productPartService.getById(pid);
        BigDecimal standardQuantity = productPartParent == null || productPartParent.getStandardQuantity() == null ? new BigDecimal(1) : productPartParent.getStandardQuantity();
        BigDecimal assembleQuantity = productPart.getAssembleQuantity() == null ? new BigDecimal(1) : productPart.getAssembleQuantity();
        productPart.setStandardQuantity(standardQuantity.multiply(assembleQuantity));
        productPartService.updateById(productPart);
        List<MdMaterialExtend> extendProperties = editProductPartVO.getProductPartExtendList();
        if (extendProperties != null && extendProperties.size() > 0) {
            //写入扩展属性
            productPartExtendService.remove(new LambdaQueryWrapper<ProductPartExtend>().eq(ProductPartExtend::getProductId, productPart.getId()).eq(ProductPartExtend::getDefinedType, "1"));
            List<ProductPartExtend> productPartExtends = extendProperties.stream().map(extendPropertyVO -> {
                ProductPartExtend productExtendProperty = new ProductPartExtend();
                productExtendProperty.setProductId(productTree.getId());
                productExtendProperty.setDefinedKey(extendPropertyVO.getDefinedKey());
                productExtendProperty.setDefinedValue(extendPropertyVO.getDefinedValue());
                return productExtendProperty;
            }).collect(Collectors.toList());
            productPartExtendService.saveBatch(productPartExtends);
        }

        //写入定制属性
        List<MdMaterialExtend> customizedProperties = editProductPartVO.getProductPartCustomizedList();
        if (customizedProperties != null && customizedProperties.size() > 0) {
            productPartExtendService.remove(new LambdaQueryWrapper<ProductPartExtend>().eq(ProductPartExtend::getProductId, productPart.getId()).eq(ProductPartExtend::getDefinedType, "2"));
            List<ProductPartExtend> productPartExtends = customizedProperties.stream().map(extendPropertyVO -> {
                ProductPartExtend productExtendProperty = new ProductPartExtend();
                BeanUtil.copyProperties(extendPropertyVO, productExtendProperty);
                productExtendProperty.setId(null);
                productExtendProperty.setProductId(productTree.getId());
                productExtendProperty.setDefinedType("2");
                return productExtendProperty;
            }).collect(Collectors.toList());
            productPartExtendService.saveBatch(productPartExtends);
        }
    }

    @Override
    public Page<DesignDocumentVO> listAllDesignDocumentByPid(String pid, String queryCondition, String category, Integer pageNo, Integer pageSize, Integer nodeType) {

        List<DesignDocumentVO> designDocumentVOs = this.listAllDesignDocumentByPid(pid, queryCondition, category, nodeType);
        if (null == designDocumentVOs || designDocumentVOs.isEmpty()) {
             return new Page<>(pageNo, pageSize);
        }
        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize - 1, designDocumentVOs.size() - 1);
        List<DesignDocumentVO> subList = designDocumentVOs.subList(startIndex, endIndex + 1);

        Page<DesignDocumentVO> resultPage = new Page<>(pageNo, pageSize);
        resultPage.setRecords(subList);
        resultPage.setTotal(designDocumentVOs.size());
        resultPage.setSize(pageSize);
        resultPage.setCurrent(pageNo);

        return resultPage;
    }

    @Override
    public List<DesignDocumentVO> listAllDesignDocumentByPid(String pid, String queryCondition, String category, Integer nodeType) {
        ProductTree productTree = this.getById(pid);
        if (productTree == null) {
            throw new CdkitCloudException(String.format("找不到指定的节点，id：%s", pid));
        }
        //总装/零部件的借用，要展示图纸信息
        if (productTree.getSourceId() != null) {
            pid = productTree.getSourceId();
        }

        // 直属下级的文件
        LambdaQueryWrapper<ProductTree> wrapper = new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getPid, pid).like(StrUtil.isNotBlank(queryCondition), ProductTree::getName, queryCondition).eq(ProductTree::getType, ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode()).eq(ProductTree::getNodeType, nodeType);
        //所有下级文件
        List<ProductTree> productTrees = this.list(wrapper);

        if (CollUtil.isEmpty(productTrees)) {
            return null;
        }

        //直接不用判断工作流
        List<ProductTree> productTreesOther = new ArrayList<>();
        //dwg走工作流的
        Map<String,ProductTree> productTreesWorkFlow = new HashMap<>();
        List<String> designDocumentIds = new ArrayList<>();
        List<String> designDocumentIdWorkFlows = new ArrayList<>();
        for (ProductTree allProductTree : productTrees) {
            //设计状态、取消发布状态、审批中需要对创建人和工作流进行判断
            if (StringUtils.getFilenameExtension(allProductTree.getName()).equals(Constants.DWG) &&
                    (allProductTree.getStatus() == DesignStatusEnum.DESIGNING.getCode() || allProductTree.getStatus() == DesignStatusEnum.NPPUBLISHED.getCode() || allProductTree.getStatus() == DesignStatusEnum.APPROVAL.getCode())) {
                if (allProductTree.getCreateBy().equals(LoginUtil.getCurrentUser().getUsername())) {
                    productTreesOther.add(allProductTree);
                    designDocumentIds.add(allProductTree.getId());
                } else {
                    productTreesWorkFlow.put(allProductTree.getId(),allProductTree);
                    designDocumentIdWorkFlows.add(allProductTree.getId());
                }
            } else {
                productTreesOther.add(allProductTree);
                designDocumentIds.add(allProductTree.getId());
            }
        }
        // 如果是工艺BOM
        if (NodeTypeEnum.PROCESS_TREE.getCode().equals(nodeType)) {
            for (ProductTree allProductTree : productTrees) {
                //设计状态、取消发布状态、审批中需要对创建人和工作流进行判断
                if (StringUtils.getFilenameExtension(allProductTree.getName()).equals(Constants.DWG) &&
                        (allProductTree.getStatus() == DesignStatusEnum.DESIGNING.getCode() || allProductTree.getStatus() == DesignStatusEnum.NPPUBLISHED.getCode() || allProductTree.getStatus() == DesignStatusEnum.APPROVAL.getCode())) {
                    if (allProductTree.getCreateBy().equals(LoginUtil.getCurrentUser().getUsername())) {
                        productTreesOther.add(allProductTree);
                        designDocumentIds.add(allProductTree.getConvertedFromId());
                    } else {
                        productTreesWorkFlow.put(allProductTree.getId(),allProductTree);
                        designDocumentIdWorkFlows.add(allProductTree.getConvertedFromId());
                    }
                } else {
                    productTreesOther.add(allProductTree);
                    designDocumentIds.add(allProductTree.getConvertedFromId());
                }
            }
        }
        List<DesignDocument> documents = new ArrayList<>();

        if(null!= designDocumentIds && !designDocumentIds.isEmpty()){
            LambdaQueryWrapper<DesignDocument> designDocumentWrapper = new LambdaQueryWrapper<DesignDocument>().in(DesignDocument::getId, designDocumentIds);
            documents = designDocumentService.list(designDocumentWrapper);
        }

        List<DesignDocument> documentWorkFlows = new ArrayList<>();
        if(null!= designDocumentIdWorkFlows && !designDocumentIdWorkFlows.isEmpty()){
            LambdaQueryWrapper<DesignDocument> designDocumentWorkFLowWrapper = new LambdaQueryWrapper<DesignDocument>().in(DesignDocument::getId, designDocumentIdWorkFlows);
            documentWorkFlows = designDocumentService.list(designDocumentWorkFLowWrapper);
        }


        //判断对应的工作流有操作权限
        for (DesignDocument designDocument : documentWorkFlows) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(designDocument.getWiid())) {
                List<WorkflowProcessAction> actions = new ArrayList<>();
                try {
                    actions = workFlowUtil.getActionsByWiId(designDocument.getWiid(), null);
                } catch (Exception e) {
                    log.info("获取工作流失败");
                    e.printStackTrace();
                }
                if (null != actions && !actions.isEmpty()) {
                    productTreesOther.add(productTreesWorkFlow.get(designDocument.getId()));
                    documents.add(designDocument);
                }
            }
        }

        Map<String, DesignDocument> designDocumentMap = documents.stream().collect(Collectors.toMap(DesignDocument::getId, e -> e));

        List<DesignDocumentVO> designDocumentVOs = productTreesOther.stream().map(x -> {
            DesignDocumentVO designDocumentVO = BeanUtil.copyProperties(x, DesignDocumentVO.class);
            BeanUtil.copyProperties(designDocumentMap.get(x.getId()), designDocumentVO);
            if (NodeTypeEnum.PROCESS_TREE.getCode().equals(nodeType)) {
                BeanUtil.copyProperties(designDocumentMap.get(x.getConvertedFromId()), designDocumentVO);
            }
            return designDocumentVO;
        }).collect(Collectors.toList());

        if (StrUtil.isNotBlank(category)) {
            designDocumentVOs = designDocumentVOs.stream().filter(x -> StrUtil.equals(category, x.getCategory())).collect(Collectors.toList());
        }
        designDocumentVOs.sort(Comparator.comparing(DesignDocumentVO::getCreateTime).reversed());
        return designDocumentVOs;
    }

    @Override
    public List<? extends ProductTree> getTileChildrenByPid(String pid) {
        return null;
    }

    @Override
    public ProductPartVO queryProductPartById(String id) {
        ProductTree productTree = this.getById(id);
        if (productTree == null) {
            // 未找到对应数据
            return null;
        }
        ProductPartVO productPartVO = BeanUtil.copyProperties(productTree, ProductPartVO.class);
        ProductPart productPart = Optional.ofNullable(productPartService.getById(id)).orElse(new ProductPart());
        BeanUtil.copyProperties(productPart, productPartVO);
        if (!org.apache.commons.lang3.StringUtils.isEmpty(productPartVO.getVersion())) {
            productPartVO.setVersion(CommonUtil.numberToLetter(Integer.parseInt(productPartVO.getVersion())));
        }
        List<ProductPartExtend> productPartExtends = productPartExtendService.list(new LambdaQueryWrapper<ProductPartExtend>().eq(ProductPartExtend::getProductId, productPart.getId()));
        List<ProductPartExtend> productPartExtendList = productPartExtends.stream().filter(v -> "1".equals(v.getDefinedType())).collect(Collectors.toList());

        productPartVO.setProductPartExtendList(productPartExtendList);
        List<ProductPartExtend> productPartCustomizedList = productPartExtends.stream().filter(v -> "2".equals(v.getDefinedType())).collect(Collectors.toList());
        productPartVO.setProductPartCustomizedList(productPartCustomizedList);


        return productPartVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importBatch(String pid, MultipartFile[] files) throws Exception {
        ProductTree parentNode = this.getById(pid);
        if (parentNode.getType().equals(ProductTreeTypeEnum.PRODUCT_TYPE.getCode()) || parentNode.getType().equals(ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode())) {
            throw new CdkitCloudException("产品分类和设计文档下级不能上传文件");
        }
        for (MultipartFile multipartFile : files) {
            String partCode, partName, materialCode, materialName, weight, radio;
            Map<String, Object> cadProperties = cadHandleService.parseCadProperties(multipartFile, Arrays.stream(CadParseKey.values()).map(CadParseKey::getCAD_KEY).collect(Collectors.toList()));
            partCode = cadProperties.getOrDefault(CadParseKey.PART_CODE.getCAD_KEY(), "") + "";
            partName = cadProperties.getOrDefault(CadParseKey.PART_NAME.getCAD_KEY(), "") + "";
            materialCode = cadProperties.getOrDefault(CadParseKey.MATERIAL_CODE.getCAD_KEY(), "") + "";
            materialName = cadProperties.getOrDefault(CadParseKey.MATERIAL_NAME.getCAD_KEY(), "") + "";
            weight = cadProperties.getOrDefault(CadParseKey.WEIGHT.getCAD_KEY(), "") + "";
            radio = cadProperties.getOrDefault(CadParseKey.RADIO.getCAD_KEY(), "") + "";
            if (StrUtil.isBlank(partCode)) {
                throw new CdkitCloudException(String.format("当前文件解析后的零部件编码为空，请检查后导入,文件名：%s", multipartFile.getOriginalFilename()));
            }
            //    校验当前零件是否已经存在于其他父节点下(原件)
            //ProductTree productTree = this.getOne(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, partCode).eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode()).isNull(ProductTree::getSourceId));
            ProductTree productTree = null;
            if (productTree == null) {
                //    树里没有该编码，正常新增
                String originalFilename = multipartFile.getOriginalFilename();
                    /*
                    生成总装/零部件节点（productTree）
                     */
                ProductTree part = new ProductTree();
                String filename = StringUtils.getFilename(originalFilename);
                String fileType = StringUtils.getFilenameExtension(originalFilename);
                part.setCode(partCode);
                part.setName(partName);
                part.setPid(pid);
                part.setType(parentNode.getType().equals(ProductTreeTypeEnum.PRODUCT_KIND.getCode()) ? ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode() : ProductTreeTypeEnum.PRODUCT_PART.getCode());
                part.setDescription(originalFilename);
                part.setMaterialCode(materialCode);
                this.save(part);
                //更新path字段
                part.setPath(parentNode.getPath() + part.getId() + Constants.PATH_SPILT);
                part.setSort(part.getId());
                this.updateById(part);
                    /*
                    生成零部件表节点（product_part）
                     */
                ProductPart productPart = new ProductPart();
                productPart.setId(part.getId());
                productPart.setMaterialCode(materialCode);
                productPart.setMaterialName(materialName);
                productPart.setBorrowId(null);
                productPart.setManufactureType(null);
                productPart.setPartType(null);
                productPart.setHandleType(null);
                productPart.setStructType(null);
                productPart.setWeight(weight);
                productPart.setSpecs(null);
                productPart.setVersion(null);
                productPart.setAssembleQuantity(null);
                productPart.setAssembleSort(null);
                productPart.setAssembleUnit(null);
                productPartService.save(productPart);
                    /*
                    挂载上边总装/零部件节点的图纸节点（ProductTree）
                     */
                ProductTree designDocumentPt = new ProductTree();
                designDocumentPt.setName(originalFilename);
                designDocumentPt.setDescription(null);
                designDocumentPt.setCode(null);
                designDocumentPt.setType(ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode());
                designDocumentPt.setStatus(DesignStatusEnum.DESIGNING.getCode());
                this.save(designDocumentPt);
                //更新path字段
                designDocumentPt.setPid(part.getId());
                designDocumentPt.setSort(designDocumentPt.getId());
                designDocumentPt.setPath(part.getPath() + designDocumentPt.getId() + Constants.PATH_SPILT);
                this.updateById(designDocumentPt);
                    /*
                    存储文档表（DesignDocument）
                     */
                DesignDocument designDocument = new DesignDocument();
                designDocument.setId(designDocumentPt.getId());
                designDocument.setRedBatch(false);
                designDocument.setSign(false);
                designDocument.setFileSize((int) multipartFile.getSize());
                designDocument.setCategory(Constants.DWG.equals(fileType) ? Constants.DRAWING : Constants.DOCUMENT);
                designDocument.setFileType(fileType);
                //上传文件到minio
                String filePath = CustomMinioUtil.upload(multipartFile, Constants.MINIO_RELATIVE);
                designDocument.setFilePath(filePath);
                //初始化图纸版本号为1
                designDocument.setVersion(1);

                designDocument.setName(filename);
                designDocumentService.save(designDocument);
                //    同步图纸
                eventPublisher.publishEvent(new DwgSyncEvent(this, 1, designDocument.getId(), multipartFile));
            } else {
                //    已经有的，校验是否在当前导入的父节点里
                if (!StrUtil.equals(productTree.getPid(), pid)) {
                    throw new CdkitCloudException(String.format("当前要导入的零件已经在节点【%s】下，零件编码：%s，", this.getById(productTree.getPid()).getName(), partCode));
                }
                //    父节点相同，更新数据
                productTree.setName(StrUtil.isBlank(partName) ? productTree.getName() : partName);
                ProductPart productPart = productPartService.getById(productTree.getId());
                productPart.setMaterialCode(StrUtil.isBlank(materialCode) ? productPart.getMaterialCode() : materialCode);
                productPart.setMaterialName(StrUtil.isBlank(materialName) ? productPart.getMaterialName() : materialName);
                this.updateById(productTree);
                productPartService.updateById(productPart);
                //更新图纸信息
                ProductTree documentProductTree = this.getOne(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getPid, productTree.getId()));
                String originalFilename = multipartFile.getOriginalFilename();
                documentProductTree.setName(originalFilename);
                this.updateById(documentProductTree);
                DesignDocument designDocument = designDocumentService.getById(documentProductTree.getId());
                //上传文件到zw cad
                CadResultDTO<UploadRespDTO> cadResultDTO = cadFeign.upload("0", multipartFile);
                if (!cadResultDTO.isSuccess()) {
                    throw new CdkitCloudException(String.format("上传文件失败，%s", originalFilename));
                }
                String docId = cadResultDTO.getData().getDocId();
                //上传文件到minio
                String filePath = CustomMinioUtil.upload(multipartFile, Constants.MINIO_RELATIVE);
                int fileSize = (int) multipartFile.getSize();
                designDocument.setDocId(docId);
                designDocument.setName(StringUtils.getFilename(originalFilename));
                designDocument.setFileSize(fileSize);
                designDocument.setFilePath(filePath);
                designDocumentService.updateById(designDocument);

                //确定是否存在该零件的借用件
                String sourceId = productTree.getSourceId();
                List<ProductTree> targetPartTrees = this.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getNodeType, NodeTypeEnum.PRODUCT_TREE.getCode()).eq(ProductTree::getSourceId, sourceId));
                if (CollUtil.isNotEmpty(targetPartTrees)) {
                    //    更新借用件的属性信息
                    for (ProductTree targetPartTree : targetPartTrees) {
                        targetPartTree.setName(StrUtil.isBlank(partName) ? productTree.getName() : partName);
                    }
                    List<ProductPart> productParts = productPartService.listByIds(targetPartTrees.stream().map(ProductTree::getId).collect(Collectors.toList()));
                    productParts.forEach(x -> x.setMaterialCode(StrUtil.isBlank(materialCode) ? productPart.getMaterialCode() : materialCode));
                    this.updateBatchById(targetPartTrees);
                    productPartService.updateBatchById(productParts);
                    //   todo 更新借用件的图纸
                    //DesignDocument designDocument = new DesignDocument();
                    //designDocument.setId(designDocumentPt.getId());
                    //designDocument.setRedBatch(false);
                    //designDocument.setSign(false);
                    //designDocument.setFileSize((int) multipartFile.getSize());
                    //designDocument.setCategory(Constants.DWG.equals(fileType) ? Constants.DRAWING : Constants.DOCUMENT);
                    //designDocument.setFileType(fileType);
                    ////上传文件到minio
                    //String filePath = CustomMinioUtil.upload(multipartFile, designDocumentPt.getPath());
                    //designDocument.setFilePath(filePath);
                    eventPublisher.publishEvent(new DwgSyncEvent(this, 1, designDocument.getId(), multipartFile));
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBorrowPart(AddBorrowPartVO addBorrowPartVO) {
        log.info("新增借用零部件:{}", JSON.toJSONString(addBorrowPartVO));
        //获取原件信息
        ProductTree productTree = this.getById(addBorrowPartVO.getSourceId());
        ProductPart productPart = productPartService.getById(addBorrowPartVO.getSourceId());
        if (productTree.getSourceId() != null) {
            throw new CdkitCloudException("当前零件为借用件，请借用原件");
        }
        List<ProductTree> productTrees = this.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getPid, addBorrowPartVO.getTargetPid()).eq(ProductTree::getSourceId, addBorrowPartVO.getSourceId()));
        if (CollUtil.isNotEmpty(productTrees)) {
            throw new CdkitCloudException("当前目录下已存在该原件，不需要重复借用");
        }

        ProductTree targetProduct = this.getById(addBorrowPartVO.getTargetPid());
        if (targetProduct != null && !org.apache.commons.lang3.StringUtils.isEmpty(targetProduct.getSourceId())) {
            throw new CdkitCloudException("当前目录为借用件，不允许在当前目录再借用其它零件");
        }

        //构造备用件信息
        ProductTree borrowProductTree = new ProductTree();
        BeanUtil.copyProperties(productTree, borrowProductTree, "id");
        borrowProductTree.setPid(addBorrowPartVO.getTargetPid());
        borrowProductTree.setSourceId(addBorrowPartVO.getSourceId());
        borrowProductTree.setCode(productTree.getCode() + UUID.randomUUID());
        this.save(borrowProductTree);

        //变更path信息
        borrowProductTree.setPath(this.getById(addBorrowPartVO.getTargetPid()).getPath() + borrowProductTree.getId() + Constants.PATH_SPILT);
        borrowProductTree.setSort(borrowProductTree.getId());
        this.updateById(borrowProductTree);

        ProductPart borrowProductPart = new ProductPart();
        //借用零部件的装配属性独有
        BeanUtil.copyProperties(productPart, borrowProductPart, "assembleQuantity", "assembleSort", "assembleUnit");
        borrowProductPart.setId(borrowProductTree.getId());
        productPartService.save(borrowProductPart);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toOrigin(String productPartId) {
        /**
         * a b c d四个零部件，其中a是原件，其他的借用件，此时四条数据分别是
         * id  name  source_id
         * 1   a
         * 2   b      a
         * 3   c      a
         * 4   d      a
         将b置为原件后
         * id  name  source_id
         * 1   a       b
         * 2   b
         * 3   c       b
         * 4   d       b
         *
         * 此时productPartId =b2
         */

        ProductTree productTree = this.getById(productPartId);
        if (StrUtil.isBlank(productTree.getSourceId())) {
            throw new CdkitCloudException("当前节点为原件，请选择借用件");
        }
        //b2的source_id置为null null代表当前为原件
        //UpdateWrapper<ProductPart> toOrigin = Wrappers.update();
        UpdateWrapper<ProductTree> toOrigin = Wrappers.update();
        this.update(null, toOrigin.eq("id", productPartId).set("source_id", null));

        //借用件置为原件后，其他的借用件也得改  a1的source_id置为b2
        UpdateWrapper<ProductTree> otherOrigin = Wrappers.update();
        this.update(null, otherOrigin.eq("source_id", productTree.getSourceId()).set("source_id", productPartId));

        //借用件置为原件  c3 d4的source_id置为b2
        UpdateWrapper<ProductTree> toBorrow = Wrappers.update();
        this.update(null, toBorrow.eq("id", productTree.getSourceId()).set("source_id", productPartId));
    }

    @Override
    public Page<PartBorrowInfoVO> getBorrowInfoById(String productPartId, String queryCondition, Integer pageNo, Integer pageSize) {
        // 请求2的信息       id:1      borrowId：2   1:借用件  2:原件  如果已经是借用件，就必须去借用原件  2是1的原件
        //请求1的信息   1是2的借用件，此时再查出来2的其他借用件
        //关联的原件、借用件信息
        ProductTree product = this.getById(productPartId);
        Page<ProductPartVO> pageVO = new Page<>(pageNo, pageSize);
        Page<ProductPartVO> page = productPartMapper.getBorrowInfo(pageVO, productPartId, product.getSourceId(), queryCondition);
        if (page.getRecords().isEmpty()) {
            return new Page<>(page.getCurrent(), page.getSize(), 0);
        }
        List<ProductPartVO> borrowInfo = page.getRecords();

        List<PartBorrowInfoVO> ret = new ArrayList<>();
        for (ProductPartVO productPartVO : borrowInfo) {
            String sourceId = productPartVO.getSourceId();
            if (StrUtil.isEmpty(sourceId)) {
                //原件 borrow为空 id有值
                PartBorrowInfoVO partBorrowInfoVO = new PartBorrowInfoVO();
                partBorrowInfoVO.setTargetId(null);
                partBorrowInfoVO.setSourceId(productPartVO.getId());
                partBorrowInfoVO.setDesc("原件");
                partBorrowInfoVO.setAssembleQuantity(productPartVO.getAssembleQuantity());
                partBorrowInfoVO.setAssemblePath(productPartVO.getPath());
                ret.add(0, partBorrowInfoVO);
            } else {
                //    借用件 id：当前的借用件  source_id:当前借用件借用的零部件（原件）
                PartBorrowInfoVO partBorrowInfoVO = new PartBorrowInfoVO();
                partBorrowInfoVO.setTargetId(productPartVO.getId());
                partBorrowInfoVO.setSourceId(sourceId);
                partBorrowInfoVO.setDesc("借用件");
                partBorrowInfoVO.setAssembleQuantity(productPartVO.getAssembleQuantity());
                partBorrowInfoVO.setAssemblePath(productPartVO.getPath());
                ret.add(partBorrowInfoVO);
            }
        }

        //路径上所有的id
        List<String> idList = ret.stream().flatMap(x -> Arrays.stream(x.getAssemblePath().split(Constants.PATH_SPILT))).collect(Collectors.toList());
        List<ProductTree> productTrees = this.listByIds(idList);

        Map<String, ProductTree> idProductTreeMap = productTrees.stream().collect(Collectors.toMap(ProductTree::getId, e -> e));

        List<PartBorrowInfoVO> data = ret.stream().peek(x -> {
            String assemblePath = x.getAssemblePath();
            String productKindName = "";
            String assemblePathDesc = "";
            for (String id : assemblePath.split(Constants.PATH_SPILT)) {
                ProductTree productTree = idProductTreeMap.get(id);
                if (productTree == null) {
                    continue;
                }
                Integer type = productTree.getType();
                if (type.equals(ProductTreeTypeEnum.PRODUCT_TYPE.getCode()) || type.equals(ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode())) {
                    //产品分类、设计图纸不参与
                    continue;
                }
                if (type.equals(ProductTreeTypeEnum.PRODUCT_KIND.getCode())) {
                    productKindName = productKindName + productTree.getName() + "\\";
                }
                assemblePathDesc = assemblePathDesc + "[" + productTree.getCode() + "]" + productTree.getName() + "\\";
            }
            x.setProductKindName(productKindName.substring(0, productKindName.length() - 1));
            x.setAssemblePathDesc(assemblePathDesc.substring(0, assemblePathDesc.length() - 1));
        }).collect(Collectors.toList());
        Page<PartBorrowInfoVO> pageDTO = new PageDTO<>();
        pageDTO.setRecords(data);
        pageDTO.setTotal(page.getTotal());
        pageDTO.setCurrent(page.getCurrent());
        pageDTO.setSize(page.getSize());
        return pageDTO;
    }

    @Override
    public DesignDocumentVO queryDesignDocumentById(String id) {
        ProductTree productTree = this.getById(id);
        if (productTree == null) {
            throw new CdkitCloudException("找不到指定的设计文件");
        }
        DesignDocument designDocument = designDocumentService.getById(id);
        DesignDocumentVO designDocumentVO = BeanUtil.copyProperties(designDocument, DesignDocumentVO.class);
        BeanUtil.copyProperties(productTree, designDocumentVO);
        return designDocumentVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void designDocumentPublish(DesignDocumentPublishVO designDocumentPublishVO, HttpServletRequest request) {
        ProductTree productTree = this.getById(designDocumentPublishVO.getId());
        if (productTree == null) {
            throw new CdkitCloudException("找不到指定的设计文件");
        }
        DesignDocument designDocument = designDocumentService.getById(designDocumentPublishVO.getId());
        //锁定人
        String lockedBy = designDocument.getLockedBy();
        String userId = LoginUtil.getCurrentUser().getUsername();
        if (StrUtil.isNotEmpty(lockedBy) && !StrUtil.equals(lockedBy, userId)) {
            //    存在锁定人并且和当前登陆用户不同
            throw new CdkitCloudException("您不是当前文档的创建者，不能提交");
        }
        Integer curr = productTree.getStatus();
        if (designDocumentPublishVO.getStatus().equals(3)) {
            if (!DesignStatusEnum.PUBLISHED.getCode().equals(productTree.getStatus())) {
                throw new CdkitCloudException("只有已发布的可以重新发布");
            }
            //重发布 版本+1
            //productTree.setStatus(DesignStatusEnum.PUBLISHED.getCode());

            DesignDocument designDocumentNew = designDocumentService.getById(designDocument.getId());
            designDocumentNew.setRemark(designDocument.getRemark());
            log.info("图纸审核驳回:{}", JSON.toJSONString(designDocumentNew));
            try {
                //todo 工作流废弃有问题，暂时使用把wiid清空的办法
                //workFlowUtil.invalidFlow(designDocumentNew.getWiid(),designDocument.getRemark(),request);
                productTree.setStatus(DesignStatusEnum.DESIGNING.getCode());
            } catch (Exception e) {
                log.error("审核驳回失败", e);
                throw new CdkitCloudException("审核驳回失败");
            }

            //主表版本号加1
            int version = (int) designDocument.getVersion();
            designDocument.setWiid("");
            designDocument.setVersion(version + 1);
            designDocumentService.updateById(designDocument);

            operationLogUtil.insertOperationLog(OperationTypeEnum.PUBLISH.getType(), designDocument.getName());
        } else if (designDocumentPublishVO.getStatus().equals(6) && productTree.getStatus() != 7) {
            //    提交并签名
            log.info("写入cad签名开始");
            List<WriteSignDTO> writeSignDTOList = new ArrayList<>();
            WriteSignDTO signDTO1 = new WriteSignDTO();
            signDTO1.setSignKey(CadParseKey.SJQM.getCAD_KEY());
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            signDTO1.setValue(loginUser.getUsername());
            signDTO1.setSignType(3);
            writeSignDTOList.add(signDTO1);

            WriteSignDTO signDTO2 = new WriteSignDTO();
            signDTO2.setSignKey(CadParseKey.SJWCRQ.getCAD_KEY());
            signDTO2.setValue(DateUtil.format(new Date(), "yyyy.M.d"));
            signDTO2.setSignType(2);
            writeSignDTOList.add(signDTO2);

            cadHandleService.writeCadSign(designDocumentPublishVO.getId(), writeSignDTOList);
            productTree.setStatus(designDocumentPublishVO.getStatus());

            /* 审批流部分 */
            log.info("发起图纸审核:{}", JSON.toJSONString(designDocument));
            String wiid = designDocument.getWiid();
            try {
                List<WorkflowProcessEntity> processEntities = workFlowUtil.getMyStartProcess(request);
                WorkflowProcessEntity entity = processEntities.stream().filter(it -> "图纸审批".equals(it.getAppName())).findFirst().orElse(null);
                if (entity != null) {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(wiid)) {
                        log.info("发起重新提交流程");
                        workFlowUtil.approvalWorkflowByWiid(wiid, "重新提交", request);
                        designDocument.setVersion(designDocument.getVersion() + 0.1);
                    } else {
                        //todo 流程标题暂时不确认是否用文件名
                        wiid = workFlowUtil.startWorkflow(entity.getAppId(), designDocument.getId(), "图纸[" + designDocument.getName() + "]审批", "", request);
                        log.info("图纸[" + designDocument.getName() + "]审批发起流程完成:{}", wiid);
                        designDocument.setWiid(wiid);
                    }
                }
            } catch (Exception e) {
                log.error("发起流程失败", e);
                throw new CdkitCloudException("发起流程失败, 请联系管理员");
            }
            //锁定人设定为下一个审批人
            log.info("当前登录人:{}" + LoginUtil.getCurrentUser().getUsername());
            designDocument.setLockedBy(LoginUtil.getCurrentUser().getUsername());
            /* 审批流部分结束 */
            designDocumentMapper.updateById(designDocument);
            log.info("写入cad签名结束");
        } else if (designDocumentPublishVO.getStatus().equals(6) && productTree.getStatus() == 7) {
            if (curr.equals(designDocumentPublishVO.getStatus())) {
                throw new CdkitCloudException("请不要重复操作");
            }
            productTree.setStatus(2);
            //取消发布在发布的时候需要增加版本库信息
            DesignDocumentHistory designDocumentHistory = BeanUtil.copyProperties(designDocument, DesignDocumentHistory.class, "id");
            designDocumentHistory.setDesignDocumentId(designDocument.getId());
            designDocumentHistory.setVersion(designDocument.getVersion());
            designDocumentHistoryService.save(designDocumentHistory);
        } else {
            if (curr.equals(designDocumentPublishVO.getStatus())) {
                throw new CdkitCloudException("请不要重复操作");
            }
            productTree.setStatus(designDocumentPublishVO.getStatus());
            if(designDocumentPublishVO.getStatus().equals(2)){
                //发布的时候需要增加版本库信息
                DesignDocumentHistory designDocumentHistory = BeanUtil.copyProperties(designDocument, DesignDocumentHistory.class, "id");
                designDocumentHistory.setDesignDocumentId(designDocument.getId());
                designDocumentHistory.setVersion(designDocument.getVersion());
                designDocumentHistoryService.save(designDocumentHistory);
            }
        }

        this.updateById(productTree);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void designDocumentOutbound(String id, Boolean cancel) {
        ProductTree productTree = this.getById(id);
        DesignDocument currDesignDocument = designDocumentService.getById(id);
        if (productTree == null || currDesignDocument == null) {
            throw new CdkitCloudException("找不到指定的设计文件");
        }
        //正常出库逻辑
        double currVersion = currDesignDocument.getVersion();
        if (Boolean.FALSE.equals(cancel)) {
            if (!productTree.getStatus().equals(DesignStatusEnum.DESIGNING.getCode()) && !productTree.getStatus().equals(DesignStatusEnum.INBOUND.getCode())) {
                throw new CdkitCloudException("只有设计中和已入库的图纸才能出库");
            }
            productTree.setStatus(DesignStatusEnum.OUTBOUND.getCode());
            this.updateById(productTree);
            //存储历史版本
            DesignDocumentHistory designDocumentHistory = BeanUtil.copyProperties(currDesignDocument, DesignDocumentHistory.class, "id");
            designDocumentHistory.setDesignDocumentId(id);
            designDocumentHistoryService.save(designDocumentHistory);
            //删除图纸表的对应记录
            designDocumentService.removeById(id);
            //文档表新增一条记录,用的是原id
            DesignDocument newest = BeanUtil.copyProperties(currDesignDocument, DesignDocument.class);
            newest.setVersion(currVersion + 1);
            newest.setLockedBy(LoginUtil.getCurrentUser().getUsername());
            newest.setLockedTime(new Date());
            designDocumentService.save(newest);

            operationLogUtil.insertOperationLog(OperationTypeEnum.OUTBOUND.getType(), currDesignDocument.getName());
            return;
        }
        if (ObjectUtil.notEqual(LoginUtil.getCurrentUser().getUsername(), currDesignDocument.getLockedBy())) {
            throw new CdkitCloudException("只有出库人才能取消出库");
        }
        //取消出库逻辑
        if (!productTree.getStatus().equals(DesignStatusEnum.OUTBOUND.getCode())) {
            throw new CdkitCloudException("当前图纸状态不是已出库，不需要取消");
        }
        UpdateWrapper<DesignDocument> updateWrapper = Wrappers.update();
        designDocumentService.update(null, updateWrapper.eq("id", currDesignDocument.getId()).set("version", currVersion - 1).set("locked_by", null).set("locked_time", null));
        //状态回置为设计中
        productTree.setStatus(DesignStatusEnum.DESIGNING.getCode());
        this.updateById(productTree);
        //    删除历史表的记录
        designDocumentHistoryService.remove(new LambdaQueryWrapper<DesignDocumentHistory>().eq(DesignDocumentHistory::getDesignDocumentId, id).eq(DesignDocumentHistory::getVersion, currVersion - 1));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBorrowDesignDocument(AddBorrowDocumentVO addBorrowDocumentVO) {
        ProductTree productTree = this.getById(addBorrowDocumentVO.getSourceId());
        DesignDocument designDocument = designDocumentService.getById(addBorrowDocumentVO.getSourceId());
        if (productTree.getSourceId() != null) {
            throw new CdkitCloudException("当前零件为借用件，请借用原件");
        }
        List<ProductTree> productTrees = this.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getId, addBorrowDocumentVO.getTargetPid()).eq(ProductTree::getSourceId, addBorrowDocumentVO.getSourceId()));
        if (CollUtil.isNotEmpty(productTrees)) {
            throw new CdkitCloudException("当前目录下已存在该原件，不需要重复借用");
        }
        ProductTree borrowProductTree = new ProductTree();
        BeanUtil.copyProperties(productTree, borrowProductTree, "id");
        borrowProductTree.setPid(addBorrowDocumentVO.getTargetPid());
        borrowProductTree.setSourceId(addBorrowDocumentVO.getSourceId());
        borrowProductTree.setCode(productTree.getCode() + UUID.randomUUID());
        this.save(borrowProductTree);

        //变更path信息
        borrowProductTree.setPath(this.getById(addBorrowDocumentVO.getTargetPid()).getPath() + borrowProductTree.getId() + Constants.PATH_SPILT);
        borrowProductTree.setSort(borrowProductTree.getId());
        this.updateById(borrowProductTree);

        DesignDocument borrowDesignDocument = new DesignDocument();
        //借用零部件的装配属性独有
        BeanUtil.copyProperties(designDocument, borrowDesignDocument);
        borrowDesignDocument.setId(borrowProductTree.getId());
        designDocumentService.save(borrowDesignDocument);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
//置为源图纸先不做
    public void toDocumentOrigin(String targetId) {
        toOrigin(targetId);
    }

    @Override
    public List<DocumentBorrowInfoVO> getDocumentBorrowInfoById(String id) {
        LambdaQueryWrapper<ProductTree> wrapper = new LambdaQueryWrapper<ProductTree>().or(x -> x.eq(ProductTree::getId, id)).or(x -> x.eq(ProductTree::getSourceId, id)).or(x -> x.eq(ProductTree::getId, this.getById(id).getSourceId()));
        List<ProductTree> borrowInfos = this.list(wrapper);
        if (CollUtil.isEmpty(borrowInfos)) {
            return Collections.emptyList();
        }
        List<DocumentBorrowInfoVO> ret = new ArrayList<>();
        for (ProductTree borrowInfo : borrowInfos) {
            String sourceId = borrowInfo.getSourceId();
            if (StrUtil.isEmpty(sourceId)) {
                DocumentBorrowInfoVO vo = new DocumentBorrowInfoVO();
                vo.setTargetId(borrowInfo.getId());
                vo.setSourceId(null);
                vo.setDesc("原图纸");
                vo.setAssemblePath(borrowInfo.getPath());
                ret.add(0, vo);
            } else {
                DocumentBorrowInfoVO vo = new DocumentBorrowInfoVO();
                vo.setTargetId(borrowInfo.getId());
                vo.setSourceId(sourceId);
                vo.setDesc("借用图纸");
                vo.setAssemblePath(borrowInfo.getPath());
                ret.add(vo);
            }
        }

        //路径上所有的id
        List<String> idList = ret.stream().flatMap(x -> Arrays.stream(x.getAssemblePath().split(Constants.PATH_SPILT))).collect(Collectors.toList());
        List<ProductTree> productTrees = this.listByIds(idList);

        Map<String, ProductTree> idProductTreeMap = productTrees.stream().collect(Collectors.toMap(ProductTree::getId, e -> e));

        return ret.stream().peek(x -> {
            String assemblePath = x.getAssemblePath();
            String productKindName = "";
            String assemblePathDesc = "";
            for (String tempId : assemblePath.split(Constants.PATH_SPILT)) {
                ProductTree productTree = idProductTreeMap.get(tempId);
                Integer type = productTree.getType();
                if (type.equals(ProductTreeTypeEnum.PRODUCT_TYPE.getCode())) {
                    //产品分类、设计图纸不参与
                    continue;
                }
                if (type.equals(ProductTreeTypeEnum.PRODUCT_KIND.getCode())) {
                    productKindName = productKindName + productTree.getName() + "\\";
                }
                assemblePathDesc = assemblePathDesc + "[" + productTree.getCode() + "]" + productTree.getName() + "\\";
            }
            x.setProductKindName(productKindName.substring(0, productKindName.length() - 1));
            x.setAssemblePathDesc(assemblePathDesc.substring(0, assemblePathDesc.length() - 1));
        }).collect(Collectors.toList());
    }

    @Override
    public Page<OutboundDocumentVO> outboundDocumentList(QueryDesignDocumentVO queryDesignDocumentVO) {
        Page<OutboundDocumentVO> pageVO = new Page<>(queryDesignDocumentVO.getPageNo(), queryDesignDocumentVO.getPageSize());
        Page<OutboundDocumentVO> page = designDocumentMapper.outboundDocumentList(pageVO, queryDesignDocumentVO);
        if (page.getRecords().isEmpty()) {
            return new Page<>(page.getCurrent(), page.getSize(), 0);
        }
        List<OutboundDocumentVO> outboundDocumentVOS = page.getRecords();
        //填充pathDesc属性
        List<String> idList = outboundDocumentVOS.stream().flatMap(x -> Arrays.stream(x.getPath().split(Constants.PATH_SPILT))).collect(Collectors.toList());
        List<ProductTree> productTrees = this.listByIds(idList);
        Map<String, ProductTree> idProductTreeMap = productTrees.stream().collect(Collectors.toMap(ProductTree::getId, e -> e));
        outboundDocumentVOS = outboundDocumentVOS.stream().peek(x -> {
            String path = x.getPath();
            StringBuilder pathDesc = new StringBuilder();
            for (String tempId : path.split(Constants.PATH_SPILT)) {
                ProductTree productTree = idProductTreeMap.get(tempId);
                if (productTree == null) {
                    continue;
                }
                Integer type = productTree.getType();
                if (type.equals(ProductTreeTypeEnum.PRODUCT_TYPE.getCode()) || type.equals(ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode())) {
                    //产品分类、设计图纸不参与
                    continue;
                }
                pathDesc.append("[").append(productTree.getCode()).append("]").append(productTree.getName()).append("\\");
            }
            x.setPathDesc(pathDesc.substring(0, pathDesc.length() - 1));
        }).collect(Collectors.toList());
        page.setRecords(outboundDocumentVOS);
        return page;
    }

    /**
     * 获取父级path
     *
     * @param currPath a,b,c,d,
     * @return a, b, c,
     */
    private String getParentPath(String currPath) {
        if (StrUtil.isEmpty(currPath)) {
            throw new CdkitCloudException("currPath不能为空");
        }
        currPath = currPath.substring(0, currPath.length() - 1);
        return currPath.substring(0, currPath.lastIndexOf(Constants.PATH_SPILT));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importDocument(String pid, MultipartFile multipartFile, Integer nodeType) throws Exception {
        ProductTree parentProductTree = this.getById(pid);
        String originalFilename = multipartFile.getOriginalFilename();
        //保存主表信息
        String parentPath = parentProductTree.getPath();
        ProductTree productTree = new ProductTree();
        productTree.setType(ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode());
        //productTree.setStatus(DesignStatusEnum.DESIGNING.getCode());
        String fileType = StringUtils.getFilenameExtension(originalFilename);
        productTree.setStatus(Constants.DWG.equals(fileType) ? DesignStatusEnum.DESIGNING.getCode() : DesignStatusEnum.PUBLISHED.getCode());
        productTree.setName(originalFilename);
        productTree.setNodeType(nodeType);
        this.save(productTree);
        productTree.setSort(productTree.getId());
        productTree.setPid(parentProductTree.getId());
        productTree.setPath(parentPath + productTree.getId() + Constants.PATH_SPILT);
        productTree.setCode(originalFilename + UUID.randomUUID());
        this.updateById(productTree);
        //保存设计文档表
        DesignDocument designDocument = new DesignDocument();
        designDocument.setId(productTree.getId());
        designDocument.setName(originalFilename);
        designDocument.setRedBatch(false);
        designDocument.setSign(false);
        designDocument.setFileSize((int) multipartFile.getSize());
        designDocument.setCategory(Constants.DWG.equals(fileType) ? Constants.DRAWING : Constants.DOCUMENT);
        designDocument.setFileType(fileType);
        //上传文件到minio
        String filePath = CustomMinioUtil.upload(multipartFile, Constants.MINIO_RELATIVE);
        designDocument.setFilePath(filePath);
        //初始化图纸版本号为1
        designDocument.setVersion(1);
        designDocumentService.save(designDocument);
        //    同步图纸
        if (Constants.DRAWING.equals(designDocument.getCategory())) {
            eventPublisher.publishEvent(new DwgSyncEvent(this, 1, designDocument.getId(), multipartFile));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void designDocumentInbound(String id, MultipartFile file) throws Exception {
        ProductTree productTree = this.getById(id);
        if (productTree == null) {
            throw new CdkitCloudException("找不到指定的设计文件");
        }
        DesignDocument currDesignDocument = designDocumentService.getById(id);
        if (!productTree.getStatus().equals(DesignStatusEnum.OUTBOUND.getCode())) {
            throw new CdkitCloudException("只有已出库的文档才能入库");
        }
        //入库
        if (!StrUtil.equals(currDesignDocument.getLockedBy(), LoginUtil.getCurrentUser().getUsername())) {
            //    锁定人并且和当前登陆用户不同
            throw new CdkitCloudException("当前设计文件已锁定");
        }
        productTree.setStatus(DesignStatusEnum.INBOUND.getCode());
        productTree.setName(file.getOriginalFilename());
        this.updateById(productTree);
        currDesignDocument.setName(file.getOriginalFilename());
        currDesignDocument.setRedBatch(false);
        currDesignDocument.setSign(false);
        currDesignDocument.setFileSize((int) file.getSize());
        String originalFilename = file.getOriginalFilename();
        String fileType = StringUtils.getFilenameExtension(originalFilename);
        currDesignDocument.setCategory(Constants.DWG.equals(fileType) ? Constants.DRAWING : Constants.DOCUMENT);
        currDesignDocument.setFileType(fileType);
        //上传文件到minio
        String filePath = CustomMinioUtil.upload(file, Constants.MINIO_RELATIVE);
        currDesignDocument.setFilePath(filePath);
        designDocumentService.updateById(currDesignDocument);
        //解除锁定
        designDocumentService.update(null, Wrappers.<DesignDocument>lambdaUpdate().set(DesignDocument::getLockedTime, null).set(DesignDocument::getLockedBy, null).eq(DesignDocument::getId, id));
        operationLogUtil.insertOperationLog(OperationTypeEnum.INBOUND.getType(), currDesignDocument.getName());

        //    todo 解析轻量化文件到数据库
        if (Constants.DRAWING.equals(currDesignDocument.getCategory())) {
            eventPublisher.publishEvent(new DwgSyncEvent(this, 1, currDesignDocument.getId(), file));
        }
    }

    @Override
    public void cancelBorrow(String id) {
        ProductTree productTree = this.getById(id);
        if (StrUtil.isEmpty(productTree.getSourceId())) {
            throw new CdkitCloudException("当前节点不是借用件，无法取消");
        }
        this.removeById(id);
    }

    @Override
    public void importProcessRoute(String productTreeId, MultipartFile file) {

    }

    /**
     * 通过id查询产品属性（来源主数据）
     *
     * @param id 主键ID
     * @return 结果返回
     */
    @Override
    public ProductAttributesVO queryAttributeById(String id) {
        ProductTree productTree = this.getById(id);
        if (productTree == null) {
            // 未找到对应数据
            return null;
        }


        ProductAttributesVO productAttributesVO = BeanUtil.copyProperties(productTree, ProductAttributesVO.class);
        ProductPart productPart = Optional.ofNullable(productPartService.getById(id)).orElse(new ProductPart());
        BeanUtil.copyProperties(productPart, productAttributesVO);
        if (!org.apache.commons.lang3.StringUtils.isEmpty(productAttributesVO.getVersion())) {
            productAttributesVO.setVersion(CommonUtil.numberToLetter(Integer.parseInt(productAttributesVO.getVersion())));
        }
        // 查询物料信息
        MdMaterialPage mdMaterial = Optional.ofNullable(mdMaterialApi.queryMdMaterial(productTree.getMaterialCode())).orElse(new MdMaterialPage());
        // 装配单位
        BeanUtil.copyProperties(mdMaterial, productAttributesVO, "id");
        productAttributesVO.setAssembleUnit(mdMaterial.getBasicUnitId());
        productAttributesVO.setManufactureType(mdMaterial.getOperateType());
        productAttributesVO.setPartType(mdMaterial.getItemType());
        productAttributesVO.setSpecs(mdMaterial.getSpecification());
        productAttributesVO.setName(productTree.getName());


        List<MdMaterialExtend> mdMaterialExtendList = mdMaterial.getMdMaterialExtendList();

        productAttributesVO.setProductPartExtendList(mdMaterialExtendList);
        productAttributesVO.setProductPartCustomizedList(mdMaterial.getMdMaterialCustomizedList());
        return productAttributesVO;
    }

    /**
     * 通过物料编码查询物料详情
     *
     * @param materialCode 物料编码
     * @return 结果返回
     */
    @Override
    public ProductAttributesVO queryAttributeByMaterialCode(String materialCode) {
        // 查询物料信息
        MdMaterialPage mdMaterial = Optional.ofNullable(mdMaterialApi.queryMdMaterial(materialCode)).orElse(new MdMaterialPage());
        ProductAttributesVO productAttributesVO = new ProductAttributesVO();
        // 装配单位
        BeanUtil.copyProperties(mdMaterial, productAttributesVO, "id");
        productAttributesVO.setAssembleUnit(mdMaterial.getBasicUnitId());
        productAttributesVO.setManufactureType(mdMaterial.getOperateType());
        productAttributesVO.setPartType(mdMaterial.getItemType());
        productAttributesVO.setSpecs(mdMaterial.getSpecification());
        productAttributesVO.setName(mdMaterial.getMaterialName());

        List<MdMaterialExtend> mdMaterialExtendList = mdMaterial.getMdMaterialExtendList();

        productAttributesVO.setProductPartExtendList(mdMaterialExtendList);
        productAttributesVO.setProductPartCustomizedList(mdMaterial.getMdMaterialCustomizedList());
        return productAttributesVO;
    }

    /**
     * 查询产品定制属性
     *
     * @return 定制属性列表
     */
    @Override
    public List<ProductPartExtend> queryProductPartExtendForAdd() {
        return productPartExtendService.list(new LambdaQueryWrapper<ProductPartExtend>().eq(ProductPartExtend::getDefinedType, "2").orderByAsc(ProductPartExtend::getSort));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyTree(String id, String productOrderNum) {
        if (StrUtil.isEmpty(id)) {
            throw new CdkitCloudException("id不能为空！");
        }
        if (StrUtil.isBlank(productOrderNum)) {
            throw new CdkitCloudException("订单号不能为空！");
        }
        QueryWrapper<ProductTree> productTreeQueryWrapper = new QueryWrapper<>();
        productTreeQueryWrapper.eq("binding_product_order_num", productOrderNum);
        productTreeQueryWrapper.eq("copy_from_id", id);
        List<ProductTree> productTreesList = productTreeMapper.selectList(productTreeQueryWrapper);
        if (CollectionUtil.isNotEmpty(productTreesList)) {
            return productTreesList.get(0).getId();
        }
        List<ProductTreeDTO> productTreeDTOS = handleTree(CollectionUtil.newArrayList(id), NodeTypeEnum.PROCESS_TREE, null);
        Assert.isTrue(CollectionUtil.isNotEmpty(productTreeDTOS), "没有找到对应的bom信息!");
        List<ProductTreeDTO> ptList = CollectionUtil.newArrayList();
        ProductTreeDTO productTreeDTO = productTreeDTOS.get(0);
        // 递归获得所有bom的id
        recursionGetProductIds(productTreeDTO, ptList);
        List<String> ids = ptList.stream().map(ProductTreeDTO::getId).collect(Collectors.toList());

        List<ProductPart> productParts = productPartService.listByIds(ids);
        Map<String, ProductPart> productPartMap = productParts.stream().collect(Collectors.toMap(ProductPart::getId, Function.identity()));
        String endId = "";
        int i = 0;
        for (ProductTreeDTO e : ptList) {
            String oldId = e.getId();
            e.setId(e.getNewId());
            e.setCopyFromId(id);
            if (i == 0) {
                endId = e.getId();
                i++;
            }
            e.setCode(DidUtil.getDid(CodeTypeEnum.PRODUCT_CODE.toString(), MapUtil.empty()));
            e.setCreateTime(null);
            e.setBindingProductOrderNum(productOrderNum);
            this.save(e);
            String pid = e.getPid();
            ProductTree byId = this.getById(pid);
            if (byId == null) {
                throw new CdkitCloudException(String.format("找不到指定的父级产品种类，pid：%s", pid));
            }
            e.setPath(byId.getPath() + e.getId() + Constants.PATH_SPILT);
            this.updateById(e);

            String newId = e.getId();
            ProductPart productPart = productPartMap.get(oldId);
            if (ObjectUtil.isNotNull(productPart)) {
                productPart.setId(newId);
                productPart.setBindingProductOrderNum(productOrderNum);
            }
        }
        List<ProductPart> insertParts = CollectionUtil.newArrayList(productPartMap.values());
        productPartService.saveBatch(insertParts);
        // 获得对应节点的工艺路线
        List<ProductProcessRoute> productProcessRoutes = productProcessRouteService.listProcessRoute(productTreeDTO.getId());
        if (CollectionUtils.isNotEmpty(productProcessRoutes)) {
            String processRouteId = productProcessRoutes.get(0).getProcessRouteId();
            ReqProductProcessRouteVo reqProductProcessRouteVo = new ReqProductProcessRouteVo();
            reqProductProcessRouteVo.setCurrProductId(productTreeDTO.getId());
            reqProductProcessRouteVo.setProcessRouteId(processRouteId);
            productProcessRouteService.importProcessRoute(reqProductProcessRouteVo);
        }
        return endId;
    }

    /**
     * 递归获得产品树内所有bom的id
     *
     * @param productTree
     */
    private void recursionGetProductIds(ProductTreeDTO productTree, List<ProductTreeDTO> ptList) {
        productTree.setNewId(IdWorker.getIdStr());
        ptList.add(productTree);

        List<ProductTreeDTO> children = productTree.getChildren();
        if (CollUtil.isEmpty(children)) {
            return;
        } else {
            for (ProductTreeDTO child : children) {
                child.setPid(productTree.getNewId());
                this.recursionGetProductIds(child, ptList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyTreeEx(String id) {
        if (StrUtil.isEmpty(id)) {
            throw new CdkitCloudException("id不能为空！");
        }
        List<ProductTreeDTO> productTreeDTOS = handleTree(CollectionUtil.newArrayList(id), NodeTypeEnum.PROCESS_TREE, null);
        Assert.isTrue(CollectionUtil.isNotEmpty(productTreeDTOS), "没有找到对应的bom信息!");
        List<ProductTreeDTO> ptList = CollectionUtil.newArrayList();
        ProductTreeDTO productTreeDTO = productTreeDTOS.get(0);
        // 递归获得所有bom的id
        recursionGetProductIds(productTreeDTO, ptList);
        List<String> ids = ptList.stream().map(ProductTreeDTO::getId).collect(Collectors.toList());

        List<ProductPart> productParts = productPartService.listByIds(ids);
        Map<String, ProductPart> productPartMap = productParts.stream().collect(Collectors.toMap(ProductPart::getId, Function.identity()));
        String endId = "";
        int i = 0;
        for (ProductTreeDTO e : ptList) {
            String oldId = e.getId();
            e.setId(e.getNewId());
            e.setCopyFromId(id);
            if (i == 0) {
                endId = e.getId();
                i++;
            }
            e.setCode(DidUtil.getDid(CodeTypeEnum.PRODUCT_CODE.toString(), MapUtil.empty()));
            e.setCreateTime(null);
            this.save(e);
            String pid = e.getPid();
            ProductTree byId = this.getById(pid);
            if (byId == null) {
                throw new CdkitCloudException(String.format("找不到指定的父级产品种类，pid：%s", pid));
            }
            e.setPath(byId.getPath() + e.getId() + Constants.PATH_SPILT);
            this.updateById(e);

            String newId = e.getId();
            ProductPart productPart = productPartMap.get(oldId);
            if (ObjectUtil.isNotNull(productPart)) {
                productPart.setId(newId);
            }
        }
        List<ProductPart> insertParts = CollectionUtil.newArrayList(productPartMap.values());
        productPartService.saveBatch(insertParts);
        return endId;
    }


    public List<ProductTree> getTreeList(String productId) {
        if (org.apache.commons.lang3.StringUtils.isBlank(productId)) {
            throw new CdkitCloudException("productId不能为空！");
        }
        List ids = new ArrayList<String>();
        ids.add(productId);
        List<ProductTreeDTO> tree = this.tree(ids, NodeTypeEnum.PROCESS_TREE, null);
        List<ProductTreeDTO> treeList = new ArrayList<>();
        treeToList(tree.get(0), treeList);
        List<ProductTree> list = new ArrayList<>();

        for (ProductTreeDTO item : treeList) {
            ProductTree productTree = new ProductTree();
            BeanUtil.copyProperties(item, productTree);
            list.add(productTree);
        }

        return list;
    }

    /**
     * 懒加载树
     *
     * @param rootIds      根节点
     * @param nodeTypeEnum 节点类型
     * @return 树
     */
    @Override
    public List<ProductTreeDTO> treeLazy(String rootIds, NodeTypeEnum nodeTypeEnum, String materialCodeOrName) {
        List<ProductTreeDTO> treeDTOList;
        if (StringUtils.isEmpty(rootIds)) {
            //产品树、工艺树共用产品分类、种类
            LambdaQueryWrapper<ProductTree> wrapper = new LambdaQueryWrapper<ProductTree>()
                    .eq(ProductTree::getPid, Constants.ROOT_PID)
                    .isNull(ProductTree::getBindingProductOrderNum);
            List<ProductTree> list = this.list(wrapper);
            treeDTOList = list.stream()
                    .map(obj -> {
                        ProductTreeDTO productTreeDTO = new ProductTreeDTO();
                        BeanUtil.copyProperties(obj, productTreeDTO);
                        productTreeDTO.setChildren(new ArrayList<>());
                        return productTreeDTO;
                    })
                    .collect(Collectors.toList());
        } else {
            treeDTOList = productTreeMapper.selectTreeList(nodeTypeEnum.getCode(), rootIds, materialCodeOrName);
        }
        return treeDTOList;
    }


    /**
     * 产品树结构转为列表结构
     *
     * @param productTreeDTO 树结构
     * @param treeList       列表结构
     */
    private void treeToList(ProductTreeDTO productTreeDTO, List<ProductTreeDTO> treeList) {
        ProductTreeDTO vo = new ProductTreeDTO();
        BeanUtil.copyProperties(productTreeDTO, vo);
        treeList.add(vo);
        if (productTreeDTO.getChildren() != null && productTreeDTO.getChildren().size() > 0) {
            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
                treeToList(item, treeList);
            }
        }
    }

    /**
     * 图纸审核
     *
     * @param designDocument
     * @param request
     * @return
     */
    @Override
    public String auditDocument(DesignDocument designDocument, HttpServletRequest request) {
        ProductTree productTree = this.getById(designDocument.getId());
        if (productTree == null) {
            throw new CdkitCloudException("找不到指定的设计文件");
        }
        DesignDocument designDocumentNew = designDocumentService.getById(designDocument.getId());
        log.info("图纸审核:{}", JSON.toJSONString(designDocumentNew));
        designDocumentNew.setRemark(designDocument.getRemark());
        try {
            workFlowUtil.approvalWorkflowByWiid(designDocumentNew.getWiid(), designDocumentNew.getRemark(), request);
            //判断是否是最后一个审批人 自动发布
            WorkflowStatusEntity workflowStatusEntity = workFlowUtil.getProcInstBaseInfo(designDocumentNew.getWiid(), request);
            if (null != workflowStatusEntity.getWorkflowState() && "已完成".equals(workflowStatusEntity.getWorkflowState())) {
                productTree.setStatus(DesignStatusEnum.PUBLISHED.getCode());
                //自动发布时需要增加版本库信息
                DesignDocumentHistory designDocumentHistory = BeanUtil.copyProperties(designDocumentNew, DesignDocumentHistory.class, "id");
                designDocumentHistory.setDesignDocumentId(designDocumentNew.getId());
                designDocumentHistory.setVersion(designDocumentNew.getVersion());
                designDocumentHistoryService.save(designDocumentHistory);
                this.updateById(productTree);
            }
            designDocumentService.updateById(designDocumentNew);
        } catch (Exception e) {
            log.error("审核失败", e);
            throw new CdkitCloudException("审核失败");
        }
        return null;
    }

    @Override
    public String writeSign(DesignDocument designDocument, HttpServletRequest request) {
        DesignDocument designDocumentNew = designDocumentService.getById(designDocument.getId());
        log.info("写入cad签名开始");
        List<WriteSignDTO> writeSignDTOList = new ArrayList<>();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        WorkflowStatusEntity workflowStatusEntity = null;
        try {
            workflowStatusEntity = workFlowUtil.getProcInstBaseInfo(designDocumentNew.getWiid(), request);
            if (null != workflowStatusEntity.getWorkflowState()) {
                WriteSignDTO signDTO1 = new WriteSignDTO();
                WriteSignDTO signDTO2 = new WriteSignDTO();
                if (workflowStatusEntity.getCurrentActivityName().equals("工艺")) {
                    signDTO1.setSignKey(CadParseKey.SHQM.getCAD_KEY());
                    signDTO2.setSignKey(CadParseKey.SHWCRQ.getCAD_KEY());
                } else if (workflowStatusEntity.getCurrentActivityName().equals("批准")) {
                    signDTO1.setSignKey(CadParseKey.GYQM.getCAD_KEY());
                    signDTO2.setSignKey(CadParseKey.GYWCRQ.getCAD_KEY());
                } else if ("已完成".equals(workflowStatusEntity.getWorkflowState()) && workflowStatusEntity.getCurrentActivityName().equals("结束")) {
                    signDTO1.setSignKey(CadParseKey.PZQM.getCAD_KEY());
                    signDTO2.setSignKey(CadParseKey.PZWCRQ.getCAD_KEY());
                }
                signDTO1.setValue(loginUser.getUsername());
                signDTO1.setSignType(3);
                writeSignDTOList.add(signDTO1);
                signDTO2.setValue(DateUtil.format(new Date(), "yyyy.M.d"));
                signDTO2.setSignType(2);
                writeSignDTOList.add(signDTO2);
                cadHandleService.writeCadSign(designDocumentNew.getId(), writeSignDTOList);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        designDocumentNew.setApprovalPerson(loginUser.getUsername());
        log.info("写入cad签名结束");
        designDocumentMapper.updateById(designDocumentNew);
        return "签名完成";
    }

    @Override
    public String reject(DesignDocument designDocument, HttpServletRequest request) {
        DesignDocument designDocumentNew = designDocumentService.getById(designDocument.getId());
        log.info("审核驳回");
        try {
            workFlowUtil.rejectToStart(designDocumentNew.getWiid(), designDocument.getRemark(), request);
            ProductTree productTree = this.getById(designDocument.getId());
            productTree.setStatus(DesignStatusEnum.DESIGNING.getCode());
            this.updateById(productTree);
        } catch (Exception e) {
            log.error("审核驳回失败", e);
            throw new CdkitCloudException("审核驳回失败");
        }
        //todo 增加红批
        return "审批完成";
    }

    /*@Override
    public List<WorkflowRecord> findAuditRecords(DesignDocument designDocument, HttpServletRequest request) {
        DesignDocument designDocumentNew = designDocumentService.getById(designDocument.getId());
        List<WorkflowRecord> workflowRecordList = null;
        try {
            workflowRecordList = workFlowUtil.getTodoApprovalRecordByWiid(designDocumentNew.getWiid(),request);
        } catch (Exception e) {
            log.error("获取审核记录失败", e);
            throw new CdkitCloudException("获取审核记录失败");
        }
        return workflowRecordList;
    }*/

    @Override
    public String reuseDocument(String pid, String designDocumentHistoryId) {
        DesignDocumentHistory designDocumentHistoryNew = designDocumentHistoryService.getById(designDocumentHistoryId);

        ProductTree parentProductTree = this.getById(pid);
        String originalFilename = designDocumentHistoryNew.getName();
        //保存主表信息
        String parentPath = parentProductTree.getPath();
        ProductTree productTree = new ProductTree();
        productTree.setType(ProductTreeTypeEnum.DESIGN_DOCUMENT.getCode());
        String fileType = StringUtils.getFilenameExtension(originalFilename);
        productTree.setStatus(Constants.DWG.equals(fileType) ? DesignStatusEnum.DESIGNING.getCode() : DesignStatusEnum.PUBLISHED.getCode());
        productTree.setName(designDocumentHistoryNew.getName());
        productTree.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
        this.save(productTree);
        productTree.setSort(productTree.getId());
        productTree.setPid(parentProductTree.getId());
        productTree.setPath(parentPath + productTree.getId() + Constants.PATH_SPILT);
        productTree.setCode(originalFilename + UUID.randomUUID());
        this.updateById(productTree);
        //保存设计文档表
        DesignDocument designDocument = new DesignDocument();
        designDocument.setId(productTree.getId());
        designDocument.setName(originalFilename);
        designDocument.setRedBatch(false);
        designDocument.setSign(false);
        designDocument.setFileSize(designDocumentHistoryNew.getFileSize());
        designDocument.setCategory(Constants.DWG.equals(fileType) ? Constants.DRAWING : Constants.DOCUMENT);
        designDocument.setFileType(fileType);
        //文件删除不删除服务器的，文件地址引用原来的，图纸不需要同步
        designDocument.setFilePath(designDocumentHistoryNew.getFilePath());
        //初始化图纸版本号为1
        designDocument.setVersion(1);
        designDocumentService.save(designDocument);

        return "复用成功";
    }

}
