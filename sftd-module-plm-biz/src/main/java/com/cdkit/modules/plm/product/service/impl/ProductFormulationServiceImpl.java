package com.cdkit.modules.plm.product.service.impl;


import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cdkit.md.api.IMdMaterialTypeApi;
import com.cdkit.md.entity.MdMaterialType;
import com.cdkit.modules.plm.config.StrategyByFactory;
import com.cdkit.modules.plm.process.entity.*;
import com.cdkit.modules.plm.process.mapper.*;
import com.cdkit.modules.plm.process.service.*;
import com.cdkit.modules.plm.process.service.impl.PlmApiServiceImpl;
import com.cdkit.modules.plm.process.vo.resp.RespProcessDetailVO;
import com.cdkit.modules.plm.process.vo.resp.RespProcessVO;
import com.cdkit.modules.plm.product.dto.ProductListDTO;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.ProductFormulation;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.enums.EnableStatusEnum;
import com.cdkit.modules.plm.product.mapper.ProductFormulationMapper;
import com.cdkit.modules.plm.product.mapper.ProductTreeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.config.TenantContext;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.workflow.WorkFlowUtil;
import com.cdkit.common.workflow.entity.WorkflowProcessEntity;
import com.cdkit.common.workflow.entity.WorkflowStatusEntity;
import com.cdkit.md.api.IMdMaterialApi;
import com.cdkit.md.entity.MdMaterial;
import com.cdkit.modules.plm.common.Constants;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.product.entity.*;
import com.cdkit.modules.plm.product.enums.FormulaStatusEnum;
import com.cdkit.modules.plm.product.mapper.*;
import com.cdkit.modules.plm.product.service.*;
import com.cdkit.modules.plm.product.vo.req.*;
import com.cdkit.modules.plm.product.vo.resp.FormulationReplaceVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.google.common.base.Joiner;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import java.util.Collections;
import java.util.List;

/**
 * @Description: product_formulation
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
@Service
@Slf4j
public class ProductFormulationServiceImpl extends ServiceImpl<ProductFormulationMapper, ProductFormulation> implements IProductFormulationService {
    @Resource
    private ProductTreeMapper productTreeMapper;
	@Resource
	private IProcessBomService processBomService;
	@Resource
	private ProductProcessRouteMapper productProcessRouteMapper;
	@Resource
	private IProductProcessRouteService productProcessRouteService;
	@Resource
	private IMdProcessRouteDetailService mdProcessRouteDetailService;
	@Resource
	private MdProcessMapper mdProcessMapper;
	@Resource
	private ProductProcessDetailMapper productProcessDetailMapper;
	@Resource
	private IProductProcessDetailService productProcessDetailService;
	@Resource
	private MdProcessDetailMapper mdProcessDetailMapper;

	// 使用线程安全的AtomicInteger保证多线程环境下的原子性操作
	private static final Map<String, AtomicInteger> COUNTER_MAP = new HashMap<>();
	private static final String PREFIX = "PF";
	private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyMM");

	@Resource
	private ProductFormulationMapper productFormulationMapper;
	@Resource
	private ProductFormulationDetailMapper productFormulationDetailMapper;
	@Resource
	private IProductTreeService productTreeService;
	@Resource
	private IProductPartService productPartService;
	@Resource
	private IMdMaterialApi mdMaterialApi;
	@Resource
	private ProductPartMapper productPartMapper;
	@Resource
	private ProductFormulationMasterMapper productFormulationMasterMapper;

	@Resource
	WorkFlowUtil workFlowUtil;
	@Resource
	private IProductOrderExtendService productOrderExtendService;
	@Resource
	private ProductOrderExtendMapper productOrderExtendMapper;
	@Resource
	private PlmApiServiceImpl plmApiService;
	@Resource
	private IProductFormulationDetailService productFormulationDetailService;

	@Resource
	private StrategyByFactory strategyByFactory;
	@Resource
	private IMdMaterialTypeApi mdMaterialTypeApi;
	@Resource
	private IProcessExperimentTermService processExperimentTermService;
	@Resource
	private IProcessMaterialBindingService processMaterialBindingService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ProductFormulation saveMain(ProductFormulation productFormulation, List<ProductFormulationDetail> productFormulationDetailList) {
		if(null!=productFormulation.getFormulaCode()){
			List<ProductFormulation> productFormulations = productFormulationMapper.selectList(new LambdaQueryWrapper<ProductFormulation>().eq(ProductFormulation::getFormulaCode, productFormulation.getFormulaCode()));
			if(!productFormulations.isEmpty()){
				throw new CdkitCloudException("该配方编码已存在,请勿重新填写");
			}
		}
		//主表
		ProductFormulationMaster productFormulationMaster = new ProductFormulationMaster();
		productFormulationMaster.setDelFlag(0);
		productFormulationMasterMapper.insert(productFormulationMaster);
		//生成tree，part表数据
		createTreePart(productFormulation,productFormulationDetailList);
		//String formulaCode = generateCode();
		//productFormulation.setFormulaCode(formulaCode);
		productFormulation.setFormulaStatus(FormulaStatusEnum.ADD.getCode());
		productFormulation.setProductFormulationMasterId(productFormulationMaster.getId());
		productFormulation.setFormulaVersion("V1.0");
		productFormulation.setTenantId(Integer.valueOf(TenantContext.getTenant()));
		productFormulationMapper.insert(productFormulation);
		productFormulationMaster.setProductFormulationId(productFormulation.getId());
		productFormulationMaster.setBeforeFormulationId(productFormulation.getId());
		productFormulationMasterMapper.updateById(productFormulationMaster);
		if(productFormulationDetailList!=null && productFormulationDetailList.size()>0) {
			for(ProductFormulationDetail entity:productFormulationDetailList) {
				//外键设置
				entity.setProductFormulationId(productFormulation.getId());
				productFormulationDetailMapper.insert(entity);
			}
		}
		//保存配方文件
		createFormulaFile(productFormulation.getFileUrlList(),productFormulation.getId());
		return productFormulation;
	}

	private void createFormulaFile(String fileUrlList, String id) {
		List<ProductOrderExtend> addList = new ArrayList<>();
		ProductOrderExtend productOrderExtend = new ProductOrderExtend();
		productOrderExtend.setDefinedKey("formulaFile");
		productOrderExtend.setDefinedValue(fileUrlList);
		productOrderExtend.setProductOrderId(id);
		productOrderExtend.setDefinedName("配方基础信息文件");
		productOrderExtend.setDefinedType("1");
		productOrderExtend.setExtendType("4");
		productOrderExtend.setDelFlag(0);
		addList.add(productOrderExtend);


		productOrderExtendService.saveBatch(addList);
	}

	private void createTreePart(ProductFormulation productFormulation, List<ProductFormulationDetail> productFormulationDetailList) {
		IFactoryStrategy businessMap = strategyByFactory.getBusinessMap(Integer.parseInt(TenantContext.getTenant()));
		//生成产品tree表信息
		ProductTree productTree = new ProductTree();

		//产品种类
		ProductTree pt=new ProductTree();
		//查询产品分类
		List<ProductTree> list = productTreeService.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getType, 1));
		if(list.isEmpty()){
          //创建父节点
			businessMap.createTree(productFormulation);
		}
		pt = this.queryTypeTree(productFormulation,list);

		productTree.setPid(pt.getId());
		productTree.setCode(productFormulation.getFormulaCode());
		productTree.setName(productFormulation.getMaterialName());
		productTree.setType(3);
		productTree.setNodeType(NodeTypeEnum.PROCESS_TREE.getCode());
		productTree.setConverted(1);
		productTree.setDelFlag(0);
		productTree.setEnable(1);
		productTree.setMaterialCode(productFormulation.getMaterialCode());
		productTreeService.save(productTree);
		productTree.setPath(pt.getPath() + productTree.getId() + Constants.PATH_SPILT);
		productTree.setSort(productTree.getId());
		productTreeService.updateById(productTree);
		productFormulation.setBoomId(productTree.getId());

		//生成part表
		ProductPart productPart = new ProductPart();
		productPart.setId(productTree.getId());
		productPart.setMaterialCode(productFormulation.getMaterialCode());
		productPart.setMaterialName(productFormulation.getMaterialName());
		//计算装备数量
		BigDecimal assembleQuantity = businessMap.getAssembleQuantity();
		productPart.setAssembleQuantity(assembleQuantity);
		productPart.setWeight(String.valueOf(assembleQuantity));
		productPart.setStandardQuantity(assembleQuantity);
		productPartService.save(productPart);

		//原料新增
		createTreePartList(productFormulationDetailList);
	}

	private ProductTree queryTypeTree(ProductFormulation productFormulation,List<ProductTree> list) {
		ProductTree pt = new ProductTree();
		//查询产品种类
		List<ProductTree> ptList = productTreeService.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getType, 2)
				.eq(ProductTree::getCode,productFormulation.getMaterialTypeId()));
		if(ptList.isEmpty()){
			ProductTree productTree1 = list.get(0);
			//1-2创建产品种类
			pt = new ProductTree();
			MdMaterialType mdMaterialType = mdMaterialTypeApi.queryMaterialType(productFormulation.getMaterialTypeId());
			if(null!=mdMaterialType) {
				pt.setName(mdMaterialType.getTypeName());
				pt.setCode(mdMaterialType.getId());
			}else{
				pt.setName(TenantContext.getTenant()+"分类");
				pt.setCode(TenantContext.getTenant()+"001");
			}
			pt.setType(2);
			pt.setPid(productTree1.getId());
			//pt.setCode(mdMaterialType.getId());
			pt.setNodeType(NodeTypeEnum.PRODUCT_TREE.getCode());
			pt.setConverted(1);
			pt.setDelFlag(0);
			pt.setEnable(0);
			productTreeService.save(pt);
			pt.setPath(productTree1.getPath()+ pt.getId()+ Constants.PATH_SPILT);
			pt.setSort(pt.getId());
			productTreeService.updateById(pt);
		}else{
			pt = ptList.get(0);
		}
		return pt;
	}

	private void createTreePartList(List<ProductFormulationDetail> productFormulationDetailList) {
		List<String> materialCodes = productFormulationDetailList.stream().map(ProductFormulationDetail::getMaterialCode).collect(Collectors.toList());
		Map<String, MdMaterial> mdMaterialMap = mdMaterialApi.queryMdMaterialAll(Joiner.on(",").join(materialCodes));
		for(ProductFormulationDetail pfd:productFormulationDetailList){

				ProductTree productTree = new ProductTree();
				MdMaterial md = mdMaterialMap.get(pfd.getMaterialCode());
				productTree.setName(md.getMaterialName());
				productTree.setType(4);
				productTree.setNodeType(NodeTypeEnum.PROCESS_TREE.getCode());
				productTree.setConverted(1);
				productTree.setDelFlag(0);
				productTree.setEnable(0);
			    productTree.setPid("");
				productTree.setMaterialCode(pfd.getMaterialCode());
			    if(null!=pfd.getBoomId()){
					pfd.setHistoryBoomId(pfd.getBoomId());
				}
				productTreeService.save(productTree);
				productTree.setSort(productTree.getId());
			    productTree.setCode(productTree.getId());
				productTreeService.updateById(productTree);
				pfd.setBoomId(productTree.getId());

				//生成part表
				ProductPart productPart = new ProductPart();
				productPart.setId(productTree.getId());
				productPart.setMaterialCode(pfd.getMaterialCode());
				productPart.setMaterialName(mdMaterialMap.get(pfd.getMaterialCode()).getMaterialName());
				//计算装备数量
			    BigDecimal result = BigDecimal.ZERO;
			    if(1001!=Integer.parseInt(TenantContext.getTenant())) {
					 result = pfd.getMolecule().divide(pfd.getDenominator(), 6, RoundingMode.HALF_UP);
				}else{
					result=pfd.getMolecule();
				}
			    productPart.setAssembleQuantity(result);
				productPart.setWeight(String.valueOf(result));
				productPart.setStandardQuantity(result);
			    productPart.setFungibleMaterialCodes(pfd.getFungibleMaterialCodes());
				productPartService.save(productPart);

		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(ProductFormulation productFormulation,List<ProductFormulationDetail> productFormulationDetailList,List<ProductFormulationDetail> deleteFormulationDetailList,HttpServletRequest request) {
		if("1".equals(productFormulation.getFormulaCodeUpdateFlag())) {
			if (null != productFormulation.getFormulaCode()) {
				List<ProductFormulation> productFormulations = productFormulationMapper.selectList(new LambdaQueryWrapper<ProductFormulation>().eq(ProductFormulation::getFormulaCode, productFormulation.getFormulaCode()));
				if (!productFormulations.isEmpty()) {
					throw new CdkitCloudException("该配方编码已存在,请勿重新填写");
				}
			}
		}
		List<ProductFormulationDetail> addList = new ArrayList<>();

		//1-1.先删除子表数据
		productFormulationDetailMapper.deleteByMainId(productFormulation.getId());
		//删除基础信息tree，part
		if (null != productFormulation.getBoomId()) {
			List<ProductTree> productTrees = productTreeMapper.selectList(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getId, productFormulation.getBoomId())
					.eq(ProductTree::getType, 3)
					.eq(ProductTree::getNodeType, 2));
			if(!productTrees.isEmpty()){
				ProductTree productTree = productTrees.get(0);
				productTree.setEnable(1);
				productTreeMapper.updateById(productTree);
			}
		}
		//1-2.处理删除的物料数据
		/*if (productFormulationDetailList != null && productFormulationDetailList.size() > 0) {
			for (ProductFormulationDetail entity : productFormulationDetailList) {
				if (null != entity.getBoomId()) {
					productTreeMapper.deleteById(entity.getBoomId());
					productPartMapper.deleteById(entity.getBoomId());
				}
			}
		}*/
		//删除1-3 文件信息
		List<ProductOrderExtend> fileList = productOrderExtendService.list(new LambdaQueryWrapper<ProductOrderExtend>()
				.eq(ProductOrderExtend::getProductOrderId, productFormulation.getId())
				.eq(ProductOrderExtend::getDefinedKey,"formulaFile"));
		if (!fileList.isEmpty()) {
			List<String> deleteIds = fileList.stream()
					.map(ProductOrderExtend::getId) // 提取 id 字段
					.filter(id -> id != null)       // 过滤 null 值
					.collect(Collectors.toList());
			productOrderExtendMapper.deleteBatchIds(deleteIds);
		}

		//保存配方文件
		if (productFormulation.getFileUrlList()!=null) {
			createFormulaFile(productFormulation.getFileUrlList(), productFormulation.getId());
		}
		//2.对于新增物料插入tree和part
		createTreePart(productFormulation, productFormulationDetailList);

		//3.子表数据重新插入
		if (productFormulationDetailList != null && productFormulationDetailList.size() > 0) {
			for (ProductFormulationDetail entity : productFormulationDetailList) {
				//外键设置
				entity.setProductFormulationId(productFormulation.getId());
				productFormulationDetailMapper.insert(entity);
			}
		}


		//4 更新配方
		productFormulationMapper.updateById(productFormulation);

		//5 发起审核
		if(1001!=Integer.parseInt(TenantContext.getTenant())) {
			this.initiateApproval(productFormulation.getId(), request);
		}



	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		productFormulationDetailMapper.deleteByMainId(id);
		productFormulationMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			productFormulationDetailMapper.deleteByMainId(id.toString());
			productFormulationMapper.deleteById(id);
		}
	}

	@Override
	public void updateStatus(List<ProductFormulation> list) {
		for(ProductFormulation productFormulation:list) {
			ProductFormulation pf = productFormulationMapper.selectById(productFormulation.getId());
			pf.setEnableStatus(productFormulation.getEnableStatus());
			productFormulationMapper.updateById(pf);
			//工艺boom启用停用
			List<ProductTree> productTrees = productTreeMapper.selectList(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getId, pf.getBoomId())
					.eq(ProductTree::getType, 3)
					.eq(ProductTree::getNodeType, 2));
			if(!productTrees.isEmpty()){
				ProductTree productTree = productTrees.get(0);
				productTree.setEnable(productFormulation.getEnableStatus());
				productTreeMapper.updateById(productTree);
			}

		}
	}

	@Override
	public IPage<ProductFormulation> queryTask(Page<ProductFormulation> page, ProductFormulation productFormulation) {
		IPage<ProductFormulation> list = productFormulationMapper.queryFormulationList(page, productFormulation);
		return list;
	}



	/**
	 * 生成符合规则的编码
	 * @return 返回格式为PF+YYMM+4位流水号的编码
	 */
	public static synchronized String generateCode() {
		// 获取当前日期并格式化为YYMM
		LocalDate currentDate = LocalDate.now();
		String datePart = currentDate.format(DATE_FORMATTER);

		// 获取当前月份对应的流水号，如果不存在则创建并初始化为1
		AtomicInteger counter = COUNTER_MAP.computeIfAbsent(datePart, k -> new AtomicInteger(1));

		// 生成4位流水号，不足4位时前面补0
		int currentValue = counter.getAndIncrement();
		// 每月流水号上限为9999，超过则抛出异常
		if (currentValue > 9999) {
			throw new IllegalStateException("当月流水号已达到上限");
		}
		String serialPart = String.format("%04d", currentValue);

		// 组合生成完整编码
		return PREFIX + datePart + serialPart;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void initiateApproval(String productFormulationId, HttpServletRequest request)  {
		ProductFormulation pf = productFormulationMapper.selectById(productFormulationId);
        List<WorkflowProcessEntity> processEntities = null;
        try {
            processEntities = workFlowUtil.getMyStartProcess(request);
			WorkflowProcessEntity entity = processEntities.stream().filter(it -> "配方审核".equals(it.getAppName())).findFirst().orElse(null);
			if (entity != null) {

				//todo 流程标题暂时不确认是否用文件名
				String	wiid = workFlowUtil.startWorkflow(entity.getAppId(), pf.getId(), "配方审核", "", request);
				//log.info( "审批发起流程完成:{}", wiid);
				pf.setWiid(wiid);
				pf.setFormulaStatus(FormulaStatusEnum.INREVIEW.getCode());
				productFormulationMapper.updateById(pf);
			}
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
	}

	@Override
	public void approvalPass(ProductFormulation productFormulation, HttpServletRequest request) {
		String id = productFormulation.getId();
		ProductFormulation pf = productFormulationMapper.selectById(id);
		if(null==pf.getWiid()){
			throw new CdkitCloudException("该单据未发起审核操作");
		}
		List<ProductTree> updateList = new ArrayList<>();
		//审核通过
		try {
			workFlowUtil.approvalWorkflowByWiid(pf.getWiid(), pf.getRemark(), request);
			//1-1判断是否是最后一个审批人 自动发布
			WorkflowStatusEntity workflowStatusEntity = workFlowUtil.getProcInstBaseInfo(pf.getWiid(), request);
			if (null != workflowStatusEntity.getWorkflowState() && "已完成".equals(workflowStatusEntity.getWorkflowState())) {
				//1-2变更配方状态
				pf.setFormulaStatus(FormulaStatusEnum.AUDITED.getCode());
				pf.setEnableStatus(EnableStatusEnum.PASS.getCode());
				//油化场 根据物料编码查询出的配方设为禁用
				/*if ("1005".equals(TenantContext.getTenant())) {
					List<ProductFormulation> productFormulationList = productFormulationMapper.selectList(new LambdaQueryWrapper<ProductFormulation>()
							.eq(ProductFormulation::getMaterialCode, pf.getMaterialCode()));
					for (ProductFormulation pfl : productFormulationList) {
						pfl.setEnableStatus(EnableStatusEnum.FAIL.getCode());
						productFormulationMapper.updateById(pfl);
					}
				}*/

				productFormulationMapper.updateById(pf);
				//1-3将主配方下boom置空
				/*List<ProductFormulation> pfList = productFormulationMapper.selectList(new LambdaQueryWrapper<ProductFormulation>().eq(ProductFormulation::getProductFormulationMasterId, productFormulation.getProductFormulationMasterId()));
				for(ProductFormulation pfl:pfList){
					ProductTree productTree = productTreeMapper.selectById(pfl.getBoomId());
					productTree.setEnable(0);
					updateList.add(productTree);
				}
				productTreeService.updateBatchById(updateList);*/
				//将当前配方tree状态变为可用
				ProductTree productTree = productTreeMapper.selectById(pf.getBoomId());
				productTree.setEnable(0);
				productTreeMapper.updateById(productTree);
				//更新上一版本配方id
				ProductFormulationMaster productFormulationMaster = productFormulationMasterMapper.selectById(pf.getProductFormulationMasterId());
				productFormulationMaster.setProductFormulationId(pf.getId());
				productFormulationMaster.setBeforeFormulationId(pf.getId());
				productFormulationMasterMapper.updateById(productFormulationMaster);
			}
		} catch (Exception e) {
			log.error("审核失败", e);
			throw new CdkitCloudException("审核失败");
		}
	}

	@Override
	public void reject(ProductFormulation productFormulation, HttpServletRequest request) {
		ProductFormulation pf = productFormulationMapper.selectById(productFormulation.getId());
		if(null==pf.getWiid()){
			throw new CdkitCloudException("该单据未发起审核操作");
		}
		try {

			workFlowUtil.rejectToStart(pf.getWiid(), pf.getRemark(), request);
			//变更配方状态
			pf.setFormulaStatus(FormulaStatusEnum.ADD.getCode());
			pf.setWiid(null);
			productFormulationMapper.updateById(pf);
		} catch (Exception e) {
			log.error("审核驳回失败", e);
			throw new CdkitCloudException("审核驳回失败");
		}

	}

	/**
	 * 查询工序步骤
	 *
	 * @param processId 工序ID
	 * @param productId 产成品ID(bomId)
	 * @return RespProcessVO
	 */
	@Override
	public RespProcessVO getProcessDetail(String processId, String productId) {
		// 查询步骤
		MdProcess mdProcess = Optional.ofNullable(mdProcessMapper.selectById(processId)).orElse(new MdProcess());
		RespProcessVO respProcessVO = new RespProcessVO();

		ProductProcessRoute productProcessRoute = productProcessRouteMapper.selectOne(new LambdaQueryWrapper<ProductProcessRoute>()
				.eq(ProductProcessRoute::getProductId, productId)
				.eq(ProductProcessRoute::getProcessId, processId)
				.eq(ProductProcessRoute::getIsNew, 1));
		if (productProcessRoute == null) {
			respProcessVO.setProcessCode(mdProcess.getProcessCode());
			respProcessVO.setProcessName(mdProcess.getProcessName());
			respProcessVO.setProcessId(mdProcess.getId());
		} else {
			respProcessVO.setId(productProcessRoute.getId());
			respProcessVO.setProcessCode(productProcessRoute.getProcessCode());
			respProcessVO.setProcessContent(productProcessRoute.getProcessContent());
			respProcessVO.setProcessName(mdProcess.getProcessName());
			respProcessVO.setProcessId(productProcessRoute.getProcessId());
			respProcessVO.setWorkHour(productProcessRoute.getWorkHour());
		}



		LambdaQueryWrapper<ProductProcessDetail> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(ProductProcessDetail::getProcessId, processId)
				.eq(ProductProcessDetail::getProductId, productId)
				.eq(ProductProcessDetail::getIsNew, 1)
				.orderByAsc(ProductProcessDetail::getNumber);

		List<ProductProcessDetail> productProcessDetails = productProcessDetailMapper.selectList(queryWrapper);
		ArrayList<RespProcessDetailVO> processDetails = new ArrayList<>();
		if (productProcessDetails.isEmpty()) {
			List<MdProcessDetail> mdProcessDetails = mdProcessDetailMapper.selectByMainId(processId);
			for (MdProcessDetail item : mdProcessDetails) {
				RespProcessDetailVO vo = new RespProcessDetailVO();
				BeanUtils.copyProperties(item, vo,"id","createTime","createBy","updateTime","upgradeBy");
				processDetails.add(vo);
			}
		} else {
			for (ProductProcessDetail item : productProcessDetails) {
				RespProcessDetailVO vo = new RespProcessDetailVO();
				BeanUtils.copyProperties(item, vo);
				processDetails.add(vo);
			}
		}


		respProcessVO.setProcessDetail(processDetails);
		return respProcessVO;
	}

	/**
	 * 根据配方ID查询工序绑定信息
	 *
	 * @param id    配方ID
	 * @param boomId 原boomId
	 * @return 工序绑定信息
	 */
	@Override
	public ImportProcessRequestVO getProcessRouteImporter(String id, String boomId) {
		ImportProcessRequestVO importProcessRequestVO = new ImportProcessRequestVO();
		ProductFormulation productFormulation = productFormulationMapper.selectById(id);
		importProcessRequestVO.setProcessRouteId(productFormulation.getProcessRouteId());
		importProcessRequestVO.setProductId(productFormulation.getBoomId());
		importProcessRequestVO.setProductFormulationId(id);

		List<ProcessInfoVO> processInfoList = new ArrayList<>();
		List<ProductProcessRoute> productProcessRoutes = productProcessRouteService.listProcessRouteByFormulationIdAndProductId(id, productFormulation.getHistoryBoomId());
		for (ProductProcessRoute route : productProcessRoutes) {
			ProcessInfoVO processInfoVO = new ProcessInfoVO();
			processInfoVO.setProcessId(route.getProcessId());
			processInfoVO.setOutputProductId(route.getProductId());
			processInfoVO.setWorkHour(route.getWorkHour());
			processInfoVO.setMaterialCode(route.getMaterialCode());

			List<ProcessMaterialBinding> processMaterialBindingList = processMaterialBindingService.selectByFormulationIdAndProcessId(id, route.getProcessId());
			List<String> formulationDetailIdList = processMaterialBindingList.stream().map(ProcessMaterialBinding::getFormulationDetailId).toList();
			processInfoVO.setFormulationDetailIdList(formulationDetailIdList);

			List<String> productIdList = processMaterialBindingList.stream().map(ProcessMaterialBinding::getProductId).toList();
			processInfoVO.setProductIdList(productIdList);

			RespProcessVO processDetail = getProcessDetail(route.getProcessId(), route.getProductId());
			processInfoVO.setProcessName(processDetail.getProcessName());
			processInfoVO.setProcessDetail(processDetail.getProcessDetail());
			processInfoList.add(processInfoVO);
		}
		importProcessRequestVO.setProcessInfoList(processInfoList);
		return importProcessRequestVO;
	}

	/**
	 * 替换产品工序中的物料编码
	 * @param oldMaterialCode 原物料编码
	 * @param newMaterialCode 新物料编码
	 * @param formulationIdList 配方ID
	 */
	@Override
	public void replaceProductProcessMaterial(String oldMaterialCode, String newMaterialCode, List<String> formulationIdList) {
		log.info("替换产品工序中的物料编码:原物料编码={},新物料编码={},配方ID={}", oldMaterialCode, newMaterialCode, JSON.toJSONString(formulationIdList));
		LambdaUpdateWrapper<ProductProcessRoute> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper
				.in(ProductProcessRoute::getFormulationId, formulationIdList)
				.eq(ProductProcessRoute::getMaterialCode, oldMaterialCode)
				.set(ProductProcessRoute::getMaterialCode, newMaterialCode);

		int updatedRows = productProcessRouteMapper.update(null, updateWrapper);
		log.info("替换产品工序中的物料编码更新成功：{}", updatedRows);
	}

	@Override
	public ProductFormulation queryById(String id) {
		ProductFormulation productFormulation = this.getById(id);
		List<ProductOrderExtend> fileList = productOrderExtendService.list(new LambdaQueryWrapper<ProductOrderExtend>().eq(ProductOrderExtend::getProductOrderId, productFormulation.getId())
				.eq(ProductOrderExtend::getDefinedKey,"formulaFile"));
		if (!fileList.isEmpty()) {
			/*List<String> fileUrlList = fileList.stream()
					.map(ProductOrderExtend::getId) // 提取 id 字段
					.filter(definedValue -> definedValue != null)       // 过滤 null 值
					.collect(Collectors.toList());*/
			productFormulation.setFileUrlList(fileList.get(0).getDefinedValue());
		}
		return productFormulation;
	}



	/**
	 * 工序与配方绑定
	 *
	 * @param importProcessRequestVO 请求参数
	 * @param request   HttpServletRequest
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void processRouteImporter(ImportProcessRequestVO importProcessRequestVO, HttpServletRequest request) {
		log.info("工序与配方绑定请求参数:{}", JSON.toJSONString(importProcessRequestVO));
		// 解构请求参数
		String productId = importProcessRequestVO.getProductId();
		String processRouteId = importProcessRequestVO.getProcessRouteId();
		String productFormulationId = importProcessRequestVO.getProductFormulationId();
		List<ProcessInfoVO> processInfoList  = importProcessRequestVO.getProcessInfoList();
		for (ProcessInfoVO item : processInfoList) {
			List<ProductFormulationDetail> productFormulationDetails = productFormulationDetailService.listByIds(item.getFormulationDetailIdList());
			item.setProductIdList(productFormulationDetails.stream().map(ProductFormulationDetail::getBoomId).toList());

			List<ProcessMaterialBinding> bindingList = new ArrayList<>();
			for (ProductFormulationDetail productFormulationDetail : productFormulationDetails) {
				ProcessMaterialBinding processMaterialBinding = new ProcessMaterialBinding();
				processMaterialBinding.setFormulationId(productFormulationId);
				processMaterialBinding.setMaterialCode(productFormulationDetail.getMaterialCode());
				processMaterialBinding.setFormulationDetailId(productFormulationDetail.getId());
				processMaterialBinding.setProcessId(item.getProcessId());
				processMaterialBinding.setProductId(productFormulationDetail.getBoomId());
				bindingList.add(processMaterialBinding);
			}
			item.setBindingList(bindingList);
		}
        // 1. 工序列表排序（最后一道序排在最前）
        Collections.reverse(processInfoList);

        // 2. 循环处理每道工序
		String pid = null;
        for (int i = 0; i < processInfoList.size(); i++) {
            ProcessInfoVO currentProcess = processInfoList.get(i);

            // 最后一道工序处理（逆序后的第一个元素）
            if (i == 0) {
                // 更新绑定物料的PID为产成品ID
                updateMaterialParentId(currentProcess.getProductIdList(), productId);
				pid = productId;
				// 赋值工序对应的产出物ID，创建工序明细使用
				processInfoList.get(i).setOutputProductId(productId);
				ProductTree productTree = productTreeMapper.selectById(productId);
				processInfoList.get(i).setMaterialCode(productTree.getMaterialCode());
            }
            // 非最后一道工序处理
            else {
                // 判断是否有产出物
                if (!StringUtils.isEmpty(currentProcess.getOutputProductId())) {
                    // 创建实际产出物，PID=上一道序的产出物ID
					updateMaterialParentId(Collections.singletonList(currentProcess.getOutputProductId()), pid);
					pid = currentProcess.getOutputProductId();
                    // 更新绑定物料的PID为产出物ID
                    updateMaterialParentId(currentProcess.getProductIdList(), pid);
                } else {
                    // 创建虚拟节点，PID=上一道序的产出物ID
					pid = createVirtualNode(pid);
					// 更新绑定物料的PID为产出物ID
					updateMaterialParentId(currentProcess.getProductIdList(), pid);
					// 赋值工序对应的产出物ID，创建工序明细使用
					processInfoList.get(i).setOutputProductId(pid);
                }
            }
        }

		// 3. 工序与产出物绑定
		bindProcessOutputs(processInfoList, processRouteId, productId, productFormulationId);

		// 4. 处理工序步骤
		saveProductProcessDetail(processInfoList);

		// 5. 保存配方对应的工艺路线
		LambdaUpdateWrapper<ProductFormulation> updateWrapperClear = new LambdaUpdateWrapper<>();
		updateWrapperClear
				.eq(ProductFormulation::getId, importProcessRequestVO.getProductFormulationId())
				.set(ProductFormulation::getProcessRouteId, processRouteId);

		productFormulationMapper.update(null, updateWrapperClear);

		if (importProcessRequestVO.getFlag() == 1) {
			// 6. 创建审核流
			IFactoryStrategy businessMap = strategyByFactory.getBusinessMap(Integer.parseInt(TenantContext.getTenant()));
			businessMap.createApproval(importProcessRequestVO.getProductFormulationId(),request);
			//initiateApproval(importProcessRequestVO.getProductFormulationId(), request);
		}
    }

	/**
	 * 工序与配方绑定升级
	 *
	 * @param importProcessRequestUpgradeVO 请求参数
	 */
	@Override
	public void processRouteImporterUpgrade(ImportProcessRequestUpgradeVO importProcessRequestUpgradeVO) {
		log.info("工序与配方绑定升级:{}", JSON.toJSONString(importProcessRequestUpgradeVO));
		// 解构请求参数
		String productId = importProcessRequestUpgradeVO.getProductId();
		String productIdOrigin = importProcessRequestUpgradeVO.getProductIdOrigin();
		String productFormulationIdOrigin = importProcessRequestUpgradeVO.getProductFormulationIdOrigin();
		String productFormulationId = importProcessRequestUpgradeVO.getProductFormulationId();

		// 查询产品BOM
		List<ProductTreeDTO> tree = productTreeService.tree(Collections.singletonList(productId), NodeTypeEnum.PROCESS_TREE,null);
		// 查询工艺路线

		List<ProductProcessRoute> productProcessRoutes = productProcessRouteService.listProcessRouteByFormulationIdAndProductId(productFormulationIdOrigin, productIdOrigin);
		// 反转数组
		Collections.reverse(productProcessRoutes);
		List<ProductListDTO> processListDTO = new ArrayList<>();
		productProcessRouteService.treeToListWithOutChildren(tree.get(0), processListDTO);
		// 对平铺后的产品BOM进行排序
		processListDTO = processListDTO.stream().sorted(Comparator.comparingInt(ProductListDTO::getLevel)
				.thenComparing(ProductListDTO::getSort)).collect(Collectors.toList());
		if (processListDTO.size() - 1 > productProcessRoutes.size()) {
			throw new CdkitCloudException("工艺路线与工艺BOM匹配失败，请检查工艺路线中工序是否有缺失");
		}

		List<ProductProcessRoute> list = new ArrayList<>();
		// BOM反转后匹配工艺路线,并组装各节点对应工序
		for (int i = 0; i < processListDTO.size(); i++) {
			ProductProcessRoute productProcessRoute = new ProductProcessRoute();
			ProductListDTO productListDTO = processListDTO.get(i);
			// 获取各个节点的工序
			if (productProcessRoutes.size() <= i || productProcessRoutes.get(i) == null) {
				continue;
			}
			ProductProcessRoute productProcessRouteOrigin = productProcessRoutes.get(i);

			BeanUtils.copyProperties(productProcessRouteOrigin, productProcessRoute, "id","createTime","createBy","updateTime","upgradeBy");
			productProcessRoute.setProductId(productListDTO.getId());
			productProcessRoute.setCurrProductId(productId);
			productProcessRoute.setFormulationId(productFormulationId);
			productProcessRoute.setMaterialCode(productListDTO.getMaterialCode());
			list.add(productProcessRoute);


		}

		if (!list.isEmpty()) {
			// productProcessRouteMapper.removeByCurProductId(productId);
			productProcessRouteService.saveBatch(list);
			for (int i = 0; i < list.size(); i++) {
				ProductProcessRoute productProcessRoute = list.get(i);
				ProductProcessRoute productProcessRouteOrigin = productProcessRoutes.get(i);
				saveProductProcessDetailUpgrade(productProcessRoute.getProductId(), productProcessRoute.getProcessId(), productProcessRouteOrigin.getProductId());
			}
		}
	}


	private void updateMaterialParentId(List<String> productIdList, String pid) {
		// 清空原来绑定的PID
		LambdaUpdateWrapper<ProductTree> updateWrapperClear = new LambdaUpdateWrapper<>();
		updateWrapperClear
				.eq(ProductTree::getPid, pid)
				.set(ProductTree::getPid, "");

		int updatedRowsClear = productTreeMapper.update(null, updateWrapperClear);
		log.info("清空BOM树父ID成功条数：{}", updatedRowsClear);

        // 实现更新BOM树父ID的逻辑
        LambdaUpdateWrapper<ProductTree> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(ProductTree::getId, productIdList)
                .set(ProductTree::getPid, pid);

        int updatedRows = productTreeMapper.update(null, updateWrapper);

        log.info("更新BOM树父ID成功条数：{}", updatedRows);

		// 更新标准用量
		for (String id : productIdList) {
			ProductPart productPart = productPartMapper.selectById(id);
			ProductPart productPartParent = productPartMapper.selectById(pid);
			BigDecimal standardQuantity = productPartParent == null || productPartParent.getStandardQuantity() == null ? new BigDecimal(1) : productPartParent.getStandardQuantity();
			BigDecimal assembleQuantity = productPart.getAssembleQuantity() == null ? new BigDecimal(1) : productPart.getAssembleQuantity();
			productPart.setStandardQuantity(standardQuantity.multiply(assembleQuantity));

			productPartMapper.updateById(productPart);
		}


    }


	private String createVirtualNode(String pid) {
		// 清空原来绑定的PID
		LambdaUpdateWrapper<ProductTree> updateWrapperClear = new LambdaUpdateWrapper<>();
		updateWrapperClear
				.eq(ProductTree::getPid, pid)
				.eq(ProductTree::getName, "虚拟件")
				.set(ProductTree::getPid, "").set(ProductTree::getDelFlag, 1);

		productTreeMapper.update(null, updateWrapperClear);

		AddProductPartVO vo = new AddProductPartVO();
		vo.setAssembleQuantity(BigDecimal.ONE);
		vo.setName("虚拟件");
		vo.setPartType("VIRTUAL");
		vo.setPid(pid);
		return processBomService.addProcessBomPart(vo);
	}

	private void bindProcessOutputs(List<ProcessInfoVO> processInfoList, String processRouteId, String productId, String productFormulationId) {
		log.info("工序与产出物绑定：{}", JSON.toJSONString(processInfoList));
		Collections.reverse(processInfoList);
		// 查询工艺路线
		List<MdProcessRouteDetail> mdProcessRouteDetails = mdProcessRouteDetailService.selectByMainId(processRouteId);

		List<ProductProcessRoute> list = new ArrayList<>();
		// BOM反转后匹配工艺路线,并组装各节点对应工序
		for (int i = 0; i < processInfoList.size(); i++) {
			ProductProcessRoute productProcessRoute = new ProductProcessRoute();
			ProcessInfoVO processInfoVO = processInfoList.get(i);
			// 获取各个节点的工序
			if (mdProcessRouteDetails.size() <= i || mdProcessRouteDetails.get(i) == null) {
				continue;
			}
			MdProcessRouteDetail mdProcessRouteDetail = mdProcessRouteDetails.get(i);

			MdProcess mdProcess = mdProcessMapper.selectById(mdProcessRouteDetail.getProcessId());
			productProcessRoute.setCurrProductId(productId);
			productProcessRoute.setProcessCode(mdProcess.getProcessCode());
			productProcessRoute.setProcessId(mdProcess.getId());
			productProcessRoute.setProcessContent(mdProcessRouteDetail.getProcessContent());
			productProcessRoute.setProcessNumber(mdProcessRouteDetail.getProcessNumber());
			productProcessRoute.setProcessRouteId(processRouteId);
			productProcessRoute.setProductId(processInfoVO.getOutputProductId());
			productProcessRoute.setMaterialCode(processInfoVO.getMaterialCode());
			productProcessRoute.setProcessRouteDetailId(mdProcessRouteDetail.getId());
			productProcessRoute.setWorkHour(processInfoVO.getWorkHour());
			productProcessRoute.setFormulationId(productFormulationId);
			list.add(productProcessRoute);

			// 删除原有绑定信息
			processMaterialBindingService.deleteByFormulationIdAndProcessId(productFormulationId, processInfoVO.getProcessId());
			processMaterialBindingService.saveBatch(processInfoVO.getBindingList());
		}
		// 查询当前最新的工艺路线
		List<ProductProcessRoute> productProcessRoutes = productProcessRouteMapper.selectList(new LambdaQueryWrapper<ProductProcessRoute>()
				.eq(ProductProcessRoute::getFormulationId, productFormulationId)
				.orderByDesc(ProductProcessRoute::getCreateTime)
				.orderByDesc(ProductProcessRoute::getProcessNumber)
				.last("limit "+mdProcessRouteDetails.size()));

		// productProcessRouteMapper.removeByCurProductId(productFormulationId);
		productProcessRouteService.saveBatch(list);

		if (!productProcessRoutes.isEmpty() && productProcessRoutes.size() == list.size()) {
			List<ProcessExperimentTerm> batchList = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				ProductProcessRoute productProcessRoute = productProcessRoutes.get(i);
				// 查询检验项
				List<ProcessExperimentTerm> processExperimentTerms = processExperimentTermService.listTermByRefKey(productProcessRoute.getId());
				for (ProcessExperimentTerm processExperimentTerm : processExperimentTerms) {
					ProcessExperimentTerm copy = new ProcessExperimentTerm();
					BeanUtils.copyProperties(processExperimentTerm, copy, "id", "createTime", "updateTime","updateBy","createBy");
					copy.setRefKey(list.get(i).getId());
					batchList.add(copy);
				}
			}

			if (!batchList.isEmpty()) {

				processExperimentTermService.saveBatch(batchList);
			}

		}

		// 更新配方信息中的historyBomId
		LambdaUpdateWrapper<ProductFormulation> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper
				.eq(ProductFormulation::getId, productFormulationId)
				.set(ProductFormulation::getHistoryBoomId, productId);

		productFormulationMapper.update(null, updateWrapper);

	}

	private void saveProductProcessDetail(List<ProcessInfoVO> processInfoList) {
		Collections.reverse(processInfoList);
		for (ProcessInfoVO processInfoVO : processInfoList) {
			List<RespProcessDetailVO> processDetail = processInfoVO.getProcessDetail();

			ArrayList<ProductProcessDetail> processDetails = new ArrayList<>();
			for (RespProcessDetailVO item : processDetail) {
				ProductProcessDetail vo = new ProductProcessDetail();
				BeanUtils.copyProperties(item, vo, "id","createTime","createBy","updateTime","upgradeBy");
				vo.setProductId(processInfoVO.getOutputProductId());
				vo.setProcessId(processInfoVO.getProcessId());
				processDetails.add(vo);
			}

			productProcessDetailMapper.removeByProcessId(processInfoVO.getProcessId(), processInfoVO.getOutputProductId());

			productProcessDetailService.saveBatch(processDetails);

		}

	}

	private void saveProductProcessDetailUpgrade(String productId, String processId, String productIdOrigin) {
		LambdaQueryWrapper<ProductProcessDetail> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(ProductProcessDetail::getProcessId, processId)
				.eq(ProductProcessDetail::getProductId, productIdOrigin)
				.eq(ProductProcessDetail::getIsNew, 1)
				.orderByAsc(ProductProcessDetail::getNumber);

		List<ProductProcessDetail> productProcessDetails = productProcessDetailMapper.selectList(queryWrapper);
		ArrayList<ProductProcessDetail> processDetails = new ArrayList<>();
		for (ProductProcessDetail item : productProcessDetails) {
			ProductProcessDetail productProcessDetail = new ProductProcessDetail();
			BeanUtils.copyProperties(item, productProcessDetail, "id","createTime","createBy","updateTime","upgradeBy");
			productProcessDetail.setProductId(productId);
			processDetails.add(productProcessDetail);
		}
		productProcessDetailMapper.removeByProcessId(processId, productId);

		productProcessDetailService.saveBatch(processDetails);
	}


	@Override
	@Transactional
	public ProductFormulation formulaUpgrade(String id) {
        //查询配方主表,子表,物料组成表
        ProductFormulation productFormulation = productFormulationMapper.selectById(id);
		//原boomId
		String productIdOrigin=productFormulation.getBoomId();
        ProductFormulationMaster productFormulationMaster = productFormulationMasterMapper.selectById(productFormulation.getProductFormulationMasterId());
        List<ProductFormulationDetail> productFormulationDetailList = productFormulationDetailMapper.selectList(new LambdaQueryWrapper<ProductFormulationDetail>().eq(ProductFormulationDetail::getProductFormulationId, id));
        productFormulationMaster.setId(null);
        productFormulationMasterMapper.insert(productFormulationMaster);
        //重新创建tree,part
        //生成tree，part表数据
        createTreePart(productFormulation, productFormulationDetailList);
        //生成配方数据
        productFormulation.setId(null);
		productFormulation.setFormulaCode("");
        productFormulation.setProductFormulationMasterId(productFormulationMaster.getId());
        productFormulation.setFormulaStatus("0");
        productFormulation.setEnableStatus(2);
        productFormulation.setExpirationDateStart(null);
        productFormulation.setExpirationDateEnd(null);
        productFormulation.setWiid(null);
        productFormulation.setFormulaVersion(incrementVersion(productFormulation.getFormulaVersion()));

        productFormulationMapper.insert(productFormulation);

        for (ProductFormulationDetail pdf : productFormulationDetailList) {
            pdf.setId(null);
            pdf.setProductFormulationId(productFormulation.getId());
            productFormulationDetailMapper.insert(pdf);
        }

		//配方绑定关系
		ImportProcessRequestUpgradeVO importProcessRequestUpgradeVO = new ImportProcessRequestUpgradeVO();
		importProcessRequestUpgradeVO.setProductId(productFormulation.getBoomId());
		importProcessRequestUpgradeVO.setProductFormulationIdOrigin(id);
		importProcessRequestUpgradeVO.setProductIdOrigin(productIdOrigin);
		processRouteImporterUpgrade(importProcessRequestUpgradeVO);

        return productFormulation;
    }



	public static String incrementVersion(String version) {
		if (version == null || !version.matches("V\\d+\\.\\d+")) {
			throw new IllegalArgumentException("版本格式不符合要求，需要是 Vx.x 格式");
		}

		// 提取主版本号
		int majorVersion = Integer.parseInt(version.substring(1, version.indexOf('.')));

		// 自增主版本号
		majorVersion++;

		// 构建新的版本号
		return "V" + majorVersion + ".0";
	}


	@Override
	public IPage<FormulationReplaceVo> queryByMaterialName(Page<FormulationReplaceVo> page,String materialName) {
		IPage<FormulationReplaceVo> resultList = productFormulationMapper.queryByMaterialName(page, materialName);
		if(!resultList.getRecords().isEmpty()){
			List<String> materialCodes = resultList.getRecords().stream().map(FormulationReplaceVo::getMaterialCode).toList();
			Map<String, String> stringStringMap = mdMaterialApi.queryMaterialDescription(Joiner.on(",").join(materialCodes));
			for(FormulationReplaceVo vo:resultList.getRecords()){
				ProductPart productPart = plmApiService.queryPart(vo.getBoomId(), vo.getMaterialCode());
				if(null!=productPart) {
					vo.setFungibleMaterialCode(productPart.getFungibleMaterialCodes());
					vo.setRemark(stringStringMap.get(vo.getMaterialCode()));
				}
			}
		}
		return resultList;
	}

	@Override
	public List<Map<String, String>> queryListByMaterialName(String materialName) {
		List<Map<String, String>> list = mdMaterialApi.formulamap(materialName);
		return list;
	}

	@Override
	public ProductFormulation formulaCopy(String id) {

			//查询配方主表,子表,物料组成表
			ProductFormulation productFormulation = productFormulationMapper.selectById(id);
			//原boomId
			String productIdOrigin=productFormulation.getBoomId();
			ProductFormulationMaster productFormulationMaster = productFormulationMasterMapper.selectById(productFormulation.getProductFormulationMasterId());
			List<ProductFormulationDetail> productFormulationDetailList = productFormulationDetailMapper.selectList(new LambdaQueryWrapper<ProductFormulationDetail>().eq(ProductFormulationDetail::getProductFormulationId, id));
			productFormulationMaster.setId(null);
			productFormulationMasterMapper.insert(productFormulationMaster);
			//重新创建tree,part
			//生成tree，part表数据
			createTreePart(productFormulation, productFormulationDetailList);
			//生成配方数据
			productFormulation.setId(null);
			productFormulation.setFormulaCode("");
			productFormulation.setProductFormulationMasterId(productFormulationMaster.getId());
			productFormulation.setFormulaStatus("0");
			productFormulation.setEnableStatus(2);
			productFormulation.setExpirationDateStart(null);
			productFormulation.setExpirationDateEnd(null);
			productFormulation.setWiid(null);

			productFormulationMapper.insert(productFormulation);

			for (ProductFormulationDetail pdf : productFormulationDetailList) {
				pdf.setId(null);
				pdf.setProductFormulationId(productFormulation.getId());
				productFormulationDetailMapper.insert(pdf);
			}

			//配方绑定关系
			ImportProcessRequestUpgradeVO importProcessRequestUpgradeVO = new ImportProcessRequestUpgradeVO();
			importProcessRequestUpgradeVO.setProductId(productFormulation.getBoomId());
			importProcessRequestUpgradeVO.setProductIdOrigin(productIdOrigin);
			processRouteImporterUpgrade(importProcessRequestUpgradeVO);


         return productFormulation;
	}

	@Override
	@Transactional
	public void replaceMainMaterial(ReplaceMainMaterialVO replaceMainMaterialVO) {
		List<ProductTree> updateList = new ArrayList<>();
		List<ProductPart> updateList1 = new ArrayList<>();
		List<ProductFormulationDetail> updateList2 = new ArrayList<>();
		//是，则将新替换的物料，变更为主料；原主料，变更为辅料。
		if(1==replaceMainMaterialVO.getReplaceFlag()){
			if(!replaceMainMaterialVO.getFormulationReplaceVo().isEmpty()) {
				for (FormulationReplaceVo vo:replaceMainMaterialVO.getFormulationReplaceVo()) {
					List<String> formulationIdList = new ArrayList<>();
					ProductFormulationDetail productFormulationDetail = productFormulationDetailMapper.selectById(vo.getId());
					formulationIdList.add(productFormulationDetail.getProductFormulationId());
					String oldMaterialCode = productFormulationDetail.getMaterialCode();
					//变更tree与part
					ProductTree productTree = productTreeMapper.selectById(vo.getBoomId());
					productTree.setMaterialCode(vo.getMaterialCode());
					updateList.add(productTree);
					ProductPart productPart = productPartMapper.selectById(vo.getBoomId());
					if(null!=productPart) {
						productPart.setMaterialCode(vo.getMaterialCode());
						if(null!=productPart.getFungibleMaterialCodes()) {
							productPart.setFungibleMaterialCodes(productPart.getFungibleMaterialCodes()+','+oldMaterialCode);
						}else{
							productPart.setFungibleMaterialCodes(productFormulationDetail.getMaterialCode());
						}
						updateList1.add(productPart);
					}
					productFormulationDetail.setMaterialCode(replaceMainMaterialVO.getNewMaterialCode());
					if(null!=productFormulationDetail.getFungibleMaterialCodes()) {
						productFormulationDetail.setFungibleMaterialCodes(productFormulationDetail.getFungibleMaterialCodes()+','+oldMaterialCode);
					}else{
						productFormulationDetail.setFungibleMaterialCodes(oldMaterialCode);
					}
					updateList2.add(productFormulationDetail);
					//变更工序中物料编码
					replaceProductProcessMaterial(oldMaterialCode,replaceMainMaterialVO.getNewMaterialCode(),formulationIdList);
				}
			}
			//更新detail表
			productFormulationDetailService.updateBatchById(updateList2);
			//更新tree，part表
			productTreeService.updateBatchById(updateList);
			productPartService.updateBatchById(updateList1);
		}else{
			//否，则只将新替换的物料，变更为主料；原主料，从配料信息中删除。
			if(!replaceMainMaterialVO.getFormulationReplaceVo().isEmpty()) {
				for (FormulationReplaceVo vo:replaceMainMaterialVO.getFormulationReplaceVo()) {
					List<String> formulationIdList = new ArrayList<>();
					ProductFormulationDetail productFormulationDetail = productFormulationDetailMapper.selectById(vo.getId());
					formulationIdList.add(productFormulationDetail.getProductFormulationId());
					String oldMaterialCode = productFormulationDetail.getMaterialCode();
					//变更tree与part
					ProductTree productTree = productTreeMapper.selectById(vo.getBoomId());
					productTree.setMaterialCode(vo.getMaterialCode());
					updateList.add(productTree);
					ProductPart productPart = productPartMapper.selectById(vo.getBoomId());
					if(null!=productPart) {
						productPart.setMaterialCode(vo.getMaterialCode());
						updateList1.add(productPart);
					}
					productFormulationDetail.setMaterialCode(replaceMainMaterialVO.getNewMaterialCode());
					updateList2.add(productFormulationDetail);
					//变更工序中物料编码
					replaceProductProcessMaterial(oldMaterialCode,replaceMainMaterialVO.getNewMaterialCode(),formulationIdList);
				}


				//更新detail表
				productFormulationDetailService.updateBatchById(updateList2);
				//更新tree，part表
				productTreeService.updateBatchById(updateList);
				productPartService.updateBatchById(updateList1);
			}
		}

	}

	@Override
	public void replaceAuxiliaryMaterials(ReplaceMainMaterialVO replaceMainMaterialVO) {
		List<ProductTree> updateList = new ArrayList<>();
		List<ProductPart> updateList1 = new ArrayList<>();
		List<ProductFormulationDetail> updateList2 = new ArrayList<>();
		if(!replaceMainMaterialVO.getFormulationReplaceVo().isEmpty()) {
			for (FormulationReplaceVo vo:replaceMainMaterialVO.getFormulationReplaceVo()) {
				List<String> formulationIdList = new ArrayList<>();
				ProductFormulationDetail productFormulationDetail = productFormulationDetailMapper.selectById(vo.getId());
				formulationIdList.add(productFormulationDetail.getProductFormulationId());
				String oldMaterialCode = productFormulationDetail.getMaterialCode();
				//变更tree与part
				ProductPart productPart = productPartMapper.selectById(vo.getBoomId());
				if(null!=productPart) {
					if(null!=productPart.getFungibleMaterialCodes()) {
						productPart.setFungibleMaterialCodes(productPart.getFungibleMaterialCodes()+','+replaceMainMaterialVO.getNewMaterialCode());
					}else{
						productPart.setFungibleMaterialCodes(replaceMainMaterialVO.getNewMaterialCode());
					}
					updateList1.add(productPart);
				}
				if(null!=productFormulationDetail.getFungibleMaterialCodes()) {
					productFormulationDetail.setFungibleMaterialCodes(productFormulationDetail.getFungibleMaterialCodes()+','+oldMaterialCode);
				}else{
					productFormulationDetail.setFungibleMaterialCodes(replaceMainMaterialVO.getNewMaterialCode());
				}
				updateList2.add(productFormulationDetail);
			}
		}
		//更新detail表
		productFormulationDetailService.updateBatchById(updateList2);
		//更新tree，part表
		productPartService.updateBatchById(updateList1);
	}

	@Override
	@Transactional
	public void InitializeData() {
		List<ProcessMaterialBinding> addList = new ArrayList<>();
		List<ProductProcessRoute> updateList = new ArrayList<>();
		//1-1 查询所有配方信息
		List<ProductTree> treeList = productTreeMapper.selectList(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getType, 3)
				.eq(ProductTree::getNodeType, 2).eq(ProductTree::getEnable,0)
				/*.eq(ProductTree::getId,"1859437861391216642")*/);
		List<String> materialCodes = treeList.stream().map(ProductTree::getMaterialCode).collect(Collectors.toList());
		//查询出全量配方boom
		Map<String, List<ProductTree>> stringListMap = queryChildren(treeList);
		//查询用量分子
		Map<String, ProductPart> idToEntityMap = getIdToEntityMap();

		List<ProductProcessRoute> productProcessRoutes = productProcessRouteMapper.selectList(new LambdaQueryWrapper<ProductProcessRoute>().eq(ProductProcessRoute::getIsNew, 1));



		//
		//1-2 生成配方管理数据库表 product_formulation_master product_formulation product_formulation_detail
		for(ProductTree pt:treeList){
			Map<String, MdMaterial> mdMaterialMap = mdMaterialApi.queryMdMaterialAll(pt.getMaterialCode());
			//主表
			ProductFormulationMaster productFormulationMaster = new ProductFormulationMaster();
			productFormulationMaster.setDelFlag(0);
			productFormulationMasterMapper.insert(productFormulationMaster);
			ProductFormulation productFormulation = new ProductFormulation();
			productFormulation.setProductFormulationMasterId(productFormulationMaster.getId());
			productFormulation.setMaterialName(pt.getName());
			productFormulation.setMaterialCode(pt.getMaterialCode());
			//todo 物料分类
			productFormulation.setFormulaType("1");
			productFormulation.setFormulaCode(pt.getCode());
			productFormulation.setFormulaStatus(FormulaStatusEnum.AUDITED.getCode());
			productFormulation.setEnableStatus(0);
			//料件类型
			String materialCode = pt.getMaterialCode();
			MdMaterial mdMaterial1 = mdMaterialMap.get(materialCode);
			if(null!=mdMaterial1){
				productFormulation.setMaterialTypeId(mdMaterial1.getMaterialTypeId());
			}
			// 2. 使用Optional安全处理可能的null值，避免空指针
			String itemType = Optional.ofNullable(materialCode)
					// 检查map中是否存在该物料编码的映射
					.map(code -> mdMaterialMap.get(code))
					// 若存在映射，获取itemType；否则返回null（或默认值）
					.map(mdMaterial -> mdMaterial.getItemType())
					// 可选：当获取失败时设置默认值，或抛出明确异常
					.orElse(null); // 例如：.orElse("DEFAULT_TYPE") 或 .orElseThrow(() -> new IllegalArgumentException("物料编码不存在: " + materialCode))

			productFormulation.setItemType(itemType);
			productFormulation.setBoomId(pt.getId());
			productFormulation.setHistoryBoomId(pt.getId());
			productFormulation.setFormulaVersion("V1.0");
			List<ProductProcessRoute> filteredList = productProcessRoutes.stream()
					.filter(route -> Objects.equals(route.getProductId(), pt.getId())) // 假设 productId 是 Long 类型
					.collect(Collectors.toList());
			if(!filteredList.isEmpty()){
				String processRouteId = filteredList.get(0).getProcessRouteId();
				productFormulation.setProcessRouteId(processRouteId);
			}
			productFormulation.setTenantId(Integer.valueOf(TenantContext.getTenant()));
			productFormulationMapper.insert(productFormulation);
			//更新ProductProcessRoute 表
			if(!filteredList.isEmpty()) {
				ProductProcessRoute productProcessRoute = filteredList.get(0);
				productProcessRoute.setFormulationId(productFormulation.getId());
				updateList.add(productProcessRoute);
			}
			productFormulationMaster.setProductFormulationId(productFormulation.getId());
			productFormulationMaster.setBeforeFormulationId(productFormulation.getId());
			productFormulationMasterMapper.updateById(productFormulationMaster);

			//保存product_formulation_detail
			List<ProductTree> treeChildrenList = stringListMap.get(pt.getId());
			if(!treeChildrenList.isEmpty()){
				int i=1;
				for(ProductTree pt1:treeChildrenList){
					ProductFormulationDetail productFormulationDetail = new ProductFormulationDetail();
					ProductPart productPart = plmApiService.queryPart(pt1.getId(), pt1.getMaterialCode());
					productFormulationDetail.setProductFormulationId(productFormulation.getId());
					productFormulationDetail.setItemNumber(i);
					productFormulationDetail.setBoomId(pt1.getId());
					productFormulationDetail.setMaterialCode(pt1.getMaterialCode());
					productFormulationDetail.setMaterialName(pt1.getName());
					productFormulationDetail.setDenominator(BigDecimal.valueOf(100));
					productFormulationDetail.setDelFlag(0);
					//用量分子
					ProductPart pp = idToEntityMap.get(pt1.getId());
					if(null!=pp) {
						productFormulationDetail.setMolecule(pp.getAssembleQuantity());
						if(null!=pp.getFungibleMaterialCodes()&& !pp.getFungibleMaterialCodes().isEmpty()){
							Map<String, MdMaterial> fMaterialMap = mdMaterialApi.queryMdMaterialAll(pp.getFungibleMaterialCodes());
							String collect = fMaterialMap.values().stream()
									// 提取materialName（注意：需确保MdMaterial有getMaterialName()方法）
									.map(MdMaterial::getMaterialName)
									// 过滤掉null和空字符串（根据业务需求，可保留空字符串则去掉此步）
									.filter(name -> name != null && !name.trim().isEmpty())
									// 用逗号拼接所有有效名称
									.collect(Collectors.joining(","));
							productFormulationDetail.setFungibleMaterialName(collect);
						}
					}
					productFormulationDetailMapper.insert(productFormulationDetail);

					//生成process_material_binding
					ProcessMaterialBinding processMaterialBinding = new ProcessMaterialBinding();
					processMaterialBinding.setMaterialCode(productFormulationDetail.getMaterialCode());
					processMaterialBinding.setProductId(productFormulationDetail.getBoomId());
					processMaterialBinding.setFormulationId(productFormulationDetail.getProductFormulationId());
					processMaterialBinding.setFormulationDetailId(productFormulationDetail.getId());
					if(!filteredList.isEmpty()){
						String processId = filteredList.get(0).getProcessId();
						processMaterialBinding.setProcessId(processId);
					}
					addList.add(processMaterialBinding);

					i++;
				}
			}
		}
		productProcessRouteService.updateBatchById(updateList);
		processMaterialBindingService.saveBatch(addList);


	}

	@Override
	public Map<String, String> queryFormulationDictByMaterialCode(String materialCode) {
		Map<String, String> resultMap = new LinkedHashMap<>();

		// 查询product_formulation表，条件：materialCode、formulaType=3、enableStatus=1
		LambdaQueryWrapper<ProductFormulation> formulationWrapper = new LambdaQueryWrapper<>();
		formulationWrapper.eq(ProductFormulation::getMaterialCode, materialCode)
				.eq(ProductFormulation::getFormulaType, "3")
				.eq(ProductFormulation::getEnableStatus, 0)
				.eq(ProductFormulation::getDelFlag, 0);

		List<ProductFormulation> formulations = productFormulationMapper.selectList(formulationWrapper);

		if (formulations.isEmpty()) {
			return resultMap;
		}

		// 获取第一个配方的信息，添加第一行数据：key=material_code，value=material_name
		ProductFormulation firstFormulation = formulations.get(0);
		if (StringUtils.isNotBlank(firstFormulation.getMaterialCode()) && StringUtils.isNotBlank(firstFormulation.getMaterialName())) {
			resultMap.put(firstFormulation.getMaterialCode(), firstFormulation.getMaterialName());
		}

		// 查询product_formulation_detail表
		for (ProductFormulation formulation : formulations) {
			LambdaQueryWrapper<ProductFormulationDetail> detailWrapper = new LambdaQueryWrapper<>();
			detailWrapper.eq(ProductFormulationDetail::getProductFormulationId, formulation.getId());

			List<ProductFormulationDetail> details = productFormulationDetailMapper.selectList(detailWrapper);

			for (ProductFormulationDetail detail : details) {
				if (StringUtils.isNotBlank(detail.getBoomId())) {
					// 通过boom_id关联product_part表
					ProductPart productPart = plmApiService.queryPart(detail.getBoomId(), detail.getMaterialCode());

					if (productPart != null) {
						// 处理fungible_material_codes（逗号分隔的值）
						String fungibleMaterialCodes = productPart.getFungibleMaterialCodes();
						String fungibleMaterialName = detail.getFungibleMaterialName();

						if (StringUtils.isNotBlank(fungibleMaterialCodes) && StringUtils.isNotBlank(fungibleMaterialName)) {
							// 分割逗号分隔的值
							String[] codes = fungibleMaterialCodes.split(",");
							String[] names = fungibleMaterialName.split(",");

							// 确保codes和names数组长度一致
							int minLength = Math.min(codes.length, names.length);
							for (int i = 0; i < minLength; i++) {
								String code = codes[i].trim();
								String name = names[i].trim();
								if (StringUtils.isNotBlank(code) && StringUtils.isNotBlank(name)) {
									resultMap.put(code, name);
								}
							}
						}
					}
				}
			}
		}

		return resultMap;
	}

	public Map<String, ProductPart> getIdToEntityMap() {
		// 1. 查询所有记录（可根据需要添加查询条件）
		LambdaQueryWrapper<ProductPart> queryWrapper = new LambdaQueryWrapper<>();
		// 可选：添加过滤条件，如 queryWrapper.eq(ProductPart::getStatus, 1);

		List<ProductPart> partList = productPartMapper.selectList(queryWrapper);

		// 2. 转换为Map<String, ProductPart>，key为id（转为String），value为实体
		return partList.stream()
				.filter(part -> part.getId() != null) // 过滤id为null的记录（避免key为null）
				.collect(Collectors.toMap(
						// key：将id转换为String（若实体id是Long类型则用String.valueOf(part.getId())）
						part -> part.getId().toString(),
						// value：实体对象本身
						part -> part,
						// 处理id重复的情况（若id是主键可忽略，此处保留避免异常）
						(existing, replacement) -> existing,
						// 初始化容量为列表大小，减少扩容开销
						() -> new HashMap<>(partList.size())
				));
	}


	private Map<String,List<ProductTree>> queryChildren(List<ProductTree> treeList) {
		// 1. 收集所有父节点ID（过滤null值，避免SQL语法问题）
		List<String> parentIds = treeList.stream()
				.map(ProductTree::getId)
				.filter(Objects::nonNull)
				.collect(Collectors.toList());

		// 2. 一次性查询所有符合条件的子节点（仅1次SQL查询）
		Map<String, List<ProductTree>> childrenMap = new HashMap<>();
		if (!parentIds.isEmpty()) {
			List<ProductTree> allChildren = productTreeMapper.selectList(
					new LambdaQueryWrapper<ProductTree>()
							.eq(ProductTree::getType, 4)
							.eq(ProductTree::getNodeType, 2)
							.in(ProductTree::getPid, parentIds) // 批量匹配父ID
			);
			// 3. 按父ID（pid）分组，内存中完成映射
			childrenMap = allChildren.stream()
					.collect(Collectors.groupingBy(ProductTree::getPid));
		}

		// 4. 确保每个父节点在返回结果中都有对应 entry（即使无子节点也返回空列表）
		Map<String, List<ProductTree>> returnMap = new HashMap<>(treeList.size());
		for (ProductTree tree : treeList) {
			String parentId = tree.getId();
			// 若无子节点，用空列表兜底，保持与原逻辑一致
			returnMap.put(parentId, childrenMap.getOrDefault(parentId, Collections.emptyList()));
		}
		return  returnMap;
	}
}
