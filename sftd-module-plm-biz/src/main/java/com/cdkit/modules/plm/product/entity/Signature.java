package com.cdkit.modules.plm.product.entity;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 人员签名
 */
@Schema(description = "人员签名")
@Data
@TableName(value = "signature")
public class Signature implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    @Size(max = 36, message = "id最大长度要小于 36")
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 用户昵称
     */
    @TableField(value = "username")
    @Schema(description = "用户昵称")
    @Size(max = 36, message = "用户昵称最大长度要小于 36")
    @NotBlank(message = "用户昵称不能为空")
    private String username;

    /**
     * 文件名
     */
    @TableField(value = "file_name")
    @Schema(description = "文件名")
    @Size(max = 100, message = "文件名最大长度要小于 100")
    private String fileName;

    /**
     * 文件大小
     */
    @TableField(value = "file_size")
    @Schema(description = "文件大小")
    private Integer fileSize;

    /**
     * 文件路径
     */
    @TableField(value = "file_path")
    @Schema(description = "文件路径")
    @Size(max = 255, message = "文件路径最大长度要小于 255")
    private String filePath;

    /**
     * 是否删除
     */
    @TableField(value = "del_flag")
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @Schema(description = "创建人")
    @Size(max = 50, message = "创建人最大长度要小于 50")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    @Schema(description = "更新人")
    @Size(max = 50, message = "更新人最大长度要小于 50")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 所属部门编码
     */
    @TableField(value = "sys_org_code")
    @Schema(description = "所属部门编码")
    @Size(max = 64, message = "所属部门编码最大长度要小于 64")
    private String sysOrgCode;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    @Schema(description = "租户号")
    @Size(max = 32, message = "租户号最大长度要小于 32")
    private String tenantId;

    /**
     * 签名dwg的docId
     */
    @TableField(value = "doc_id")
    @Schema(description = "签名dwg的docId")
    @Size(max = 100, message = "签名dwg的docId最大长度要小于 100")
    private String docId;

    private static final long serialVersionUID = 1L;
}
