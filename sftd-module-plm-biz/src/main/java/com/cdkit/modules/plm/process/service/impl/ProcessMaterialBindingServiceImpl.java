package com.cdkit.modules.plm.process.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.modules.plm.process.entity.ProcessMaterialBinding;
import com.cdkit.modules.plm.process.mapper.ProcessMaterialBindingMapper;
import com.cdkit.modules.plm.process.service.IProcessMaterialBindingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 工序与投入物绑定
 * @Author: cdkit-boot
 * @Date:   2025-08-11
 * @Version: V1.0
 */
@Slf4j
@Service
public class ProcessMaterialBindingServiceImpl extends ServiceImpl<ProcessMaterialBindingMapper, ProcessMaterialBinding> implements IProcessMaterialBindingService {
    @Resource
    private ProcessMaterialBindingMapper processMaterialBindingMapper;

    /**
     * 删除工序绑定的物料
     * @param productFormulationId 配方ID
     * @param processId 工序ID
     */
    @Override
    public void deleteByFormulationIdAndProcessId(String productFormulationId, String processId) {
        log.info("删除工序绑定的物料:配方ID={},工序ID={}", productFormulationId, processId);
        processMaterialBindingMapper.deleteByFormulationIdAndProcessId(productFormulationId, processId);
    }

    /**
     * 查询工序绑定的物料
     * @param formulationId 配方ID
     * @param processId 工序ID
     * @return 物料
     */
    @Override
    public List<ProcessMaterialBinding> selectByFormulationIdAndProcessId(String formulationId, String processId) {
        return processMaterialBindingMapper.selectList(new LambdaQueryWrapper<ProcessMaterialBinding>()
                .eq(ProcessMaterialBinding::getFormulationId, formulationId)
                .eq(ProcessMaterialBinding::getProcessId, processId));
    }
}
