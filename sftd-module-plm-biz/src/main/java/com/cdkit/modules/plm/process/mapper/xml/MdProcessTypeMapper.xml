<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.plm.process.mapper.MdProcessTypeMapper">

	<update id="updateTreeNodeStatus" parameterType="java.lang.String">
		update md_process_type set has_child = #{status} where id = #{id}
	</update>

  	<!-- 【vue3专用】 -->
	<select id="queryListByPid" parameterType="java.lang.Object" resultType="com.cdkit.common.system.vo.SelectTreeModel">
		select
		  id as "key",
		  type_name as "title",
		  (case when has_child = '1' then 0 else 1 end) as isLeaf,
		  pid as parentId
		from md_process_type
		where pid = #{pid}
		<if test="query != null">
			<foreach collection="query.entrySet()" item="value" index="key">
				and ${key} = #{value}
			</foreach>
		</if>
	</select>

</mapper>
