package com.cdkit.modules.plm.product.entity;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 文档树
 */
@Schema(description="文档树")
@Data
@TableName(value = "base_file_tree")
public class BaseFileTree implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="主键ID")
    @Size(max = 36,message = "主键ID最大长度要小于 36")
    @NotBlank(message = "主键ID不能为空")
    private String id;

    /**
     * 父级目录ID
     */
    @TableField(value = "pid")
    @Schema(description="父级目录ID")
    @Size(max = 36,message = "父级目录ID最大长度要小于 36")
    @NotBlank(message = "父级目录ID不能为空")
    private String pid;

    /**
     * 目录或文档名称
     */
    @TableField(value = "file_name")
    @Schema(description="目录或文档名称")
    @Size(max = 255,message = "目录或文档名称最大长度要小于 255")
    @NotBlank(message = "目录或文档名称不能为空")
    private String fileName;

    /**
     * 类型 1目录 2文件
     */
    @TableField(value = "file_type")
    @Schema(description="类型 1目录 2文件")
    @NotNull(message = "类型 1目录 2文件不能为null")
    private Integer fileType;

    /**
     * 文件路径
     */
    @TableField(value = "file_path")
    @Schema(description="文件路径")
    @Size(max = 255,message = "文件路径最大长度要小于 255")
    @NotBlank(message = "文件路径不能为空")
    private String filePath;

    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "del_flag")
    @Schema(description="是否删除")
    private Integer delFlag;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    @Schema(description="租户号")
    @Size(max = 32,message = "租户号最大长度要小于 32")
    private String tenantId;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    @Schema(description="创建人")
    @Size(max = 32,message = "创建人最大长度要小于 32")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @Schema(description="创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by")
    @Schema(description="更新人")
    @Size(max = 32,message = "更新人最大长度要小于 32")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @Schema(description="更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
