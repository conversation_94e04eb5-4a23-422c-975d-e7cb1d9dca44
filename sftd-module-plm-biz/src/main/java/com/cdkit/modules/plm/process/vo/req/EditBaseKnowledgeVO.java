package com.cdkit.modules.plm.process.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：AddBaseKnowledgeVO
 * @Date：2024/4/16 13:47
 */
@Data
@Schema(description = "新增工艺知识库vo")
public class EditBaseKnowledgeVO {

    @Schema(description = "主键id")
    @NotBlank(message = "id不能为空")
    private String id;

    @Schema(description = "父级ID")
    @Size(max = 36, message = "父级ID最大长度要小于 36")
    private String pid;

    @Schema(description = "知识名称")
    @Size(max = 255, message = "知识名称最大长度要小于 255")
    private String name;

    @Schema(description = "知识编码")
    @Size(max = 255, message = "知识编码最大长度要小于 255")
    private String code;

    @Schema(description = "描述")
    @Size(max = 800, message = "描述最大长度要小于 800")
    private String description;

}
