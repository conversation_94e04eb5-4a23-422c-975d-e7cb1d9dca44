package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author：mr
 * @name：ProductUploadVO
 * @Date：2024/4/16 18:00
 */
@Data
@Schema(description ="产品通用说明书")
public class ProductUploadVO {
    //配方id
    private String id;
    @Schema(description ="物资编码申请表")
    private String materialCodeFileList;
    @Schema(description ="产品SDS")
    private String  productSdsList;
    @Schema(description ="安全标签")
    private String securityLabelList;

    //是否更新 1-新增 2-更新
    private String updateFlag;
}
