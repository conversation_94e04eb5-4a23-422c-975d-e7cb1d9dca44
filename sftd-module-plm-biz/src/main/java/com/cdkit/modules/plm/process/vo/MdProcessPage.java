package com.cdkit.modules.plm.process.vo;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import com.cdkit.modules.plm.process.entity.MdProcessDetail;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.cdkitframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;


/**
 * @Description: 工序定义
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Data
@Schema(description="md_processPage对象", name="工序定义")
public class MdProcessPage {

	/**id*/
	@Schema(description = "id")
    private String id;
	/**工序编码*/
	@Excel(name = "工序编码", width = 15)
	@Schema(description = "工序编码")
    private String processCode;
	/**工序名称*/
	@Excel(name = "工序名称", width = 15)
	@Schema(description = "工序名称")
    private String processName;
	/**产出单位id*/
	@Excel(name = "产出单位id", width = 15)
	@Schema(description = "产出单位id")
    private String outputUnitId;
	/**产出副单位id*/
	@Excel(name = "产出副单位id", width = 15)
	@Schema(description = "产出副单位id")
	private String outputSecUnitId;
	/**工序分类id*/
	@Excel(name = "工序分类id", width = 15)
	@Schema(description = "工序分类id")
    private String processTypeId;
	/**工艺类型*/
	@Excel(name = "工艺类型", width = 15)
	@Schema(description = "工艺类型")
    private String processKind;
	/**可委外*/
	@Excel(name = "可委外", width = 15)
	@Schema(description = "可委外")
    private Integer outsourced;
	/**是否排产*/
	@Excel(name = "是否排产", width = 15)
	@Schema(description = "是否排产")
    private Integer scheduling;
	/**是否追溯*/
	@Excel(name = "是否追溯", width = 15)
	@Schema(description = "是否追溯")
    private Integer traceable;
	/**启用状态*/
	@Excel(name = "启用状态", width = 15)
	@Schema(description = "启用状态")
    private String useStatus;
	/**是否是关键工序*/
	@Excel(name = "是否是关键工序", width = 15)
	@Schema(description = "是否是关键工序")
	private Integer isKey;
	/**序号*/
	@Excel(name = "序号", width = 15)
	@Schema(description = "序号")
    private String number;
	/**步骤名称*/
	@Excel(name = "步骤名称", width = 15)
	@Schema(description = "步骤名称")
    private String stepName;
	/**备注*/
	@Excel(name = "备注", width = 15)
	@Schema(description = "备注")
    private String remark;
	/**工时*/
	@Excel(name = "工时", width = 15)
	@Schema(description = "工时")
	private BigDecimal workHour;
	/**创建人*/
	@Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
	@Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
	@Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
	@Schema(description = "是否删除")
    private Integer delFlag;

	@ExcelCollection(name="工序定义明细")
	@Schema(description = "工序定义明细")
	private List<MdProcessDetail> mdProcessDetailList;

}
