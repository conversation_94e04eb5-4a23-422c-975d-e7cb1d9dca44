package com.cdkit.modules.plm.product.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.common.LoginUtil;
import com.cdkit.modules.plm.product.entity.Signature;
import com.cdkit.modules.plm.product.service.CadHandleService;
import com.cdkit.modules.plm.product.service.SignatureService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * @Author：zhao yang
 * @name：SignatureController
 * @Date：2024/4/12 14:04
 */
@RestController
@Tag(name = "签名管理")
@RequestMapping("signature")
@Slf4j
@AllArgsConstructor
public class SignatureController extends CdkitController<Signature, SignatureService> {
    private final SignatureService signatureService;

    @Operation(summary = "签名上传", description = "上传人的签名")
    @PostMapping(value = "upload")
    public Result<String> upload(@RequestBody JSONObject jsonObject) throws Exception {
        signatureService.upload(jsonObject.getStr("username"), jsonObject.getStr("filePath"));
        return Result.OK("上传成功");
    }

    @Operation(summary = "签名编辑", description = "签名编辑")
    @PostMapping(value = "edit")
    public Result<String> edit(@RequestBody JSONObject jsonObject) throws Exception {
        signatureService.edit(jsonObject.getStr("id"), jsonObject.getStr("filePath"));
        return Result.OK("上传成功");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id删除签名", description = "通过id删除签名")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        signatureService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询签名
     *
     * @param id
     * @return
     */
    @Operation(summary = "通过id查询签名", description = "通过id查询签名")
    @GetMapping(value = "queryById")
    public Result<Signature> queryById(@RequestParam(name = "id") String id) {
        Signature signature = signatureService.getById(id);
        if (signature == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(signature);
    }

    /**
     * 分页列表查询
     *
     * @param queryCondition
     * @return
     */
    @Operation(summary = "分页列表查询", description = "分页列表查询，userIds多个用逗号分隔")
    @GetMapping(value = "/list")
    public Result<IPage<Signature>> queryPageList(@RequestParam(name = "queryCondition", required = false) String queryCondition,
                                                  @RequestParam(name = "usernames", required = false) String usernames,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        List<String> usernameList = null;
        if (StrUtil.isNotBlank(usernames)) {
            usernameList = Arrays.asList(usernames.split(","));
        }
        LambdaQueryWrapper<Signature> wrapper = new LambdaQueryWrapper<Signature>()
                .eq(Signature::getCreateBy, LoginUtil.getCurrentUser().getUsername())
                .in(CollUtil.isNotEmpty(usernameList), Signature::getUsername, usernameList)
                .like(StrUtil.isNotBlank(queryCondition), Signature::getFileName, queryCondition)
                .orderByDesc(Signature::getCreateTime);
        Page<Signature> page = new Page<>(pageNo, pageSize);
        IPage<Signature> pageList = signatureService.page(page, wrapper);
        return Result.OK(pageList);
    }

    @Resource
    CadHandleService cadHandleService;

    @GetMapping("view")
    @Operation(summary = "签章图预览")
    public void view(HttpServletResponse response, @RequestParam String id) throws IOException {
        MultipartFile multipartFile = null;
        try {
            multipartFile = cadHandleService.dwg2Image(id);
        } catch (Exception e) {
            multipartFile = cadHandleService.dwg2Image(id);
        }
        InputStream inputStream = multipartFile.getInputStream();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType(MediaType.IMAGE_PNG_VALUE);
            IOUtils.copy(inputStream, outputStream);
        }
    }

}
