package com.cdkit.modules.plm.process.vo.req;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Schema(description = "工艺类型新增")
public class ReqProcessTypeEditVO {
    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @NotNull(message = "主键ID不能为空")
    @Schema(description = "主键ID", required = true)
    private String id;

    /**工艺类型名称*/
    @NotBlank(message = "工艺类型名称不能为空")
    @Size(max = 64, message = "工艺类型名称长度不能超过64个字符")
    @Schema(description = "工艺类型名称", required = true)
    private String name;
}
