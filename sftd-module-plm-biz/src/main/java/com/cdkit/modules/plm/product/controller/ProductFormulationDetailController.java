package com.cdkit.modules.plm.product.controller;

import java.util.Arrays;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.plm.product.entity.ProductFormulationDetail;
import com.cdkit.modules.plm.product.service.IProductFormulationDetailService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.query.QueryGenerator;

import lombok.extern.slf4j.Slf4j;

import com.cdkit.common.system.base.controller.CdkitController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.cdkit.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 zhaoyang
 * @Description: product_formulation_detail
 * @Author: cdkit-boot
 * @Date:   2025-06-03
 * @Version: V1.0
 */
@Tag(name="product_formulation_detail")
@RestController
@RequestMapping("/product/productFormulationDetail")
@Slf4j
public class ProductFormulationDetailController extends CdkitController<ProductFormulationDetail, IProductFormulationDetailService> {
	@Autowired
	private IProductFormulationDetailService productFormulationDetailService;
	
	/**
	 * 分页列表查询
	 *
	 * @param productFormulationDetail
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "product_formulation_detail-分页列表查询")
	@Operation(summary="product_formulation_detail-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ProductFormulationDetail>> queryPageList(ProductFormulationDetail productFormulationDetail,
																 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
																 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
																 HttpServletRequest req) {
		QueryWrapper<ProductFormulationDetail> queryWrapper = QueryGenerator.initQueryWrapper(productFormulationDetail, req.getParameterMap());
		Page<ProductFormulationDetail> page = new Page<ProductFormulationDetail>(pageNo, pageSize);
		IPage<ProductFormulationDetail> pageList = productFormulationDetailService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param productFormulationDetail
	 * @return
	 */
	@AutoLog(value = "product_formulation_detail-添加")
	@Operation(summary="product_formulation_detail-添加")
	@RequiresPermissions("product:product_formulation_detail:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ProductFormulationDetail productFormulationDetail) {
		productFormulationDetailService.save(productFormulationDetail);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param productFormulationDetail
	 * @return
	 */
	@AutoLog(value = "product_formulation_detail-编辑")
	@Operation(summary="product_formulation_detail-编辑")
	@RequiresPermissions("product:product_formulation_detail:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ProductFormulationDetail productFormulationDetail) {
		productFormulationDetailService.updateById(productFormulationDetail);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "product_formulation_detail-通过id删除")
	@Operation(summary="product_formulation_detail-通过id删除")
	@RequiresPermissions("product:product_formulation_detail:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		productFormulationDetailService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "product_formulation_detail-批量删除")
	@Operation(summary="product_formulation_detail-批量删除")
	@RequiresPermissions("product:product_formulation_detail:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.productFormulationDetailService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "product_formulation_detail-通过id查询")
	@Operation(summary="product_formulation_detail-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ProductFormulationDetail> queryById(@RequestParam(name="id",required=true) String id) {
		ProductFormulationDetail productFormulationDetail = productFormulationDetailService.getById(id);
		if(productFormulationDetail==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(productFormulationDetail);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param productFormulationDetail
    */
    @RequiresPermissions("product:product_formulation_detail:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ProductFormulationDetail productFormulationDetail) {
        return super.exportXls(request, productFormulationDetail, ProductFormulationDetail.class, "product_formulation_detail");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("product:product_formulation_detail:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ProductFormulationDetail.class);
    }

}
