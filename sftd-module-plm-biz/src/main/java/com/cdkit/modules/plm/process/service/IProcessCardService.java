package com.cdkit.modules.plm.process.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.process.entity.ProcessCard;
import com.cdkit.modules.plm.process.vo.req.ReqProcessCardAddVO;
import com.cdkit.modules.plm.process.vo.resp.RespProcessCardVO;

import java.util.List;

/**
 * @Description: 工艺卡片相关接口
 * @Author: mc
 * @Date:   2024-04-01
 * @Version: V1.0
 */
public interface IProcessCardService extends IService<ProcessCard> {
    /**
     * 保存工艺卡片
     * @param reqProcessCardAddVO 请求参数
     */
    void saveCard(List<ReqProcessCardAddVO> reqProcessCardAddVO);

    /**
     * 根据零件ID查询工艺卡片
     * @param partId 零件ID
     * @return 工艺卡片信息
     */
    List<RespProcessCardVO> listCardByPartId(String partId);

    /**
     * 通过卡片ID查询工艺卡片
     *
     * @param id 主键ID
     * @return 返回结果
     */
    RespProcessCardVO queryById(String id);
}
