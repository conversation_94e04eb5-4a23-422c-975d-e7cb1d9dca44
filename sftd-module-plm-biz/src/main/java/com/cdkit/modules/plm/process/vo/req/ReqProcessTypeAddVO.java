package com.cdkit.modules.plm.process.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Schema(description = "工艺类型新增")
public class ReqProcessTypeAddVO {
    /**工艺类型名称*/
    @NotBlank(message = "工艺类型名称不能为空")
    @Size(max = 64, message = "工艺类型名称长度不能超过64个字符")
    @Schema(description = "工艺类型名称", required = true)
    private String name;
}
