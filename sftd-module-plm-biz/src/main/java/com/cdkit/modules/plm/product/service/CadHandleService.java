package com.cdkit.modules.plm.product.service;

import jakarta.servlet.http.HttpServletResponse;
import com.cdkit.modules.plm.product.dto.WriteSignDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：CadHandleService
 * @Date：2024/4/25 15:32
 */
public interface CadHandleService {

    Map<String, Object> parseCadProperties(MultipartFile file, List<String> strTags);
    /**
     * 解析图纸属性
     *
     * @param id      designDocument表的id
     * @param strTags json里的tag，例如设计_机打信息，设计_物料编码，设计_零件名称，设计_零件编码
     * @return
     */
    Map<String, Object> parseCadProperties(String id, List<String> strTags);

    /**
     * 写入签章图
     *
     * @param id               designDocument表的id
     * @param writeSignDTOList
     * @return
     */
    void writeCadSign(String id, List<WriteSignDTO> writeSignDTOList);

    /**
     * 图纸转化
     *
     * @param id designDocument表的id
     * @return
     */
    MultipartFile transPdf(String id);

    /**
     * 获取签章后的文件
     *
     * @param id
     * @return
     */
    MultipartFile getDwgFile(String id);


    MultipartFile dwg2Image(String signatureId);

    void documentView(String id, HttpServletResponse response);
}
