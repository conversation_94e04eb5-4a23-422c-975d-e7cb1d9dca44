package com.cdkit.modules.plm.process.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.cdkit.modules.plm.process.vo.resp.MdProcessRemarkPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.cdkit.common.api.vo.Result;
import com.cdkit.common.aspect.annotation.AutoLog;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.system.query.QueryGenerator;
import com.cdkit.common.system.vo.LoginUser;
import com.cdkit.common.util.oConvertUtils;
import com.cdkit.modules.plm.process.entity.MdProcess;
import com.cdkit.modules.plm.process.entity.MdProcessDetail;
import com.cdkit.modules.plm.process.service.IMdProcessDetailService;
import com.cdkit.modules.plm.process.service.IMdProcessService;
import com.cdkit.modules.plm.process.vo.MdProcessPage;
import com.cdkit.modules.plm.process.vo.resp.MdBoomPage;
import com.cdkitframework.poi.excel.ExcelImportUtil;
import com.cdkitframework.poi.excel.def.NormalExcelConstants;
import com.cdkitframework.poi.excel.entity.ExportParams;
import com.cdkitframework.poi.excel.entity.ImportParams;
import com.cdkitframework.poi.excel.view.CdkitEntityExcelView;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;


/**
 * @Description: 工序定义
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Tag(name="工序定义")
@RestController
@RequestMapping("/mdProcess")
@Slf4j
public class MdProcessController {
	@Autowired
	private IMdProcessService mdProcessService;
	@Autowired
	private IMdProcessDetailService mdProcessDetailService;

	/**
	 * 分页列表查询
	 *
	 * @param mdProcess
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "工序定义-分页列表查询")
	@Operation(summary="工序定义-分页列表查询", description="工序定义-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<MdProcess>> queryPageList(MdProcess mdProcess,
												  @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												  HttpServletRequest req) {
		QueryWrapper<MdProcess> queryWrapper = QueryGenerator.initQueryWrapper(mdProcess, req.getParameterMap());
		Page<MdProcess> page = new Page<MdProcess>(pageNo, pageSize);
		IPage<MdProcess> pageList = mdProcessService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 *   添加
	 *
	 * @param mdProcessPage
	 * @return
	 */
	@AutoLog(value = "工序定义-添加")
	@Operation(summary="工序定义-添加", description="工序定义-添加")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody MdProcessPage mdProcessPage) {
		MdProcess mdProcess = new MdProcess();
		BeanUtils.copyProperties(mdProcessPage, mdProcess);
		mdProcessService.saveMain(mdProcess, mdProcessPage.getMdProcessDetailList());
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param mdProcessPage
	 * @return
	 */
	@AutoLog(value = "工序定义-编辑")
	@Operation(summary="工序定义-编辑", description="工序定义-编辑")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody MdProcessPage mdProcessPage) {
		MdProcess mdProcess = new MdProcess();
		BeanUtils.copyProperties(mdProcessPage, mdProcess);
		MdProcess mdProcessEntity = mdProcessService.getById(mdProcess.getId());
		if(mdProcessEntity==null) {
			return Result.error("未找到对应数据");
		}
		mdProcessService.updateMain(mdProcess, mdProcessPage.getMdProcessDetailList());
		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "工序定义-通过id删除")
	@Operation(summary="工序定义-通过id删除", description="工序定义-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		mdProcessService.delMain(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "工序定义-批量删除")
	@Operation(summary="工序定义-批量删除", description="工序定义-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.mdProcessService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "工序定义-通过id查询")
	@Operation(summary="工序定义-通过id查询", description="工序定义-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<MdProcess> queryById(@RequestParam(name="id",required=true) String id) {
		MdProcess mdProcess = mdProcessService.getById(id);
		if(mdProcess==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(mdProcess);

	}

	 /**
	  * 通过code查询
	  *
	  * @param processCode 工序编码
	  * @return 结果返回
	  */
	 @Operation(summary="工序定义-通过code查询", description="工序定义-通过code查询")
	 @GetMapping(value = "/queryByProcessCode")
	 public Result<MdProcess> queryByProcessCode(@RequestParam(name="processCode") String processCode) {
		 MdProcess mdProcess = mdProcessService.queryByProcessCode(processCode);
		 if(mdProcess==null) {
			 return Result.error("未找到对应数据");
		 }
		 return Result.OK(mdProcess);

	 }

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "工序定义明细通过主表ID查询")
	@Operation(summary="工序定义明细主表ID查询", description="工序定义明细-通主表ID查询")
	@GetMapping(value = "/queryMdProcessDetailByMainId")
	public Result<List<MdProcessDetail>> queryMdProcessDetailListByMainId(@RequestParam(name="id",required=true) String id) {
		List<MdProcessDetail> mdProcessDetailList = mdProcessDetailService.selectByMainId(id);
		return Result.OK(mdProcessDetailList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param mdProcess
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, MdProcess mdProcess) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<MdProcess> queryWrapper = QueryGenerator.initQueryWrapper(mdProcess, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
         List<String> selectionList = Arrays.asList(selections.split(","));
         queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<MdProcess> mdProcessList = mdProcessService.list(queryWrapper);

      // Step.3 组装pageList
      List<MdProcessPage> pageList = new ArrayList<MdProcessPage>();
      for (MdProcess main : mdProcessList) {
          MdProcessPage vo = new MdProcessPage();
          BeanUtils.copyProperties(main, vo);
          List<MdProcessDetail> mdProcessDetailList = mdProcessDetailService.selectByMainId(main.getId());
          vo.setMdProcessDetailList(mdProcessDetailList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new CdkitEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "工序定义列表");
      mv.addObject(NormalExcelConstants.CLASS, MdProcessPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("工序定义数据", "导出人:"+sysUser.getRealname(), "工序定义"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
	@Operation(summary="工序导入", description="工序导入")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<MdProcessPage> list = ExcelImportUtil.importExcel(file.getInputStream(), MdProcessPage.class, params);
              for (MdProcessPage page : list) {
                  MdProcess po = new MdProcess();
                  BeanUtils.copyProperties(page, po);
                  mdProcessService.saveMain(po, page.getMdProcessDetailList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }



	 /**
	  *   自动化新增工序，工艺路线，关联boom
	  *
	  * @param
	  * @return
	  */
	 @AutoLog(value = "自动化新增工序，工艺路线，关联boom")
	 @Operation(summary="自动化新增工序，工艺路线，关联boom", description="自动化新增工序，工艺路线，关联boom")
	 @PostMapping(value = "/autoInsert")
	 public Result<String> autoInsert(@RequestParam(name="flagText",required=true) String flagText,@RequestParam(name="workShopId",required=true) String workShopId) {
		 mdProcessService.autoInsert(flagText,workShopId);
		 return Result.OK("添加成功！");
	 }



	 @PostMapping("/importOrderExcel")
	 @Operation(summary="导入boom", description="导入boom")
	 public Result<String> importOrderExcel(
			 @RequestPart("file") MultipartFile file,
			 @RequestParam("boomId") String boomId) {

		 //判断文件类型是否正确
		 String originalFilename = file.getOriginalFilename();
		 String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
		 if (!".xls".equalsIgnoreCase(fileType) && !".xlsx".equalsIgnoreCase(fileType)) {
			 throw new CdkitCloudException("文件格式不正确,只支持excel格式文件");
		 }


		 // 验证文件是否为空
		 if (file.isEmpty()) {
			 throw new CdkitCloudException("数据不完整不能进行导入");
		 }

		 ImportParams params = new ImportParams();
		 //params.setTitleRows(2);
		 //params.setHeadRows(1);
		 params.setNeedSave(true);
		 try {
			 List<MdBoomPage> list = ExcelImportUtil.importExcel(file.getInputStream(), MdBoomPage.class, params);
			 mdProcessService.createData(boomId,list);
			 return Result.OK("文件导入成功！数据行数:" + list.size());
		 } catch (Exception e) {
			 log.error(e.getMessage(),e);
			 return Result.error("文件导入失败:"+e.getMessage());
		 } finally {
			 try {
				 file.getInputStream().close();
			 } catch (IOException e) {
				 e.printStackTrace();
			 }
		 }

/*		 // 假设处理成功，返回包含描述信息的Result对象
		 return Result.OK("导入成功！");;*/
	 }




	@PostMapping("/importProcessRemark")
	@Operation(summary="导入工序指导", description="导入工序指导")
	public Result<String> importProcessRemark(
			@RequestPart("file") MultipartFile file) {

		//判断文件类型是否正确
		String originalFilename = file.getOriginalFilename();
		String fileType = originalFilename.substring(originalFilename.lastIndexOf("."));
		if (!".xls".equalsIgnoreCase(fileType) && !".xlsx".equalsIgnoreCase(fileType)) {
			throw new CdkitCloudException("文件格式不正确,只支持excel格式文件");
		}


		// 验证文件是否为空
		if (file.isEmpty()) {
			throw new CdkitCloudException("数据不完整不能进行导入");
		}

		ImportParams params = new ImportParams();
		//params.setTitleRows(2);
		//params.setHeadRows(1);
		params.setNeedSave(true);
		try {
			List<MdProcessRemarkPage> list = ExcelImportUtil.importExcel(file.getInputStream(), MdProcessRemarkPage.class, params);
			mdProcessService.importProcessRemark(list);
			return Result.OK("文件导入成功！数据行数:" + list.size());
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			return Result.error("文件导入失败:"+e.getMessage());
		} finally {
			try {
				file.getInputStream().close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

/*		 // 假设处理成功，返回包含描述信息的Result对象
		 return Result.OK("导入成功！");;*/
	}

}
