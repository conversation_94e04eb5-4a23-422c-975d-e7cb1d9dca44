package com.cdkit.modules.plm.process.controller;

import java.util.Arrays;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.aspect.annotation.AutoLog;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.common.system.query.QueryGenerator;
import com.cdkit.modules.plm.process.entity.ProcessType;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.plm.process.service.IProcessTypeService;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTypeAddVO;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTypeEditVO;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTypeQueryVO;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 工艺类型管理
 * <AUTHOR>
 * @Date   2024-03-27
 * @Version V1.0
 */

@Tag(name="工艺类型管理")
@RestController
@RequestMapping("/processType")
@Slf4j
public class ProcessTypeController extends CdkitController<ProcessType, IProcessTypeService> {
	@Autowired
	private IProcessTypeService processTypeService;

	/**
	 * 分页列表查询
	 *
	 * @param reqProcessTypeQueryVO 工艺类型查询
	 * @param pageNo  页码
	 * @param pageSize 每页条数
	 * @param req HttpServletRequest
	 * @return 工艺类型列表
	 */
	@Operation(summary="工艺类型-分页列表查询", description="工艺类型-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ProcessType>> queryPageList(ReqProcessTypeQueryVO reqProcessTypeQueryVO,
													@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
													@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
													HttpServletRequest req) {
		ProcessType queryVo = BeanUtil.toBean(reqProcessTypeQueryVO, ProcessType.class);
		QueryWrapper<ProcessType> queryWrapper = QueryGenerator.initQueryWrapper(queryVo, req.getParameterMap());
		Page<ProcessType> page = new Page<>(pageNo, pageSize);
		IPage<ProcessType> pageList = processTypeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	/**
	 * 工艺类型新增
	 *
	 * @param reqProcessTypeAddVO 请求参数
	 * @return 返回结果
	 */
	@AutoLog(value = "工艺类型-新增")
	@Operation(summary="工艺类型-新增", description="工艺类型-新增")
	//@RequiresPermissions("process:processType:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody @Validated ReqProcessTypeAddVO reqProcessTypeAddVO) {
		processTypeService.add(reqProcessTypeAddVO);
		return Result.OK("添加成功！");
	}

	/**
	 * 工艺类型编辑
	 *
	 * @param reqProcessTypeEditVO 请求参数
	 * @return 返回结果
	 */
	@AutoLog(value = "工艺类型-编辑")
	@Operation(summary="工艺类型-编辑", description="工艺类型-编辑")
	//@RequiresPermissions("process:processType:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT})
	public Result<String> edit(@RequestBody @Validated ReqProcessTypeEditVO reqProcessTypeEditVO) {
		processTypeService.edit(reqProcessTypeEditVO);
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 *
	 * @param id 主键ID
	 * @return 返回结果
	 */
	@AutoLog(value = "工艺类型-通过id删除")
	@Operation(summary="工艺类型-通过id删除", description="工艺类型-通过id删除")
	//@RequiresPermissions("process:processType:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id") String id) {
		processTypeService.deleteById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 *
	 * @param ids 主键ID
	 * @return 返回结果
	 */
	@AutoLog(value = "工艺类型-批量删除")
	@Operation(summary="工艺类型-批量删除", description="工艺类型-批量删除，主键ID逗号分割")
	//@RequiresPermissions("process:processType:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch( @RequestParam(name="ids") String ids) {
		this.processTypeService.deleteByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id 主键ID
	 * @return 返回结果
	 */
	@Operation(summary="工艺类型-通过id查询", description="工艺类型-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ProcessType> queryById(@RequestParam(name="id") String id) {
		ProcessType processType = processTypeService.getById(id);
		if(processType == null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(processType);
	}

}
