package com.cdkit.modules.plm.product.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.md.entity.MdMaterial;
import com.cdkit.modules.plm.product.entity.ProductPart;

import java.util.List;

/**
 * @Description: product_part
 * @Author: mc
 * @Date:   2024-03-26
 * @Version: V1.0
 */
public interface IProductPartService extends IService<ProductPart> {

    List<MdMaterial> queryFungibleMaterialCodes(String id);

    void editFungibleMaterialCodes(String id, String materialCodes);
}
