package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：AddBorrowPartVO
 * @Date：2024/3/28 22:31
 */
@Data
@Schema(description = "新增图纸借用信息")
public class AddBorrowDocumentVO {
    @NotBlank(message = "sourceId不能为空")
    @Schema(description = "源id")
    private String sourceId;
    @NotBlank(message = "targetPid不能为空")
    @Schema(description = "借用后所在的父级id")
    private String targetPid;
}
