package com.cdkit.modules.plm.process.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.aspect.annotation.AutoLog;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.common.system.query.QueryGenerator;
import com.cdkit.common.system.vo.SelectTreeModel;
import com.cdkit.common.util.oConvertUtils;
import com.cdkit.modules.plm.process.entity.MdProcessType;
import com.cdkit.modules.plm.process.service.IMdProcessTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
* @Description: 工序分类主数据
* @Author: mc
* @Date:   2024-07-26
* @Version: V1.0
*/
@Tag(name="工序分类主数据")
@RestController
@RequestMapping("/base/mdProcessType")
@Slf4j
public class MdProcessTypeController extends CdkitController<MdProcessType, IMdProcessTypeService> {
   @Autowired
   private IMdProcessTypeService mdProcessTypeService;

   /**
    * 分页列表查询
    *
    * @param mdProcessType
    * @param pageNo
    * @param req
    * @return
    */
   //@AutoLog(value = "工序分类主数据-分页列表查询")
   @Operation(summary="工序分类主数据-分页列表查询", description="工序分类主数据-分页列表查询")
   @GetMapping(value = "/rootList")
   public Result<IPage<MdProcessType>> queryPageList(MdProcessType mdProcessType,
                                                     @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                     HttpServletRequest req) {
       String hasQuery = req.getParameter("hasQuery");
       if(hasQuery != null && "true".equals(hasQuery)){
           QueryWrapper<MdProcessType> queryWrapper =  QueryGenerator.initQueryWrapper(mdProcessType, req.getParameterMap());
           List<MdProcessType> list = mdProcessTypeService.queryTreeListNoPage(queryWrapper);
           IPage<MdProcessType> pageList = new Page<>(1, 10, list.size());
           pageList.setRecords(list);
           return Result.OK(pageList);
       }else{
           String parentId = mdProcessType.getPid();
           if (oConvertUtils.isEmpty(parentId)) {
               parentId = "0";
           }
           mdProcessType.setPid(null);
           QueryWrapper<MdProcessType> queryWrapper = QueryGenerator.initQueryWrapper(mdProcessType, req.getParameterMap());
           // 使用 eq 防止模糊查询
           queryWrapper.eq("pid", parentId);
           Page<MdProcessType> page = new Page<MdProcessType>(pageNo, 99999);
           IPage<MdProcessType> pageList = mdProcessTypeService.page(page, queryWrapper);
           return Result.OK(pageList);
       }
   }

    /**
     * 【vue3专用】加载节点的子数据
     *
     * @param pid
     * @return
     */
    @Operation(summary="【vue3专用】加载节点的子数据", description="【vue3专用】加载节点的子数据")
    @RequestMapping(value = "/loadTreeChildren", method = RequestMethod.GET)
    public Result<List<SelectTreeModel>> loadTreeChildren(@RequestParam(name = "pid") String pid) {
        Result<List<SelectTreeModel>> result = new Result<>();
        try {
            List<SelectTreeModel> ls = mdProcessTypeService.queryListByPid(pid);
            result.setResult(ls);
            result.setSuccess(true);
        } catch (Exception e) {
            e.printStackTrace();
            result.setMessage(e.getMessage());
            result.setSuccess(false);
        }
        return result;
    }

    /**
     * 【vue3专用】加载一级节点/如果是同步 则所有数据
     *
     * @param async
     * @param pcode
     * @return
     */
    @Operation(summary="【vue3专用】加载一级节点/如果是同步 则所有数据", description="【vue3专用】加载一级节点/如果是同步 则所有数据")
    @RequestMapping(value = "/loadTreeRoot", method = RequestMethod.GET)
    public Result<List<SelectTreeModel>> loadTreeRoot(@RequestParam(name = "async") Boolean async, @RequestParam(name = "pcode") String pcode) {
        Result<List<SelectTreeModel>> result = new Result<>();
        try {
            List<SelectTreeModel> ls = mdProcessTypeService.queryListByCode(pcode);
            if (!async) {
                loadAllChildren(ls);
            }
            result.setResult(ls);
            result.setSuccess(true);
        } catch (Exception e) {
            e.printStackTrace();
            result.setMessage(e.getMessage());
            result.setSuccess(false);
        }
        return result;
    }

    /**
     * 【vue3专用】递归求子节点 同步加载用到
     *
     * @param ls
     */
    private void loadAllChildren(List<SelectTreeModel> ls) {
        for (SelectTreeModel tsm : ls) {
            List<SelectTreeModel> temp = mdProcessTypeService.queryListByPid(tsm.getKey());
            if (temp != null && temp.size() > 0) {
                tsm.setChildren(temp);
                loadAllChildren(temp);
            }
        }
    }

    /**
     * 获取子数据
     * @param mdProcessType
     * @param req
     * @return
     */
   //@AutoLog(value = "工序分类主数据-获取子数据")
   @Operation(summary="工序分类主数据-获取子数据", description="工序分类主数据-获取子数据")
   @GetMapping(value = "/childList")
   public Result<IPage<MdProcessType>> queryPageList(MdProcessType mdProcessType,HttpServletRequest req) {
       QueryWrapper<MdProcessType> queryWrapper = QueryGenerator.initQueryWrapper(mdProcessType, req.getParameterMap());
       List<MdProcessType> list = mdProcessTypeService.list(queryWrapper);
       IPage<MdProcessType> pageList = new Page<>(1, 10, list.size());
       pageList.setRecords(list);
       return Result.OK(pageList);
   }

   /**
     * 批量查询子节点
     * @param parentIds 父ID（多个采用半角逗号分割）
     * @return 返回 IPage
     * @param parentIds
     * @return
     */
   //@AutoLog(value = "工序分类主数据-批量获取子数据")
   @Operation(summary="工序分类主数据-批量获取子数据", description="工序分类主数据-批量获取子数据")
   @GetMapping("/getChildListBatch")
   public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
       try {
           QueryWrapper<MdProcessType> queryWrapper = new QueryWrapper<>();
           List<String> parentIdList = Arrays.asList(parentIds.split(","));
           queryWrapper.in("pid", parentIdList);
           List<MdProcessType> list = mdProcessTypeService.list(queryWrapper);
           IPage<MdProcessType> pageList = new Page<>(1, 10, list.size());
           pageList.setRecords(list);
           return Result.OK(pageList);
       } catch (Exception e) {
           log.error(e.getMessage(), e);
           return Result.error("批量查询子节点失败：" + e.getMessage());
       }
   }

   /**
    *   添加
    *
    * @param mdProcessType
    * @return
    */
   @AutoLog(value = "工序分类主数据-添加")
   @Operation(summary="工序分类主数据-添加", description="工序分类主数据-添加")
   @PostMapping(value = "/add")
   public Result<String> add(@RequestBody MdProcessType mdProcessType) {
       mdProcessTypeService.addMdProcessType(mdProcessType);
       return Result.OK("添加成功！");
   }

   /**
    *  编辑
    *
    * @param mdProcessType
    * @return
    */
   @AutoLog(value = "工序分类主数据-编辑")
   @Operation(summary="工序分类主数据-编辑", description="工序分类主数据-编辑")
   @RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
   public Result<String> edit(@RequestBody MdProcessType mdProcessType) {
       mdProcessTypeService.updateMdProcessType(mdProcessType);
       return Result.OK("编辑成功!");
   }

   /**
    *   通过id删除
    *
    * @param id
    * @return
    */
   @AutoLog(value = "工序分类主数据-通过id删除")
   @Operation(summary="工序分类主数据-通过id删除", description="工序分类主数据-通过id删除")
   @DeleteMapping(value = "/delete")
   public Result<String> delete(@RequestParam(name="id",required=true) String id) {
       mdProcessTypeService.deleteMdProcessType(id);
       return Result.OK("删除成功!");
   }

   /**
    *  批量删除
    *
    * @param ids
    * @return
    */
   @AutoLog(value = "工序分类主数据-批量删除")
   @Operation(summary="工序分类主数据-批量删除", description="工序分类主数据-批量删除")
   @DeleteMapping(value = "/deleteBatch")
   public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
       this.mdProcessTypeService.removeByIds(Arrays.asList(ids.split(",")));
       return Result.OK("批量删除成功！");
   }

   /**
    * 通过id查询
    *
    * @param id
    * @return
    */
   //@AutoLog(value = "工序分类主数据-通过id查询")
   @Operation(summary="工序分类主数据-通过id查询", description="工序分类主数据-通过id查询")
   @GetMapping(value = "/queryById")
   public Result<MdProcessType> queryById(@RequestParam(name="id",required=true) String id) {
       MdProcessType mdProcessType = mdProcessTypeService.getById(id);
       if(mdProcessType==null) {
           return Result.error("未找到对应数据");
       }
       return Result.OK(mdProcessType);
   }

   /**
   * 导出excel
   *
   * @param request
   * @param mdProcessType
   */
   @RequestMapping(value = "/exportXls")
   public ModelAndView exportXls(HttpServletRequest request, MdProcessType mdProcessType) {
       return super.exportXls(request, mdProcessType, MdProcessType.class, "工序分类主数据");
   }

   /**
     * 通过excel导入数据
   *
   * @param request
   * @param response
   * @return
   */
   @Operation(summary="工序分类-导入", description="工序分类-导入")
   @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
   public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
       return super.importExcel(request, response, MdProcessType.class);
   }

}
