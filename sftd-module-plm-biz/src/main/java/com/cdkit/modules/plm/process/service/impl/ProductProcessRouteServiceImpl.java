package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.cdkit.common.config.TenantContext;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.plm.config.StrategyByFactory;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.enums.ProductTreeTypeEnum;
import com.cdkit.modules.plm.process.entity.*;
import com.cdkit.modules.plm.process.mapper.MdProcessDetailMapper;
import com.cdkit.modules.plm.process.mapper.MdProcessMapper;
import com.cdkit.modules.plm.process.mapper.ProductProcessDetailMapper;
import com.cdkit.modules.plm.process.mapper.ProductProcessRouteMapper;
import com.cdkit.modules.plm.process.service.IFactoryStrategy;
import com.cdkit.modules.plm.process.service.IMdProcessRouteDetailService;
import com.cdkit.modules.plm.process.service.IProductProcessDetailService;
import com.cdkit.modules.plm.process.service.IProductProcessRouteService;
import com.cdkit.modules.plm.process.vo.req.MdProcessVo;
import com.cdkit.modules.plm.process.vo.req.ReqProductProcessRouteVo;
import com.cdkit.modules.plm.process.vo.resp.RespProcessDetailVO;
import com.cdkit.modules.plm.process.vo.resp.RespProcessVO;
import com.cdkit.modules.plm.product.dto.ProductListDTO;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.ProductPartExtend;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.mapper.ProductPartExtendMapper;
import com.cdkit.modules.plm.product.mapper.ProductTreeMapper;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.resp.ProductFileVO;
import com.cdkit.modules.plm.product.vo.resp.ProductPartVO;
import com.cdkit.modules.plm.product.vo.resp.ProductProcessRouteVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * @Description: 产品工艺路线表
 * @Author: cdkit-boot
 * @Date: 2024-08-05
 * @Version: V1.0
 */
@Service
@Slf4j
public class ProductProcessRouteServiceImpl extends ServiceImpl<ProductProcessRouteMapper, ProductProcessRoute> implements IProductProcessRouteService {
    @Resource @Lazy
    private IProductTreeService productTreeService;
    @Resource
    private IMdProcessRouteDetailService mdProcessRouteDetailService;
    @Resource
    private MdProcessMapper mdProcessMapper;
    @Resource
    private ProductProcessRouteMapper productProcessRouteMapper;
    @Resource
    private MdProcessDetailMapper mdProcessDetailMapper;
    @Resource
    private ProductProcessDetailMapper productProcessDetailMapper;
    @Resource
    private IProductProcessDetailService productProcessDetailService;
    @Resource
    private ProductPartExtendMapper productPartExtendMapper;
    @Resource
    private StrategyByFactory strategyByFactory;
    @Autowired
    private ProductTreeMapper productTreeMapper;

    /**
     * 导入工艺路线
     *
     * @param reqProductProcessRouteVo 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importProcessRoute(ReqProductProcessRouteVo reqProductProcessRouteVo) {
        log.info("导入工艺路线:{}", JSON.toJSONString(reqProductProcessRouteVo));
        // 查询当前节点是否为产品，如果不是则继续找父级节点
        String productRootId = findProductRootId(reqProductProcessRouteVo.getCurrProductId());
        if (StringUtils.isEmpty(productRootId)) {
            throw new CdkitCloudException("无法找到产成品对的工艺路线");
        }
        // 查询产品BOM
        List<ProductTreeDTO> tree = productTreeService.tree(Collections.singletonList(productRootId), NodeTypeEnum.PROCESS_TREE,null);
        // 查询工艺路线
        List<MdProcessRouteDetail> mdProcessRouteDetails = mdProcessRouteDetailService.selectByMainId(reqProductProcessRouteVo.getProcessRouteId());
        // 反转数组
        Collections.reverse(mdProcessRouteDetails);
        List<ProductListDTO> processListDTO = new ArrayList<>();
        treeToListWithOutChildren(tree.get(0), processListDTO);
        // 对平铺后的产品BOM进行排序
        processListDTO = processListDTO.stream().sorted(Comparator.comparingInt(ProductListDTO::getLevel)
                .thenComparing(ProductListDTO::getSort)).collect(Collectors.toList());
        if (processListDTO.size() - 1 > mdProcessRouteDetails.size()) {
            throw new CdkitCloudException("工艺路线与工艺BOM匹配失败，请检查工艺路线中工序是否有缺失");
        }
        List<ProductProcessRoute> list = new ArrayList<>();
        // BOM反转后匹配工艺路线,并组装各节点对应工序
        for (int i = 0; i < processListDTO.size(); i++) {
            ProductProcessRoute productProcessRoute = new ProductProcessRoute();
            ProductListDTO productListDTO = processListDTO.get(i);
            // 获取各个节点的工序
            if (mdProcessRouteDetails.size() <= i || mdProcessRouteDetails.get(i) == null) {
                continue;
            }
            MdProcessRouteDetail mdProcessRouteDetail = mdProcessRouteDetails.get(i);

            MdProcess mdProcess = mdProcessMapper.selectById(mdProcessRouteDetail.getProcessId());
            productProcessRoute.setCurrProductId(reqProductProcessRouteVo.getCurrProductId());
            productProcessRoute.setProcessCode(mdProcess.getProcessCode());
            productProcessRoute.setProcessId(mdProcess.getId());
            productProcessRoute.setProcessContent(mdProcessRouteDetail.getProcessContent());
            productProcessRoute.setProcessNumber(mdProcessRouteDetail.getProcessNumber());
            productProcessRoute.setProcessRouteId(reqProductProcessRouteVo.getProcessRouteId());
            productProcessRoute.setProductId(productListDTO.getId());
            productProcessRoute.setMaterialCode(productListDTO.getMaterialCode());
            productProcessRoute.setProcessRouteDetailId(mdProcessRouteDetail.getId());
            productProcessRoute.setWorkHour(mdProcess.getWorkHour());
            list.add(productProcessRoute);


        }

        if (list.size() > 0) {
            productProcessRouteMapper.removeByCurProductId(reqProductProcessRouteVo.getCurrProductId());
            this.saveBatch(list);
            for (ProductProcessRoute item : list) {
                saveProductProcessDetail(item.getProductId(), item.getProcessCode());
            }
        }
    }

    /**
     * 查询当前节点的父级ID为产品的节点
     *
     * @param id 节点ID
     * @return 节点ID
     */
    private String findProductRootId(String id) {
        ProductTree productTree = productTreeService.getById(id);
        if (productTree == null) {
            return null;
        }
        if (ProductTreeTypeEnum.PRODUCT_FINAL_ASSEMBLE.getCode().equals(productTree.getType()) ||
                ProductTreeTypeEnum.PRODUCT_PART.getCode().equals(productTree.getType())) {
            return id;
        } else {
            findProductRootId(productTree.getPid());

        }
        return null;

    }

    private void saveProductProcessDetail(String productId, String processCode) {
        // 查询步骤
        MdProcess mdProcess = Optional.ofNullable(mdProcessMapper.selectOne(new LambdaQueryWrapper<MdProcess>()
                .eq(MdProcess::getProcessCode, processCode))).orElse(new MdProcess());

        List<MdProcessDetail> mdProcessDetails = mdProcessDetailMapper.selectByMainId(mdProcess.getId());
        ArrayList<ProductProcessDetail> processDetails = new ArrayList<>();
        for (MdProcessDetail item : mdProcessDetails) {
            ProductProcessDetail vo = new ProductProcessDetail();
            BeanUtils.copyProperties(item, vo, "id","createTime","createBy","updateTime","upgradeBy");
            vo.setProductId(productId);
            vo.setProcessId(mdProcess.getId());
            processDetails.add(vo);
        }
        productProcessDetailMapper.removeByProcessId(mdProcess.getId(), productId);
        productProcessDetailService.saveBatch(processDetails);
    }

    /**
     * 查询节点对应工艺路线
     *
     * @param id 节点ID
     * @return 返回结果
     */
    @Override
    public List<ProductProcessRoute> listProcessRoute(String id) {
        List<ProductProcessRoute> productProcessRoutes = productProcessRouteMapper.selectList(new LambdaQueryWrapper<ProductProcessRoute>()
                .eq(ProductProcessRoute::getCurrProductId, id)
                .eq(ProductProcessRoute::getIsNew, 1)
                .orderByAsc(ProductProcessRoute::getProcessNumber));
        if (productProcessRoutes == null || productProcessRoutes.isEmpty()) {
            // 如果没有当前节点的工艺路线，则通过查询产品BOM，通BOM下的各个节点ID查询工序，组成工艺路线
            List<ProductTreeDTO> tree = productTreeService.tree(Collections.singletonList(id), NodeTypeEnum.PROCESS_TREE,null);
            List<ProductListDTO> processListDTO = new ArrayList<>();
            treeToList(tree.get(0), processListDTO);
            List<String> productIds = processListDTO.stream().map(ProductListDTO::getId).collect(Collectors.toList());
            productProcessRoutes = productProcessRouteMapper.selectList(new LambdaQueryWrapper<ProductProcessRoute>()
                    .eq(ProductProcessRoute::getIsNew, 1)
                    .in(ProductProcessRoute::getProductId, productIds));


            productProcessRoutes = productProcessRoutes.stream().sorted(comparing(ProductProcessRoute::getProcessNumber))
                    .collect(collectingAndThen(
                            toCollection(() -> new TreeSet<>(comparing(ProductProcessRoute::getProcessNumber))), ArrayList::new));
        }
        // 有当前节点对应的工艺路线
        for (ProductProcessRoute item : productProcessRoutes) {
            item.setProcessName(Optional.ofNullable(mdProcessMapper.selectOne(new LambdaQueryWrapper<MdProcess>()
                    .eq(MdProcess::getProcessCode, item.getProcessCode()))).orElse(new MdProcess()).getProcessName());
        }
        return productProcessRoutes;
    }

    /**
     * 查询节点对应工艺
     *
     * @param id 节点ID
     * @return 返回结果
     */
    @Override
    public RespProcessVO getProcessDetail(String id) {
        ProductProcessRoute productProcessRoute = productProcessRouteMapper.selectOne(new LambdaQueryWrapper<ProductProcessRoute>()
                .eq(ProductProcessRoute::getProductId, id)
                .eq(ProductProcessRoute::getIsNew, 1));
        if (productProcessRoute == null) {
            return null;
        }
        // 查询步骤
        MdProcess mdProcess = Optional.ofNullable(mdProcessMapper.selectOne(new LambdaQueryWrapper<MdProcess>()
                .eq(MdProcess::getProcessCode, productProcessRoute.getProcessCode()))).orElse(new MdProcess());
        RespProcessVO respProcessVO = new RespProcessVO();
        respProcessVO.setId(productProcessRoute.getId());
        respProcessVO.setProcessCode(productProcessRoute.getProcessCode());
        respProcessVO.setProcessContent(productProcessRoute.getProcessContent());
        respProcessVO.setProcessName(mdProcess.getProcessName());
        respProcessVO.setProcessId(productProcessRoute.getProcessId());
        respProcessVO.setWorkHour(productProcessRoute.getWorkHour());

        LambdaQueryWrapper<ProductProcessDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductProcessDetail::getProcessId, mdProcess.getId())
                .eq(ProductProcessDetail::getProductId, id)
                .eq(ProductProcessDetail::getIsNew, 1)
                .orderByAsc(ProductProcessDetail::getNumber);

//        List<ProductProcessDetail> productProcessDetails = productProcessDetailMapper.selectByProcessId(mdProcess.getId());
        List<ProductProcessDetail> productProcessDetails = productProcessDetailMapper.selectList(queryWrapper);
        ArrayList<RespProcessDetailVO> processDetails = new ArrayList<>();
        for (ProductProcessDetail item : productProcessDetails) {
            RespProcessDetailVO vo = new RespProcessDetailVO();
            BeanUtils.copyProperties(item, vo);
            processDetails.add(vo);
        }
        respProcessVO.setProcessDetail(processDetails);
        return respProcessVO;
    }

    /**
     * 保存节点对应工艺内容或步骤
     *
     * @param respProcessVO 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProcessDetail(RespProcessVO respProcessVO) {
        log.info("保存节点对应工艺内容或步骤:{}", JSON.toJSONString(respProcessVO));
        ProductProcessRoute productProcessRoute = productProcessRouteMapper.selectById(respProcessVO.getId());
        productProcessRoute.setProcessContent(respProcessVO.getProcessContent());
        productProcessRoute.setWorkHour(respProcessVO.getWorkHour());
        productProcessRouteMapper.updateById(productProcessRoute);

        List<RespProcessDetailVO> processDetail = respProcessVO.getProcessDetail();
        for (RespProcessDetailVO item : processDetail) {
            ProductProcessDetail vo = new ProductProcessDetail();
            BeanUtils.copyProperties(item, vo);
            productProcessDetailService.updateById(vo);
        }

    }

    /**
     * 通过产品编码或代号查询产品档案
     *
     * @param code 产品编码或代号
     * @return 返回结果
     */
    @Override
    public ProductFileVO getProductFile(String code) {
        ProductFileVO productFileVO = new ProductFileVO();
        // 查询产品BOM
        List<ProductTree> productTreeList = productTreeService.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getMaterialCode, code)
                .eq(ProductTree::getConverted, 1)
                .eq(ProductTree::getEnable,0));
        List<ProductTreeDTO> tree = new ArrayList<>();
        List<ProductProcessRoute> productProcessRoutes = new ArrayList<>();
        if (productTreeList.size() > 0) {
            ProductTree productTree = productTreeList.get(0);
            tree = productTreeService.tree(Collections.singletonList(productTree.getId()), NodeTypeEnum.PROCESS_TREE,null);

            // 查询工艺路线
            productProcessRoutes = this.listProcessRoute(productTree.getId());
        }

        if (tree.size() > 0) {
            Map<String, ProductProcessRoute> mappedProductProcessRoute = productProcessRoutes.stream().collect(
                    Collectors.toMap(ProductProcessRoute::getProductId, (p) -> p));
            ProductTreeDTO productBom = tree.get(0);
            List<String> productIdList = new ArrayList<>();
            getProductIdsFromBom(productBom, productIdList);
            // 查询定制属性
            List<ProductPartExtend> productPartExtends = productPartExtendMapper.selectList(new LambdaQueryWrapper<ProductPartExtend>()
                    .eq(ProductPartExtend::getDefinedType, "2")
                    .in(ProductPartExtend::getProductId, productIdList));
            // 设置每个BOM对应的工序
            setProcessIdForBom(productBom, mappedProductProcessRoute, productPartExtends);
            productFileVO.setProductBom(productBom);
        }

        if (!productProcessRoutes.isEmpty()) {

            List<ProductProcessRouteVO> list = new ArrayList<>();
            // 查询工序信息
            List<MdProcess> mdProcesses = mdProcessMapper.selectList(new LambdaQueryWrapper<>());
            Map<String, MdProcess> mappedMdProcess = new HashMap<>(64);
            if (mdProcesses != null && mdProcesses.size() > 0) {
                mappedMdProcess = mdProcesses.stream().collect(
                        Collectors.toMap(MdProcess::getId, (p) -> p));
            }
            // 查询工艺路
            List<MdProcessRouteDetail> mdProcessRouteDetails = mdProcessRouteDetailService.selectByMainId(productProcessRoutes.get(0).getProcessRouteId());
            Map<String, MdProcessRouteDetail> mdProcessRouteDetailMap = mdProcessRouteDetails.stream().collect(
                    Collectors.toMap(MdProcessRouteDetail::getId, (p) -> p));

            for (ProductProcessRoute item : productProcessRoutes) {
                ProductProcessRouteVO vo = new ProductProcessRouteVO();
                BeanUtil.copyProperties(item, vo);
                ProductPartVO productPartVO = productTreeService.queryProductPartById(vo.getProductId());
                vo.setOutputProductName(productPartVO.getName());
                MdProcess mdProcess = mappedMdProcess.get(item.getProcessId());
                vo.setOutputUnitId(mdProcess.getOutputUnitId());
                vo.setOutputSecUnitId(mdProcess.getOutputSecUnitId());
                vo.setAssembleQuantity(productPartVO.getAssembleQuantity());
                vo.setStandardQuantity(productPartVO.getStandardQuantity());
                MdProcessRouteDetail mdProcessRouteDetail = mdProcessRouteDetailMap.get(item.getProcessRouteDetailId());
                vo.setOutsourcedFlag(mdProcessRouteDetail.getOutsourcedFlag());
                vo.setIsQuality(mdProcessRouteDetail.getIsQuality());
                vo.setIsKey(mdProcess.getIsKey());
                vo.setReportRatio(mdProcessRouteDetail.getReportRatio());
                list.add(vo);
            }
            productFileVO.setProcessRoute(list);
        }
        return productFileVO;
    }

    /**
     * 通过产品BOM ID查询产品档案
     *
     * @param id 产品BOM ID
     * @return 返回结果
     */
    @Override
    public ProductFileVO getProductFileById(String id) {
        ProductFileVO productFileVO = new ProductFileVO();
        // 查询产品BOM
        ProductTree productTree = productTreeService.getById(id);
        List<ProductTreeDTO> tree = productTreeService.tree(Collections.singletonList(productTree.getId()), NodeTypeEnum.PROCESS_TREE,null);

        // 查询工艺BOM
        ProductTree productTreeProcess = productTreeService.getById(id);
        // 查询工艺路线
        List<ProductProcessRoute> productProcessRoutes = this.listProcessRoute(productTreeProcess.getId());
        if (tree.size() > 0) {
            Map<String, ProductProcessRoute> mappedProductProcessRoute = productProcessRoutes.stream().collect(
                    Collectors.toMap(ProductProcessRoute::getProductId, (p) -> p));
            ProductTreeDTO productBom = tree.get(0);
            List<String> productIdList = new ArrayList<>();
            getProductIdsFromBom(productBom, productIdList);
            // 查询定制属性
            List<ProductPartExtend> productPartExtends = productPartExtendMapper.selectList(new LambdaQueryWrapper<ProductPartExtend>()
                    .eq(ProductPartExtend::getDefinedType, "2")
                    .in(ProductPartExtend::getProductId, productIdList));

            // 设置每个BOM对应的工序
            setProcessIdForBom(productBom, mappedProductProcessRoute, productPartExtends);
            productFileVO.setProductBom(productBom);
        }

        if (!productProcessRoutes.isEmpty()) {

            List<ProductProcessRouteVO> list = new ArrayList<>();
            // 查询工序信息
            List<MdProcess> mdProcesses = mdProcessMapper.selectList(new LambdaQueryWrapper<>());
            Map<String, MdProcess> mappedMdProcess = new HashMap<>(64);
            if (mdProcesses != null && mdProcesses.size() > 0) {
                mappedMdProcess = mdProcesses.stream().collect(
                        Collectors.toMap(MdProcess::getId, (p) -> p));
            }
            // 查询工艺路
            List<MdProcessRouteDetail> mdProcessRouteDetails = mdProcessRouteDetailService.selectByMainId(productProcessRoutes.get(0).getProcessRouteId());
            Map<String, MdProcessRouteDetail> mdProcessRouteDetailMap = mdProcessRouteDetails.stream().collect(
                    Collectors.toMap(MdProcessRouteDetail::getId, (p) -> p));

            for (ProductProcessRoute item : productProcessRoutes) {
                ProductProcessRouteVO vo = new ProductProcessRouteVO();
                BeanUtil.copyProperties(item, vo);
                ProductPartVO productPartVO = productTreeService.queryProductPartById(vo.getProductId());
                vo.setOutputProductName(productPartVO.getName());
                MdProcess mdProcess = mappedMdProcess.get(item.getProcessId());
                vo.setOutputUnitId(mdProcess.getOutputUnitId());
                vo.setOutputSecUnitId(mdProcess.getOutputSecUnitId());
                vo.setAssembleQuantity(productPartVO.getAssembleQuantity());
                vo.setStandardQuantity(productPartVO.getStandardQuantity());
                MdProcessRouteDetail mdProcessRouteDetail = mdProcessRouteDetailMap.get(item.getProcessRouteDetailId());
                vo.setOutsourcedFlag(mdProcessRouteDetail.getOutsourcedFlag());
                vo.setIsQuality(mdProcessRouteDetail.getIsQuality());
                vo.setIsKey(mdProcess.getIsKey());
                vo.setReportRatio(mdProcessRouteDetail.getReportRatio());
                list.add(vo);
            }
            productFileVO.setProcessRoute(list);
        }
        return productFileVO;
    }

    /**
     * 从BOM中获取各个节点ID
     *
     * @param productBom    产品BOM
     * @param productIdList 节点ID
     */
    private void getProductIdsFromBom(ProductTreeDTO productBom, List<String> productIdList) {
        productIdList.add(productBom.getId());
        List<ProductTreeDTO> children = productBom.getChildren();
        if (children != null && children.size() > 0) {
            for (ProductTreeDTO item : children) {
                getProductIdsFromBom(item, productIdList);
            }
        }
    }

    /**
     * 查询产品档案列表
     *
     * @return 返回结果
     */
    @Override
    public List<ProductTree> getProductFileList() {
        // 各工厂定制
        IFactoryStrategy businessMap = strategyByFactory.getBusinessMap(Integer.parseInt(TenantContext.getTenant()));
        return businessMap.getProductFileList(null);
    }

    /**
     * 设置每个BOM对应的工序
     *
     * @param productBom                BOM树
     * @param mappedProductProcessRoute 工艺路线
     * @param productPartExtends        定制属性
     */
    private void setProcessIdForBom(ProductTreeDTO productBom, Map<String, ProductProcessRoute> mappedProductProcessRoute, List<ProductPartExtend> productPartExtends) {
        if (mappedProductProcessRoute != null) {
            ProductProcessRoute productProcessRoute = mappedProductProcessRoute.get(productBom.getId());
            if (productProcessRoute != null) {
                productBom.setProcessId(productProcessRoute.getProcessId());
                productBom.setWorkHour(productProcessRoute.getWorkHour());
            }
            ProductProcessRoute productProcessRouteParent = mappedProductProcessRoute.get(productBom.getPid());
            if (productProcessRouteParent != null) {
                productBom.setParentProcessId(productProcessRouteParent.getProcessId());
                productBom.setProcessRouteDetailId(productProcessRouteParent.getId());
            }
        }
        List<ProductPartExtend> collect = productPartExtends.stream().filter(v -> v.getProductId().equals(productBom.getId())).collect(Collectors.toList());
        productBom.setCustomizedProperties(collect);
        if (productBom.getChildren() != null && !productBom.getChildren().isEmpty()) {
            for (ProductTreeDTO item : productBom.getChildren()) {
                setProcessIdForBom(item, mappedProductProcessRoute, productPartExtends);
            }
        }
    }


    /**
     * 产品树结构转为列表结构
     *
     * @param productTreeDTO 树结构
     * @param productListDTO 列表结构
     */
    private void treeToList(ProductTreeDTO productTreeDTO, List<ProductListDTO> productListDTO) {
        ProductListDTO dto = new ProductListDTO();
        dto.setId(productTreeDTO.getId());
        dto.setCode(productTreeDTO.getCode());
        dto.setMaterialCode(productTreeDTO.getMaterialCode());
        dto.setSort(productTreeDTO.getSort());
        productListDTO.add(dto);
        if (productTreeDTO.getChildren() != null && !productTreeDTO.getChildren().isEmpty()) {
            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
                treeToList(item, productListDTO);
            }
        }
    }

    /**
     * 产品树结构转为列表结构--如果不包含子节点则不平铺
     *
     * @param productTreeDTO 树结构
     * @param productListDTO 列表结构
     */
    @Override
    public void treeToListWithOutChildren(ProductTreeDTO productTreeDTO, List<ProductListDTO> productListDTO) {
        ProductListDTO dto = new ProductListDTO();
        dto.setId(productTreeDTO.getId());
        dto.setCode(productTreeDTO.getCode());
        dto.setMaterialCode(productTreeDTO.getMaterialCode());
        dto.setSort(productTreeDTO.getSort());
        dto.setLevel(productTreeDTO.getLevel());
        productListDTO.add(dto);
        if (productTreeDTO.getChildren() != null && !productTreeDTO.getChildren().isEmpty()) {
            for (ProductTreeDTO item : productTreeDTO.getChildren()) {
                if (item.getChildren() != null && !item.getChildren().isEmpty()) {
                    treeToListWithOutChildren(item, productListDTO);
                }
            }
        }
    }

    /**
     * 判断产品BOM是否含有多个子节点
     *
     * @param productListDTO 产品BOM
     * @return 布尔
     */
    private boolean validHasChildren(ProductTreeDTO productListDTO) {
        if (productListDTO.getChildren().size() > 1) {
            return true;
        } else {
            for (ProductTreeDTO item : productListDTO.getChildren()) {
                validHasChildren(item);
            }
        }
        return false;
    }

    @Override
    public void add(MdProcessVo mdProcessVo) {
        MdProcess mdProcess = mdProcessMapper.selectById(mdProcessVo.getId());

        if (mdProcess == null) {
            throw new CdkitCloudException("该工序不存在！");
        }
        ProductTree productTree = productTreeMapper.selectById(mdProcessVo.getProductId());
        if (productTree == null) {
            throw new CdkitCloudException("该树形结构不存在！");
        }

        ProductProcessRoute productProcessRoute = new ProductProcessRoute();
        productProcessRoute.setCurrProductId(mdProcessVo.getCurrProductId());
        productProcessRoute.setProcessCode(mdProcess.getProcessCode());
        productProcessRoute.setProcessId(mdProcess.getId());
        productProcessRoute.setProcessContent(mdProcessVo.getProcessContent());
        productProcessRoute.setProcessNumber(mdProcessVo.getProcessNumber());
        productProcessRoute.setProcessRouteId(mdProcessVo.getProcessRouteId());
        productProcessRoute.setProductId(productTree.getId());
        productProcessRoute.setMaterialCode(productTree.getMaterialCode());
        //有问题再说 mc
//            productProcessRoute.setProcessRouteDetailId(mdProcessRouteDetail.getId());
        productProcessRoute.setWorkHour(mdProcess.getWorkHour());
        productProcessRouteMapper.insert(productProcessRoute);
    }

    /**
     * 通过代号查询产品档案
     *
     * @param code 产品代号
     * @return 返回结果
     */
    @Override
    public ProductFileVO getProductFileByCode(String code) {
        ProductFileVO productFileVO = new ProductFileVO();
        // 查询产品BOM
        List<ProductTree> productTreeList = productTreeService.list(new LambdaQueryWrapper<ProductTree>().eq(ProductTree::getCode, code)
                .eq(ProductTree::getConverted, 1));
        List<ProductTreeDTO> tree = new ArrayList<>();
        List<ProductProcessRoute> productProcessRoutes = new ArrayList<>();
        if (productTreeList.size() > 0) {
            ProductTree productTree = productTreeList.get(0);
            tree = productTreeService.tree(Collections.singletonList(productTree.getId()), NodeTypeEnum.PROCESS_TREE,null);

            // 查询工艺路线
            productProcessRoutes = this.listProcessRoute(productTree.getId());
        }

        if (tree.size() > 0) {
            Map<String, ProductProcessRoute> mappedProductProcessRoute = productProcessRoutes.stream().collect(
                    Collectors.toMap(ProductProcessRoute::getProductId, (p) -> p));
            ProductTreeDTO productBom = tree.get(0);
            List<String> productIdList = new ArrayList<>();
            getProductIdsFromBom(productBom, productIdList);
            // 查询定制属性
            List<ProductPartExtend> productPartExtends = productPartExtendMapper.selectList(new LambdaQueryWrapper<ProductPartExtend>()
                    .eq(ProductPartExtend::getDefinedType, "2")
                    .in(ProductPartExtend::getProductId, productIdList));

            // 设置每个BOM对应的工序
            setProcessIdForBom(productBom, mappedProductProcessRoute, productPartExtends);
            productFileVO.setProductBom(productBom);
        }

        if (!productProcessRoutes.isEmpty()) {

            List<ProductProcessRouteVO> list = new ArrayList<>();
            // 查询工序信息
            List<MdProcess> mdProcesses = mdProcessMapper.selectList(new LambdaQueryWrapper<>());
            Map<String, MdProcess> mappedMdProcess = new HashMap<>(64);
            if (mdProcesses != null && mdProcesses.size() > 0) {
                mappedMdProcess = mdProcesses.stream().collect(
                        Collectors.toMap(MdProcess::getId, (p) -> p));
            }
            // 查询工艺路
            List<MdProcessRouteDetail> mdProcessRouteDetails = mdProcessRouteDetailService.selectByMainId(productProcessRoutes.get(0).getProcessRouteId());
            Map<String, MdProcessRouteDetail> mdProcessRouteDetailMap = mdProcessRouteDetails.stream().collect(
                    Collectors.toMap(MdProcessRouteDetail::getId, (p) -> p));

            for (ProductProcessRoute item : productProcessRoutes) {
                ProductProcessRouteVO vo = new ProductProcessRouteVO();
                BeanUtil.copyProperties(item, vo);
                ProductPartVO productPartVO = productTreeService.queryProductPartById(vo.getProductId());
                vo.setOutputProductName(productPartVO.getName());
                MdProcess mdProcess = mappedMdProcess.get(item.getProcessId());
                vo.setOutputUnitId(mdProcess.getOutputUnitId());
                vo.setOutputSecUnitId(mdProcess.getOutputSecUnitId());
                vo.setAssembleQuantity(productPartVO.getAssembleQuantity());
                vo.setStandardQuantity(productPartVO.getStandardQuantity());
                MdProcessRouteDetail mdProcessRouteDetail = mdProcessRouteDetailMap.get(item.getProcessRouteDetailId());
                vo.setOutsourcedFlag(mdProcessRouteDetail.getOutsourcedFlag());
                vo.setIsQuality(mdProcessRouteDetail.getIsQuality());
                vo.setIsKey(mdProcess.getIsKey());
                vo.setReportRatio(mdProcessRouteDetail.getReportRatio());
                list.add(vo);
            }
            productFileVO.setProcessRoute(list);
        }
        return productFileVO;
    }

    /**
     * 查询配方对应工艺路线
     *
     * @param formulationId 配方ID
     * @param productId 产品ID
     * @return 返回结果
     */
    @Override
    public List<ProductProcessRoute> listProcessRouteByFormulationIdAndProductId(String formulationId, String productId) {
        return productProcessRouteMapper.selectList(new LambdaQueryWrapper<ProductProcessRoute>()
                .eq(ProductProcessRoute::getFormulationId, formulationId)
                        .eq(ProductProcessRoute::getCurrProductId, productId)
                .eq(ProductProcessRoute::getIsNew, 1)
                .orderByAsc(ProductProcessRoute::getProcessNumber));
    }

}
