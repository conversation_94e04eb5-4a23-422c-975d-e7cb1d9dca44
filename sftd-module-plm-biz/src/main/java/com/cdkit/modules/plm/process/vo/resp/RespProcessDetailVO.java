package com.cdkit.modules.plm.process.vo.resp;

import com.alibaba.fastjson.JSONArray;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/6
 */
@Data
public class RespProcessDetailVO {
    /**主键ID*/
    @Schema(description = "主键ID")
    private String id;
    /**序号*/
    @Schema(description = "序号")
    private Integer number;
    /**步骤编码*/
    @Schema(description = "步骤编码")
    private String stepCode;
    /**步骤名称*/
    @Schema(description = "步骤名称")
    private String stepName;
    /**步骤内容*/
    @Schema(description = "步骤内容")
    private String stepContent;
    /**pid*/
    @Schema(description = "pid")
    private String pid;
    /**作业结果*/
    @Schema(description = "作业结果")
    private String pdo;
    /**备注*/
    @Schema(description = "备注")
    private String remark;
}
