package com.cdkit.modules.plm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * BOM比较
 * <AUTHOR>
 * @date 2024/4/15
 */
@Getter
@AllArgsConstructor
public enum BomCompareDiffEnum {
    /**
     * 两个对象的属性不同
     */
    RED(1,"RED"),
    /**
     * 新增的对象
     */
    BLUE(2,"BLUE"),
    /**
     * 被删除的对象
     */
    GREEN(3,"GREEN");

    private final Integer code;
    private final String desc;
}
