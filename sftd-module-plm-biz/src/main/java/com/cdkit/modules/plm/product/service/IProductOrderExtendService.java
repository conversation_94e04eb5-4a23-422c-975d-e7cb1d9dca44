package com.cdkit.modules.plm.product.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.product.entity.ProductOrderExtend;
import com.cdkit.modules.plm.product.vo.req.ProductGeneralManualVO;
import com.cdkit.modules.plm.product.vo.req.ProductUploadVO;
import com.cdkit.modules.plm.product.vo.req.TechnicalRequirementsVO;
import com.cdkit.modules.plm.product.vo.resp.ProductExtendVO;

/**
 * @Description: product_order_extend
 * @Author: cdkit-boot
 * @Date:   2025-06-23
 * @Version: V1.0
 */
public interface IProductOrderExtendService extends IService<ProductOrderExtend> {

    void addTechnicalRequirements(TechnicalRequirementsVO technicalRequirementsVO);

    void addProductGeneralManual(ProductGeneralManualVO productGeneralManualVO);

    void addProductUpload(ProductUploadVO productUploadVO);

    ProductExtendVO queryByFormulaId(String id);
}
