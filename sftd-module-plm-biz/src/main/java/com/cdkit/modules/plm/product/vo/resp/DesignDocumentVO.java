package com.cdkit.modules.plm.product.vo.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import com.cdkit.common.aspect.annotation.Dict;
import com.cdkit.modules.plm.product.entity.ProductTree;

import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;

import jakarta.validation.constraints.Size;

/**
 * @Author：z<PERSON> yang
 * @name：DesignDocumentVO
 * @Date：2024/3/28 17:09
 */
@Data
@Schema(description = "设计文档vo")
public class DesignDocumentVO extends ProductTree {

    @Schema(description = "id")
    private String id;
    /**
     * 产品树id
     */
    @Excel(name = "产品树id", width = 15)
    @Schema(description = "产品树id")
    private String productTreeId;
    /**
     * 文件id，根据文件id再去filecenter的minio的文件
     */
    @Excel(name = "文件id，根据文件id再去filecenter的minio的文件", width = 15)
    @Schema(description = "文件id，根据文件id再去filecenter的minio的文件")
    private String filePath;
    /**
     * 文档分类，图纸/
     */
    @Excel(name = "文档分类，图纸/", width = 15)
    @Schema(description = "文档分类，图纸/")
    private String category;
    /**
     * 文档后缀类型，exb
     */
    @Excel(name = "文档后缀类型，exb", width = 15)
    @Schema(description = "文档后缀类型，exb")
    private String fileType;
    /**
     * 是否已出库
     */
    @Excel(name = "是否已出库", width = 15)
    @Schema(description = "是否已出库")
    private Integer outbound;
    /**
     * 是否已红批
     */
    @Excel(name = "是否已红批", width = 15)
    @Schema(description = "是否已红批")
    private Integer redBatch;
    /**
     * 是否已签名
     */
    @Excel(name = "是否已签名", width = 15)
    @Schema(description = "是否已签名")
    private Integer sign;
    /**
     * 文件大小
     */
    @Excel(name = "文件大小", width = 15)
    @Schema(description = "文件大小")
    private Integer fileSize;
    /**
     * 轻量化文件id
     */
    @TableField(value = "light_file_path")
    @Schema(description = "轻量化文件id")
    @Size(max = 255, message = "轻量化文件id最大长度要小于 255")
    private String lightFilePath;

    /**
     * 轻量化文件名称
     */
    @TableField(value = "light_file_name")
    @Schema(description = "轻量化文件名称")
    @Size(max = 255, message = "轻量化文件名称最大长度要小于 255")
    private String lightFileName;

    /**
     * 锁定人
     */
    @TableField(value = "locked_by")
    @Schema(description = "锁定人")
    @Size(max = 36, message = "锁定人最大长度要小于 36")
    private String lockedBy;

    @Schema(description = "cad docId")
    private String docId;

    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String approvalPerson;

    /**
     * 工作流绑定wiid
     */
    @Schema(description = "工作流绑定wiid")
    private String wiid;

    /**
     * 当前版本号
     */
    @TableField(value = "version")
    @Schema(description = "当前版本号")
    private String version;
}
