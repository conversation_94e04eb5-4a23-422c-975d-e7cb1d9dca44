package com.cdkit.modules.plm.product.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author：<PERSON><PERSON> ya<PERSON>
 * @name：QueryDesignDocumentVO
 * @Date：2024/4/9 09:32
 */
@Data
@Schema(description = "查询出库文档vo")
public class QueryDesignDocumentVO {
    @Schema(description = "搜索关键词")
    private String queryCondition;
    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    @Schema(description = "用户id，传了返回我的出库文档，没传返回所有出库文档")
    private String userId;
    @Schema(description = "页码")
    private Integer pageNo = 1;
    @Schema(description = "页容量")
    private Integer pageSize = 10;

}
