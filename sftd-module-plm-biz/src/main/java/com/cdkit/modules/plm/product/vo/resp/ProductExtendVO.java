package com.cdkit.modules.plm.product.vo.resp;

import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.vo.req.ProductGeneralManualVO;
import com.cdkit.modules.plm.product.vo.req.ProductUploadVO;
import com.cdkit.modules.plm.product.vo.req.TechnicalRequirementsVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author：mr
 * @name：ProductExtendVO
 * @Date：2024/3/27 14:18
 */
@Data
@Schema(description = "配方拓展信息")
public class ProductExtendVO extends ProductTree {
    private TechnicalRequirementsVO technicalRequirementsVO;
    private ProductGeneralManualVO productGeneralManualVO;
    private ProductUploadVO productUploadVO;
}
