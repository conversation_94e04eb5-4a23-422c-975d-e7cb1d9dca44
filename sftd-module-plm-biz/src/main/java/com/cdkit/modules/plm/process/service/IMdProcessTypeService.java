package com.cdkit.modules.plm.process.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.system.vo.SelectTreeModel;
import com.cdkit.modules.plm.process.entity.MdProcessType;

import java.util.List;

/**
 * @Description: 工序分类主数据
 * @Author: mc
 * @Date:   2024-07-26
 * @Version: V1.0
 */
public interface IMdProcessTypeService extends IService<MdProcessType> {

	/**根节点父ID的值*/
	public static final String ROOT_PID_VALUE = "0";

	/**树节点有子节点状态值*/
	public static final String HASCHILD = "1";

	/**树节点无子节点状态值*/
	public static final String NOCHILD = "0";

	/**
	 * 新增节点
	 *
	 * @param mdProcessType
	 */
	void addMdProcessType(MdProcessType mdProcessType);

	/**
   * 修改节点
   *
   * @param mdProcessType
   * @throws CdkitCloudException
   */
	void updateMdProcessType(MdProcessType mdProcessType) throws CdkitCloudException;

	/**
	 * 删除节点
	 *
	 * @param id
   * @throws CdkitCloudException
	 */
	void deleteMdProcessType(String id) throws CdkitCloudException;

	  /**
	   * 查询所有数据，无分页
	   *
	   * @param queryWrapper
	   * @return List<MdProcessType>
	   */
    List<MdProcessType> queryTreeListNoPage(QueryWrapper<MdProcessType> queryWrapper);

	/**
	 * 【vue3专用】根据父级编码加载分类字典的数据
	 *
	 * @param parentCode
	 * @return
	 */
	List<SelectTreeModel> queryListByCode(String parentCode);

	/**
	 * 【vue3专用】根据pid查询子节点集合
	 *
	 * @param pid
	 * @return
	 */
	List<SelectTreeModel> queryListByPid(String pid);

}
