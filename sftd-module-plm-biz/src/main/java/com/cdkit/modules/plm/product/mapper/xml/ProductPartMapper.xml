<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.plm.product.mapper.ProductPartMapper">
    <insert id="insertOrUpdate">
        insert into product_part(id, material_code, material_name, manufacture_type, part_type, handle_type, struct_type, weight, specs, version, assemble_quantity, assemble_sort, assemble_unit, standard_quantity, extend_product_id, extend_product_name)
        values
        (#{id}, #{materialCode}, #{materialName}, #{manufactureType}, #{partType}, #{handleType}, #{structType}, #{weight}, #{specs}, #{version}, #{assembleQuantity}, #{assembleSort}, #{assembleUnit}, #{standardQuantity}, #{extendProductId},#{extendProductName})

        on duplicate key update
        id = values(id),
        material_code = values(material_code),
        material_name = values(material_name),
        borrow_id = values(borrow_id),
        manufacture_type = values(manufacture_type),
        part_type = values(part_type),
        handle_type = values(handle_type),
        struct_type = values(struct_type),
        weight = values(weight),
        specs = values(specs),
        version = values(version),
        assemble_quantity = values(assemble_quantity),
        assemble_sort = values(assemble_sort),
        assemble_unit = values(assemble_unit),
        standard_quantity = values(standard_quantity),
        extend_product_id = values(extend_product_id),
        extend_product_name = values(extend_product_name)
    </insert>
    <select id="getBorrowInfo" resultMap="productPartMap">
        SELECT pp.id,
               pp.material_code,
               pp.material_name,
               pp.manufacture_type,
               pp.part_type,
               pp.handle_type,
               pp.struct_type,
               pp.weight,
               pp.specs,
               pp.version,
               pp.assemble_quantity,
               pp.assemble_sort,
               pp.assemble_unit,
               pp.extend_product_id,
               pp.extend_product_name,

               pt.name,
               pt.type,
               pt.code,
               pt.description,
               pt.belong_factory,
               pt.pid,
               pt.path,
               pt.source_id,
               pt.sort,
               pt.converted,
               pt.status,
               pt.node_type,
               pt.del_flag,
               pt.create_by,
               pt.create_time,
               pt.update_by,
               pt.update_time,
               pt.sys_org_code,
               pt.tenant_id
        FROM product_part pp,
             product_tree pt
        WHERE pp.id = pt.id
          and (pp.id = #{productPartId}
            or pt.source_id = #{productPartId}
            or pp.id = #{sourceId}
            )
        <if test="queryCondition != null and queryCondition != ''">
            and pt.name LIKE CONCAT('%', #{queryCondition}, '%')
        </if>
          AND pt.node_type = 1
          and pt.del_flag = 0
    </select>

    <!-- 定义结果映射 -->
    <resultMap id="productPartMap" type="com.cdkit.modules.plm.product.vo.resp.ProductPartVO">
        <result column="id" property="id"/>
        <result column="material_code" property="materialCode"/>
        <result column="material_name" property="materialName"/>
        <result column="manufacture_type" property="manufactureType"/>
        <result column="part_type" property="partType"/>
        <result column="handle_type" property="handleType"/>
        <result column="struct_type" property="structType"/>
        <result column="weight" property="weight"/>
        <result column="specs" property="specs"/>
        <result column="version" property="version"/>
        <result column="assemble_quantity" property="assembleQuantity"/>
        <result column="assemble_sort" property="assembleSort"/>
        <result column="assemble_unit" property="assembleUnit"/>
        <result column="extend_product_id" property="extendProductId"/>
        <result column="extend_product_name" property="extendProductName"/>

        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="code" property="code"/>
        <result column="description" property="description"/>
        <result column="belong_factory" property="belongFactory"/>
        <result column="pid" property="pid"/>
        <result column="path" property="path"/>
        <result column="source_id" property="sourceId"/>
        <result column="sort" property="sort"/>
        <result column="converted" property="converted"/>
        <result column="status" property="status"/>
        <result column="node_type" property="nodeType"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="sys_org_code" property="sysOrgCode"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
</mapper>
