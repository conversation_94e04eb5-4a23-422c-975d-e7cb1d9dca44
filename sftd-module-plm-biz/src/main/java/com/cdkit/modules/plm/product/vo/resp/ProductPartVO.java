package com.cdkit.modules.plm.product.vo.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import com.cdkit.modules.plm.product.entity.ProductPartExtend;
import com.cdkit.modules.plm.product.entity.ProductTree;

import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author：z<PERSON> yang
 * @name：ProductPartVO
 * @Date：2024/3/28 09:05
 */
@Data
@Schema(description = "总装/零部件")
public class ProductPartVO extends ProductTree {

    @Schema(description = "id，同product_tree的id，1:1关系")
    private String id;

    /**
     * 材料
     */
    @Excel(name = "材料", width = 15)
    @Schema(description = "材料")
    private String materialName;
    /**
     * 生产类型
     */
    @Excel(name = "生产类型", width = 15)
    @Schema(description = "生产类型")
    private String manufactureType;
    /**
     * 零件类型
     */
    @Excel(name = "零件类型", width = 15)
    @Schema(description = "零件类型")
    private String partType;
    /**
     * 处理类型
     */
    @Excel(name = "处理类型", width = 15)
    @Schema(description = "处理类型")
    private String handleType;
    /**
     * 结构类型
     */
    @Excel(name = "结构类型", width = 15)
    @Schema(description = "结构类型")
    private String structType;
    /**
     * 重量
     */
    @Excel(name = "重量", width = 15)
    @Schema(description = "重量")
    private String weight;
    /**
     * 规格
     */
    @Excel(name = "规格", width = 15)
    @Schema(description = "规格")
    private String specs;
    /**
     * 版本号，a、b、c、d增长
     */
    @Excel(name = "版本号，a、b、c、d增长", width = 15)
    @Schema(description = "版本号，a、b、c、d增长")
    private String version;
    /**
     * 装配数量
     */
    @Excel(name = "装配数量", width = 15)
    @Schema(description = "装配数量")
    private BigDecimal assembleQuantity;
    /**
     * 装配序号
     */
    @Excel(name = "装配序号", width = 15)
    @Schema(description = "装配序号")
    private Integer assembleSort;
    /**
     * 装配单位
     */
    @Excel(name = "装配单位", width = 15)
    @Schema(description = "装配单位")
    private String assembleUnit;

    /**
     * 标准用量
     */
    @TableField(value = "standard_quantity")
    @Schema(description = "标准用量")
    private BigDecimal standardQuantity;

    /**
     * 件次号继承来源产品id
     */
    @Schema(description = "件次号继承来源产品id")
    private String extendProductId;

    /**
     * 件次号继承来源产品name
     */
    @Schema(description = "件次号继承来源产品name")
    private String extendProductName;

    private List<ProductPartExtend> productPartExtendList;

    private List<ProductPartExtend> productPartCustomizedList;
}
