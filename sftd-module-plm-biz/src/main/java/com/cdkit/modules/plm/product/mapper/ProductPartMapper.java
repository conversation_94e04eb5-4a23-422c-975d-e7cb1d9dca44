package com.cdkit.modules.plm.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.modules.plm.product.entity.ProductPart;
import com.cdkit.modules.plm.product.vo.resp.ProductPartVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProductPartMapper extends BaseMapper<ProductPart> {
    Page<ProductPartVO> getBorrowInfo(IPage<ProductPartVO> page, @Param("productPartId") String productPartId,
                                      @Param("sourceId") String sourceId, @Param("queryCondition") String queryCondition);

    /**
     * 插入或更新
     *
     * @param productPart 零件信息
     */
    void insertOrUpdate(ProductPart productPart);
}
