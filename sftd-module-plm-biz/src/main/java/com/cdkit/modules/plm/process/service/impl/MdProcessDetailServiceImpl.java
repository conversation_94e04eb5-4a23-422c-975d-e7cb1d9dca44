package com.cdkit.modules.plm.process.service.impl;

import com.cdkit.modules.plm.process.entity.MdProcessDetail;
import com.cdkit.modules.plm.process.mapper.MdProcessDetailMapper;
import com.cdkit.modules.plm.process.service.IMdProcessDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 工序定义明细
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Service
public class MdProcessDetailServiceImpl extends ServiceImpl<MdProcessDetailMapper, MdProcessDetail> implements IMdProcessDetailService {

	@Autowired
	private MdProcessDetailMapper mdProcessDetailMapper;

	@Override
	public List<MdProcessDetail> selectByMainId(String mainId) {
		return mdProcessDetailMapper.selectByMainId(mainId);
	}
}
