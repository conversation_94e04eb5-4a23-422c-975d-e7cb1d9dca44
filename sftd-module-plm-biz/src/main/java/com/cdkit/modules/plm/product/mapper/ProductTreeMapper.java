package com.cdkit.modules.plm.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.ProductTree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductTreeMapper extends BaseMapper<ProductTree> {
    /**
     * 插入或更新
     *
     * @param productTree 产品树信息
     */
    void insertOrUpdate(ProductTree productTree);

    /**
     * 删除某节点下的图纸
     * @param pid 节点ID
     * @param type 类型
     * @param nodeType 节点类型
     */
    void deleteDesignDocument(@Param("pid") String pid, @Param("type") Integer type, @Param("nodeType") Integer nodeType);

    /**
     * 查询零件及总装数量
     * @param type 类型
     * @return 数量
     */
    Long countByTypeAndCode(@Param("type") Integer type);

    /**
     * 查询树
     * @param nodeType 节点类型
     * @param pid 父节点ID
     * @return 树
     */
    List<ProductTreeDTO> selectTreeList(@Param("nodeType") Integer nodeType, @Param("pid") String pid,@Param("materialCodeOrName")String materialCodeOrName);

    /**
     * 查询全部树
     * @param nodeType 节点类型
     * @return 树
     */
    List<ProductTreeDTO> selectTreeListAll(
            @Param("type1") Integer type1, @Param("type2") Integer type2, @Param("type3") Integer type3, @Param("type4") Integer type4,
            @Param("nodeType") Integer nodeType, @Param("materialCodeOrName")String materialCodeOrName,@Param("rootIdsSize")boolean rootIdsSize);

    /**
     * 查询全部树
     * @param nodeType 节点类型
     * @return 树
     */
    List<ProductTreeDTO> selectTreeListByIds(
            @Param("type1") Integer type1, @Param("type2") Integer type2, @Param("type3") Integer type3, @Param("type4") Integer type4,
            @Param("ids") String ids,
            @Param("nodeType") Integer nodeType, @Param("materialCodeOrName")String materialCodeOrName,@Param("rootIdsSize")boolean rootIdsSize);

}
