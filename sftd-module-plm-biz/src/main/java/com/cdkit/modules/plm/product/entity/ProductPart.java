package com.cdkit.modules.plm.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import com.cdkit.common.aspect.annotation.Dict;

/**
 * 产品总装/零部件
 */
@Schema(description = "产品总装/零部件")
@Data
@TableName(value = "product_part")
public class ProductPart implements Serializable {
    /**
     * id，同product_tree的id，1:1关系
     */
    @TableField(value = "id")
    @Schema(description = "id，同product_tree的id，1:1关系")
    @Size(max = 36, message = "id，同product_tree的id，1:1关系最大长度要小于 36")
    @NotBlank(message = "id，同product_tree的id，1:1关系不能为空")
    private String id;

    /**
     * 物料编码
     */
    @TableField(value = "material_code")
    @Schema(description = "物料编码")
    @Size(max = 255, message = "物料编码最大长度要小于 255")
    private String materialCode;

    /**
     * 材料
     */
    @TableField(value = "material_name")
    @Schema(description = "材料")
    @Size(max = 255, message = "材料最大长度要小于 255")
    private String materialName;

    /**
     * 借用ID
     */
    @TableField(value = "borrow_id")
    @Schema(description = "借用ID")
    @Size(max = 255, message = "借用ID最大长度要小于 255")
    private String borrowId;

    /**
     * 生产类型
     */
    @TableField(value = "manufacture_type")
    @Schema(description = "生产类型")
    @Size(max = 50, message = "生产类型最大长度要小于 50")
    private String manufactureType;

    /**
     * 零件类型
     */
    @TableField(value = "part_type")
    @Schema(description = "零件类型")
    @Size(max = 50, message = "零件类型最大长度要小于 50")
    private String partType;

    /**
     * 处理类型
     */
    @TableField(value = "handle_type")
    @Schema(description = "处理类型")
    @Size(max = 50, message = "处理类型最大长度要小于 50")
    private String handleType;

    /**
     * 结构类型
     */
    @TableField(value = "struct_type")
    @Schema(description = "结构类型")
    @Size(max = 50, message = "结构类型最大长度要小于 50")
    private String structType;

    /**
     * 重量
     */
    @TableField(value = "weight")
    @Schema(description = "重量")
    @Size(max = 50, message = "重量最大长度要小于 50")
    private String weight;

    /**
     * 规格
     */
    @TableField(value = "specs")
    @Schema(description = "规格")
    @Size(max = 100, message = "规格最大长度要小于 100")
    private String specs;

    /**
     * 版本号，1、2、3增长
     */
    @TableField(value = "version")
    @Schema(description = "版本号，1、2、3增长")
    private Integer version;

    /**
     * 装配数量
     */
    @TableField(value = "assemble_quantity")
    @Schema(description = "装配数量")
    private BigDecimal assembleQuantity;

    /**
     * 装配序号
     */
    @TableField(value = "assemble_sort")
    @Schema(description = "装配序号")
    private Integer assembleSort;

    /**
     * 装配单位
     */
    @TableField(value = "assemble_unit")
    @Schema(description = "装配单位")
    @Size(max = 50, message = "装配单位最大长度要小于 50")
    private String assembleUnit;

    /**
     * 标准用量
     */
    @TableField(value = "standard_quantity")
    @Schema(description = "标准用量")
    private BigDecimal standardQuantity;

    /**
     * 成品总损耗比例
     */
    @TableField(value = "loss_ratio")
    @Schema(description = "成品总损耗比例")
    private BigDecimal lossRatio;

    /**
     * 件次号继承来源产品id
     */
    @TableField(value = "extend_product_id")
    @Schema(description = "件次号继承来源产品id")
    private String extendProductId;

    /**
     * 件次号继承来源产品name
     */
    @TableField(value = "extend_product_name")
    @Schema(description = "件次号继承来源产品name")
    private String extendProductName;

    private static final long serialVersionUID = 1L;

    // 绑定的生产订单号
    private String bindingProductOrderNum;


    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    @Schema(description = "租户号")
    @Size(max = 32, message = "租户号最大长度要小于 32")

    private Integer tenantId;

    @TableField(value = "fungible_material_codes")
    @Schema(description = "可替代物")
    private String fungibleMaterialCodes;


    @TableField(value = "main_materials_flag")
    @Schema(description = "是否主材")
    @Dict(dicCode="main_materials_flag")
    private Integer mainMaterialsFlag;

    @Schema(description = "执行标准")
    private String executionStandards;

    @Schema(description = "制版条件")
    private String plateMakingConditions;

    @Schema(description = "工序指导")
    private String processRemark;

}
