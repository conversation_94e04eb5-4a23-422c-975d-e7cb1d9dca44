<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.plm.process.mapper.ProductProcessDetailMapper">

    <delete id="removeByProcessId">
        update product_process_detail set is_new = 0 where process_id = #{processId} and product_id = #{productId}
    </delete>
    <select id="selectByProcessId" resultType="com.cdkit.modules.plm.process.entity.ProductProcessDetail">
        SELECT *
		FROM  product_process_detail
		WHERE
			 process_id = #{processId}
		and is_new = 1 order by number asc
    </select>
</mapper>
