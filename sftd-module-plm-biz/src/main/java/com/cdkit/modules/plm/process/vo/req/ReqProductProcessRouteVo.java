package com.cdkit.modules.plm.process.vo.req;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;

/**
 * <AUTHOR>
 * @date 2024/8/5
 */
@Data
public class ReqProductProcessRouteVo {
    /**通过对应节点导入工艺路线*/
    @Excel(name = "通过对应节点导入工艺路线", width = 15)
    @Schema(description = "通过对应节点导入工艺路线")
    private String currProductId;
    /**工艺路线编码*/
    @Excel(name = "工艺路线编码", width = 15)
    @Schema(description = "工艺路线编码")
    private String processRouteId;
}
