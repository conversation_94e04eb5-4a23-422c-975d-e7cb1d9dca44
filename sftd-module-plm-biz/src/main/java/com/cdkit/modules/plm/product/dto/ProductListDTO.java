package com.cdkit.modules.plm.product.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/2
 */
@Data
public class ProductListDTO {
    /**
     * 主键ID
     */
    private String id;
    /**
     * 名称
     */
    private String name;

    /**
     * 编码/代号
     */
    private String code;

    /**
     * 规格
     */
    private String specs;
    /**
     * 重量
     */
    private String weight;

    /**
     * 生产类型
     */
    private String manufactureType;
    /**
     * 处理类型
     */
    private String handleType;
    /**
     * 零件类型
     */
    private String partType;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 材料
     */
    private String materialName;
    /**
     * 结构类型
     */
    private String structType;
    /**
     * 装配数量
     */
    private BigDecimal assembleQuantity;
    /**
     * 装配序号
     */
    private Integer assembleSort;
    /**
     * 装配单位
     */
    private String assembleUnit;

    /**
     * 序号
     */
    private String sort;
    /**
     * 层级号
     */
    private Integer level;

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        ProductListDTO dto = (ProductListDTO) o;
        return Objects.equals(name, dto.name) &&
                Objects.equals(code, dto.code) &&
                Objects.equals(specs, dto.specs) &&
                Objects.equals(weight, dto.weight) &&
                Objects.equals(manufactureType, dto.manufactureType) &&
                Objects.equals(handleType, dto.handleType) &&
                Objects.equals(partType, dto.partType) &&
                Objects.equals(materialCode, dto.materialCode) &&
                Objects.equals(materialName, dto.materialName) &&
                Objects.equals(structType, dto.structType) &&
                Objects.equals(assembleQuantity, dto.assembleQuantity) &&
                Objects.equals(assembleSort, dto.assembleSort) &&
                Objects.equals(assembleUnit, dto.assembleUnit);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, code, specs, weight, manufactureType, handleType, partType, materialCode, materialName, structType, assembleQuantity, assembleSort, assembleUnit);
    }
}
