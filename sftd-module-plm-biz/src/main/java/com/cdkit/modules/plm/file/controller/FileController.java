package com.cdkit.modules.plm.file.controller;

import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.util.MinioUtil;
import com.cdkit.modules.plm.product.thirdparty.zwcad.CadFeign;
import com.cdkit.modules.plm.product.thirdparty.zwcad.dto.CadResultDTO;
import com.cdkit.modules.plm.product.thirdparty.zwcad.dto.UploadRespDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * @Author：zhao yang
 * @name：FileController
 * @Date：2024/4/15 08:32
 */
@Tag(name = "文件管理")
@RestController
@RequestMapping("file")
@Slf4j
@AllArgsConstructor
public class FileController {
    @GetMapping("shareableLink")
    @Operation(summary = "获取文件的可分享链接",description = "获取文件的可分享链接")
    public Result<String> shareableLink(@RequestParam String filePath) {
        String shareableLink = MinioUtil.getObjectUrl(MinioUtil.getBucketName(), filePath.substring(1), (int) TimeUnit.DAYS.toSeconds(1));
        Result<String> ret = Result.OK(shareableLink);
        ret.setMessage(null);
        return ret;
    }

    @GetMapping("view")
    @Operation(summary = "文件预览",description = "文件预览")
    public void view(HttpServletResponse response, @RequestParam String filePath) throws IOException {
        InputStream inputStream = MinioUtil.getMinioFile(MinioUtil.getBucketName(), filePath);
        String filename = StringUtils.getFilename(filePath);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType(MediaTypeFactory.getMediaType(filename).orElse(MediaType.APPLICATION_OCTET_STREAM).toString());
            IOUtils.copy(inputStream, outputStream);
        }
    }

    @GetMapping("download")
    @Operation(summary = "文件下载",description = "文件下载")
    public void download(HttpServletResponse response, @RequestParam String filePath) throws IOException {
        InputStream inputStream = MinioUtil.getMinioFile(MinioUtil.getBucketName(), filePath);
        String filename = StringUtils.getFilename(filePath);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType(MediaTypeFactory.getMediaType(filename).orElse(MediaType.APPLICATION_OCTET_STREAM).toString());
            String fileName = URLEncoder.encode(filename, StandardCharsets.UTF_8.name());
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            IOUtils.copy(inputStream, outputStream);
        }
    }

    @Autowired
    CadFeign cadFeign;

    @PostMapping("upload")
    @Operation(summary = "测试上传",description = "测试上传")
    public void upload(@RequestPart("file") MultipartFile file) {
        CadResultDTO<UploadRespDTO> ret = cadFeign.upload("0", file);
        System.out.println(JSONUtil.toJsonPrettyStr(ret));
    }
}
