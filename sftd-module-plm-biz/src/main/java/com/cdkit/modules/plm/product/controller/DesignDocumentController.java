package com.cdkit.modules.plm.product.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.aspect.annotation.AutoLog;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.product.entity.DesignDocument;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.CadHandleService;
import com.cdkit.modules.plm.product.service.IDesignDocumentService;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.*;
import com.cdkit.modules.plm.product.vo.resp.DesignDocumentAuditVO;
import com.cdkit.modules.plm.product.vo.resp.DesignDocumentVO;
import com.cdkit.modules.plm.product.vo.resp.DocumentBorrowInfoVO;
import com.cdkit.modules.plm.product.vo.resp.OutboundDocumentVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Description: product_tree
 * @Author: zhao yang
 * @Date: 2024-03-26
 * @Version: V1.0
 */
@Tag(name = "设计bom-产品图纸")
@RestController
@RequestMapping("designDocument")
@Slf4j
public class DesignDocumentController extends CdkitController<ProductTree, IProductTreeService> {

    @Resource
    @Lazy
    private IProductTreeService productTreeService;
    @Resource
    private IDesignDocumentService designDocumentService;

    @PostMapping(value = "importBatch")
    @Operation(summary = "图纸批量倒入", description = "图纸批量倒入，后台调用sdk解析图纸信息写入数据库")
    public Result<String> importBatch(@RequestParam(name = "pid") String pid,
                                      @RequestPart(name = "files") MultipartFile[] files) throws Exception {
        productTreeService.importBatch(pid, files);
        return Result.OK("操作成功！");
    }

    @PostMapping(value = "importDocument")
    @Operation(summary = "文档上传", description = "文档上传")
    public Result<String> importDocument(@RequestParam(name = "pid") String pid, @RequestPart(name = "file") MultipartFile file) throws Exception {
        productTreeService.importDocument(pid, file, NodeTypeEnum.PRODUCT_TREE.getCode());
        return Result.OK("操作成功！");
    }


    /**
     * 通过id删除产品图纸
     *
     * @param id
     * @return #username删除了图纸，#getProductName(#id)
     */
    @AutoLog(value = "通过id删除产品图纸")
    @Operation(summary = "通过id删除产品图纸", description = "通过id删除产品图纸")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id, @RequestParam(name = "cascade", required = false) boolean cascade) {
        productTreeService.cascadeDeleteNode(id, cascade);
        return Result.OK("删除成功!");
    }

    /**
     * 通过id查询产品图纸
     *
     * @param id
     * @return
     */
    @AutoLog(value = "通过id查询产品图纸")
    @Operation(summary = "通过id查询产品图纸", description = "通过id查询产品图纸")
    @GetMapping(value = "queryById")
    public Result<DesignDocumentVO> queryById(@RequestParam(name = "id") String id) {
        DesignDocumentVO ret = productTreeService.queryDesignDocumentById(id);
        return Result.OK(ret);
    }

    @Operation(summary = "图纸发布、取消发布、重发布、提交并签名")
    @PostMapping(value = "publish")
    public Result<String> publish(@RequestBody @Validated DesignDocumentPublishVO designDocumentPublishVO, HttpServletRequest request) {
        productTreeService.designDocumentPublish(designDocumentPublishVO, request);
        return Result.OK("操作成功");
    }

    @Operation(summary = "图纸出库或取消出库", description = "id:图纸id cancel：true代表取消出库")
    @PostMapping(value = "outbound")
    public Result<String> outbound(@RequestBody OutboundReqVO outboundReqVO) {
        productTreeService.designDocumentOutbound(outboundReqVO.getId(), outboundReqVO.isCancel());
        return Result.OK("操作成功");
    }

    @Operation(summary = "图纸入库", description = "只有出库状态的图纸可以进行入库")
    @PostMapping(value = "inbound")
    public Result<String> inbound(@RequestParam(name = "id") String id, @RequestPart MultipartFile file) throws Exception {
        productTreeService.designDocumentInbound(id, file);
        return Result.OK("操作成功");
    }

    @GetMapping("listAllDesignDocumentByPid")
    @Operation(summary = "获取父节点下的图纸列表", description = "获取父节点下的图纸列表")
    public Result<Page<DesignDocumentVO>> listAllDesignDocumentByPid(
            @RequestParam String pid,
            @RequestParam(name = "queryCondition", required = false) String queryCondition,
            @RequestParam(name = "category", required = false) String category,
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<DesignDocumentVO> designDocumentVOS = productTreeService.listAllDesignDocumentByPid(pid, queryCondition, category, pageNo, pageSize, NodeTypeEnum.PRODUCT_TREE.getCode());
        return Result.OK(designDocumentVOS);
    }

    @GetMapping("outboundDocumentList")
    @Operation(summary = "出库文档列表")
    public Result<Page<OutboundDocumentVO>> outboundDocumentList(QueryDesignDocumentVO queryDesignDocumentVO) {
        Page<OutboundDocumentVO> outboundDocumentVOS = productTreeService.outboundDocumentList(queryDesignDocumentVO);
        return Result.OK(outboundDocumentVOS);
    }

    @Resource
    CadHandleService cadHandleService;

    @GetMapping("pdfView")
    @Operation(summary = "pdf预览", description = "预览图纸")
    public void pdfView(HttpServletResponse response, @RequestParam(name = "id") String id) throws IOException {
        MultipartFile multipartFile = cadHandleService.transPdf(id);
        InputStream inputStream = multipartFile.getInputStream();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType(MediaTypeFactory.getMediaType(multipartFile.getOriginalFilename()).orElse(MediaType.APPLICATION_OCTET_STREAM).toString());
            IOUtils.copy(inputStream, outputStream);
        }
    }

    @GetMapping("documentView")
    @Operation(summary = "文件预览", description = "文件预览")
    public void documentView(HttpServletResponse response, @RequestParam(name = "id") String id) throws IOException {
         cadHandleService.documentView(id, response);
    }

    /**
     * todo 增加图纸的轻量化文件导出
     *
     * @param response
     * @param pid
     * @param ids
     * @throws IOException
     */
    @GetMapping("export")
    @Operation(summary = "文档批量导出", description = "数据导出当前节点下直属的图纸文件和轻量化文件zip包，pid：父id， ids：页面导出特定id的文件列表")
    public void export(HttpServletResponse response, @RequestParam(name = "pid", required = false) String pid, @RequestParam(name = "ids", required = false) String ids) throws IOException {
        List<DesignDocumentVO> designDocumentVOS;
        if (StrUtil.isNotBlank(pid)) {
            designDocumentVOS = productTreeService.listAllDesignDocumentByPid(pid, null, "", 1, 10000, NodeTypeEnum.PRODUCT_TREE.getCode()).getRecords();
            if (StrUtil.isNotBlank(ids)) {
                designDocumentVOS = designDocumentVOS.stream().filter(x -> ids.contains(x.getId())).collect(Collectors.toList());
            }
        } else {
            if (StrUtil.isEmpty(ids)) {
                throw new CdkitCloudException("参数异常");
            }
            List<String> idList = Arrays.asList(ids.split(","));
            List<ProductTree> productTrees = productTreeService.listByIds(idList);
            List<DesignDocument> designDocuments = designDocumentService.listByIds(idList);
            if (CollUtil.isEmpty(productTrees) || CollUtil.isEmpty(designDocuments)) {
                throw new CdkitCloudException("数据异常");
            }
            designDocumentVOS = productTrees.stream().map(x -> {
                DesignDocumentVO designDocumentVO = BeanUtil.copyProperties(x, DesignDocumentVO.class);
                designDocuments.stream()
                        .filter(t -> x.getId().equals(t.getId()))
                        .findFirst().ifPresent(t -> BeanUtil.copyProperties(t, designDocumentVO));
                return designDocumentVO;
            }).collect(Collectors.toList());
        }
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            for (DesignDocumentVO designDocumentVO : designDocumentVOS) {
                //InputStream inputStream = MinioUtil.getMinioFile(MinioUtil.getBucketName(), designDocumentVO.getFilePath());
                MultipartFile dwgFile = cadHandleService.getDwgFile(designDocumentVO.getId());
                InputStream inputStream = dwgFile.getInputStream();
                if (designDocumentVOS.size() > 1) {
                    ZipEntry zipEntry = new ZipEntry(StringUtils.getFilename(designDocumentVO.getFilePath()));
                    zipOutputStream.putNextEntry(zipEntry);
                    StreamUtils.copy(inputStream, zipOutputStream);
                    zipOutputStream.flush();
                } else {
                    try (ServletOutputStream outputStream = response.getOutputStream()) {
                        //单个下载
                        response.setContentType(MediaTypeFactory.getMediaType(designDocumentVO.getName()).orElse(MediaType.APPLICATION_OCTET_STREAM).toString());
                        String fileName = URLEncoder.encode(designDocumentVO.getName(), StandardCharsets.UTF_8.name());
                        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
                        IOUtils.copy(inputStream, outputStream);
                        return;
                    }
                }
            }
            zipOutputStream.closeEntry();
        }
    }

    @PostMapping("addBorrowDesignDocument")
    @Operation(summary = "新增借用图纸", description = "新增借用图纸")
    public Result<String> addBorrowDesignDocument(@RequestBody @Validated AddBorrowDocumentVO addBorrowDocumentVO) {
        productTreeService.addBorrowDesignDocument(addBorrowDocumentVO);
        return Result.OK("新增成功");
    }

    /**
     * 置为原图纸
     */
    @PostMapping("toOrigin")
    @Operation(summary = "置为原图纸", description = "置为原图纸")
    public Result<String> toOrigin(@RequestBody ToOriginVO toOriginVO) {
        productTreeService.toDocumentOrigin(toOriginVO.getTargetId());
        return Result.OK("操作成功");
    }

    /**
     * 获取图纸的借用信息
     *
     * @param designDocumentId
     * @return
     */
    @GetMapping("getDocumentBorrowInfoById")
    @Operation(summary = "获取图纸的借用信息", description = "获取图纸的借用信息，id：可能是源图纸id也可能是目标图纸id")
    public Result<List<DocumentBorrowInfoVO>> getDocumentBorrowInfoById(@RequestParam String designDocumentId) {
        List<DocumentBorrowInfoVO> ret = productTreeService.getDocumentBorrowInfoById(designDocumentId);
        return Result.OK(ret);
    }

    /**
     * 提交审核
     */
    @PostMapping("auditDocument")
    @Operation(summary = "图纸审核", description = "图纸审核")
    public Result<String> auditDocument (@RequestBody @Validated DesignDocumentAuditVO designDocumentAuditVO, HttpServletRequest request) {
        DesignDocument designDocument = new DesignDocument();
        BeanUtil.copyProperties(designDocumentAuditVO,designDocument);
        String ret = productTreeService.auditDocument(designDocument,request);
        //判断自动签字 自动发布
        if( null!= designDocumentAuditVO.getWriteSign() && "true".equals(designDocumentAuditVO.getWriteSign())){
            productTreeService.writeSign(designDocument,request);
        }
        //自动发布
        return Result.OK(ret);
    }

    /**
     * 自动签名
     */
    @PostMapping("writeSign")
    @Operation(summary = "自动签名", description = "自动签名")
    public Result<String> writeSign (@RequestBody @Validated DesignDocument designDocument, HttpServletRequest request) {
        String ret = productTreeService.writeSign(designDocument,request);
        return Result.OK(ret);
    }

    /**
     * 审核驳回
     */
    @PostMapping("reject")
    @Operation(summary = "审核驳回", description = "审核驳回")
    public Result<List<DocumentBorrowInfoVO>> reject (@RequestBody @Validated DesignDocument designDocument, HttpServletRequest request) {
        String ret = productTreeService.reject(designDocument,request);
        return Result.OK(ret);
    }

    /**
     * 复用图纸
     */
    @GetMapping("reuseDocument")
    @Operation(summary = "复用图纸", description = "复用图纸")
    public Result<List<DocumentBorrowInfoVO>> reuseDocument (@RequestParam(name = "pid") String pid, @RequestParam(name = "designDocumentHistoryId") String designDocumentHistoryId) {
        String ret = productTreeService.reuseDocument(pid,designDocumentHistoryId);
        return Result.OK(ret);
    }


}
