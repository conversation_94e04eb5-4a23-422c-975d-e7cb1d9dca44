package com.cdkit.modules.plm.product.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.system.api.ISysBaseAPI;
import com.cdkit.common.system.base.controller.CdkitController;
import com.cdkit.common.system.vo.DictModel;
import com.cdkit.modules.plm.enums.NodeTypeEnum;
import com.cdkit.modules.plm.product.dto.ProductTreeDTO;
import com.cdkit.modules.plm.product.entity.ProductTree;
import com.cdkit.modules.plm.product.service.IProductTreeService;
import com.cdkit.modules.plm.product.vo.req.CancelBorrowReqVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: product_tree
 * @Author: zhao yang
 * @Date: 2024-03-26
 * @Version: V1.0
 */
@Tag(name = "设计bom-产品树")
@RestController
@RequestMapping("productTree")
@Slf4j
@AllArgsConstructor
public class ProductTreeController extends CdkitController<ProductTree, IProductTreeService> {

    private final IProductTreeService productTreeService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Operation(summary = "产品树形，可用于产品bom", description = "根据根节点id查询树形")
    @GetMapping(value = "/tree")
    public Result<List<ProductTreeDTO>> tree(@RequestParam(required = false) List<String> rootIds,@RequestParam(required = false) String materialCodeOrName) {
        List<ProductTreeDTO> productTreeDTOS = productTreeService.tree(rootIds, NodeTypeEnum.PRODUCT_TREE,materialCodeOrName);
        return Result.OK(productTreeDTOS);
    }
    /**
     * 查询工艺树
     *
     * @param rootIds 节点ID
     * @return 返回结果
     */
    @Operation(summary = "工艺树", description = "根据根节点id查询树形，不传则查询全部,如果展示产品bom则前端过滤掉文件节点")
    @GetMapping(value = "/treeLazy")
    public Result<List<ProductTreeDTO>> treeLazy(@RequestParam(required = false) String rootIds,@RequestParam(required = false) String materialCodeOrName) {
        List<ProductTreeDTO> dto = productTreeService.treeLazy(rootIds, NodeTypeEnum.PRODUCT_TREE,materialCodeOrName);
        List<DictModel> materialItemType = sysBaseAPI.getDictItems("material_item_type");
        List<DictModel> materialsOpType = sysBaseAPI.getDictItems("materials_op_type");
        Map<String, String> materialItemMap = materialItemType.stream().collect(
                Collectors.toMap(DictModel::getValue, DictModel::getText));
        Map<String, String> materialsOpMap = materialsOpType.stream().collect(
                Collectors.toMap(DictModel::getValue, DictModel::getText));
        for (ProductTreeDTO productTreeDTO : dto) {
            productTreeDTO.setPartTypeName(materialItemMap.get(productTreeDTO.getPartType()));
            productTreeDTO.setManufactureTypeName(materialsOpMap.get(productTreeDTO.getManufactureType()));
        }
        return Result.OK(dto);
    }

    @Operation(summary = "产品树形，可用于产品bom", description = "根据根节点id查询树形")
    @GetMapping(value = "/treeEx")
    public Result<List<ProductTreeDTO>> treeEx(@RequestParam(required = false) List<String> rootIds,@RequestParam(required = false) String materialCodeOrName) {
        List<ProductTreeDTO> productTreeDTOS = productTreeService.tree(rootIds, NodeTypeEnum.PROCESS_TREE,materialCodeOrName);

        return Result.OK(productTreeDTOS);
    }


    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @Operation(summary = "批量删除", description = "批量删除节点，全类型通用")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        for (String id : ids.split(",")) {
            productTreeService.cascadeDeleteNode(id, false);
        }
        return Result.OK("删除成功!");
    }

    @PostMapping("cancelBorrow")
    @Operation(summary = "取消借用", description = "取消借用（通用接口）")
    public Result<String> cancelBorrow(@RequestBody CancelBorrowReqVO cancelBorrowReqVO) {
        productTreeService.cancelBorrow(cancelBorrowReqVO.getId());
        return Result.OK("操作成功");
    }

    @Operation(summary = "修改并导入工艺bom")
    @GetMapping("/copy/producttree")
    public Result copyProductTree(String id, String productOrderNum) {
        Result<Object> ok = Result.OK(productTreeService.copyTree(id, productOrderNum));
        ok.setMessage("");
        return ok;
    }

    @Operation(summary = "修改并导入工艺bom")
    @GetMapping("/copy/producttreeEx")
    public Result copyProductTreeEx(String id) {
        Result<Object> ok = Result.OK(productTreeService.copyTreeEx(id));
        ok.setMessage("");
        return ok;
    }

    @Operation(summary = "修改并导入工艺bom")
    @GetMapping("/treeList")
    public Result getTreeList(@RequestParam("productId") String productId) {
        return Result.OK(productTreeService.getTreeList(productId));
    }
}
