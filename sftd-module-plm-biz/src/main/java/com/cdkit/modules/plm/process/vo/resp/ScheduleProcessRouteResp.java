package com.cdkit.modules.plm.process.vo.resp;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 智能排程工艺路径接口返回值实体类
 * <AUTHOR>
 * @Date 2024年11月8日
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleProcessRouteResp {

    @Schema(description = "路径编码")
    private String routingCode;
    @Schema(description = "物料编码")
    private String productCode;
    @Schema(description = "路径名称")
    private String routingName;
    @Schema(description = "路径步骤编码")
    private String routingStepCode;
    @Schema(description = "路径步骤名称")
    private String routingStepName;
    @Schema(description = "制造时间")
    private String processDuration;
    @Schema(description = "生效时间")
    private String effectiveTime;
    @Schema(description = "失效时间")
    private String expiryTime;
    @Schema(description = "资源组编码")
    private String resourceGroupCode;
}
