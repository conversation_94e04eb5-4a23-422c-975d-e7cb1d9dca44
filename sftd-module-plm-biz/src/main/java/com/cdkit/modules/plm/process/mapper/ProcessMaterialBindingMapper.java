package com.cdkit.modules.plm.process.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.plm.process.entity.ProcessMaterialBinding;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 工序与投入物绑定
 * @Author: cdkit-boot
 * @Date:   2025-08-11
 * @Version: V1.0
 */
public interface ProcessMaterialBindingMapper extends BaseMapper<ProcessMaterialBinding> {
    /**
     * 删除工序绑定的物料
     * @param productFormulationId 配方ID
     * @param processId 工序ID
     */
    void deleteByFormulationIdAndProcessId(@Param("productFormulationId") String productFormulationId,
                                           @Param("processId") String processId);
}
