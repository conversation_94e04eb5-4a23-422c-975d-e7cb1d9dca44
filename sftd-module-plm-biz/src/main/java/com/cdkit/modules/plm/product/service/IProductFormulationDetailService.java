package com.cdkit.modules.plm.product.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.plm.product.entity.ProductFormulationDetail;

import java.util.List;

/**
 * @Description: product_formulation_detail
 * @Author: cdkit-boot
 * @Date:   2025-06-04
 * @Version: V1.0
 */
public interface IProductFormulationDetailService extends IService<ProductFormulationDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<ProductFormulationDetail>
	 */
	public List<ProductFormulationDetail> selectByMainId(String mainId);

	/**
	 * 根据工序查询物料绑定信息
	 *
	 * @param processId 工序ID
	 * @param productId 产成品ID(bomId)
	 * @return 物料组成ID
	 */
	List<String> processProductList(String processId, String productId);

	/**
	 * 根据工序查询绑定的产出物
	 *
	 * @param processId 工序ID
	 * @param productId 产成品ID(bomId)
	 * @return 产出物ID
	 */
	String processOutputProduct(String processId, String productId);
}
