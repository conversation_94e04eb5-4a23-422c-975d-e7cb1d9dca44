package com.cdkit.modules.plm.process.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.plm.process.entity.ProcessCard;
import com.cdkit.modules.plm.process.entity.ProcessTemplate;
import com.cdkit.modules.plm.process.entity.ProcessType;
import com.cdkit.modules.plm.process.mapper.ProcessCardMapper;
import com.cdkit.modules.plm.process.mapper.ProcessTemplateMapper;
import com.cdkit.modules.plm.process.mapper.ProcessTypeMapper;
import com.cdkit.modules.plm.process.service.IProcessTemplateService;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTemplateAddVO;
import com.cdkit.modules.plm.process.vo.req.ReqProcessTemplateEditVO;
import com.cdkit.modules.plm.process.vo.resp.RespProcessTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 工艺模板业务实现
 * @Author: mc
 * @Date:   2024-04-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class ProcessTemplateServiceImpl extends ServiceImpl<ProcessTemplateMapper, ProcessTemplate> implements IProcessTemplateService {
    @Resource
    private ProcessTypeMapper processTypeMapper;
    @Resource
    private ProcessTemplateMapper processTemplateMapper;
    @Resource
    private ProcessCardMapper processCardMapper;

    /**
     * 按工艺类型查询工艺模板
     *
     * @return 返回结果
     */
    @Override
    public List<RespProcessTemplateVO> listGroupByType() {
        List<RespProcessTemplateVO> list = new ArrayList<>();
        List<ProcessType> processTypes = processTypeMapper.selectList(null);
        for (ProcessType item : processTypes) {
            RespProcessTemplateVO vo = new RespProcessTemplateVO();
            vo.setId(item.getId());
            vo.setName(item.getName());
            List<ProcessTemplate> processTemplates = processTemplateMapper.selectList(new LambdaQueryWrapper<ProcessTemplate>().eq(ProcessTemplate::getProcessTypeId, item.getId()));
            vo.setProcessTemplateList(processTemplates);
            list.add(vo);
        }
        return list;
    }

    /**
     * 根据ID删除工艺模板
     *
     * @param id 模板ID
     */
    @Override
    public void deleteById(String id) {
        log.info("删除工艺模板ID:{}", id);
        Long count = processCardMapper.selectCount(new LambdaQueryWrapper<ProcessCard>().eq(ProcessCard::getTemplateId, id));
        if (count > 0L) {
            throw new CdkitCloudException("此工艺模板下有工艺卡片，禁止删除");
        }
        this.removeById(id);
    }

    /**
     * 批量删除工艺模板
     *
     * @param ids 主键ID
     */
    @Override
    public void deleteByIds(List<String> ids) {
        log.info("批量删除工艺模板ID:{}", JSON.toJSONString(ids));
        for (String id : ids) {
            Long count = processCardMapper.selectCount(new LambdaQueryWrapper<ProcessCard>().eq(ProcessCard::getTemplateId, id));
            if (count > 0L) {
                ProcessTemplate processTemplate = this.getById(id);
                throw new CdkitCloudException("工艺模板【" + processTemplate.getTemplateName() + "】下有工艺卡片，禁止删除");
            }
        }
        this.removeByIds(ids);
    }

    /**
     * 新增工艺模板
     *
     * @param reqProcessTemplateAddVO 工艺模板
     */
    @Override
    public void add(ReqProcessTemplateAddVO reqProcessTemplateAddVO) {
        log.info("新增工艺模板:{}", JSON.toJSONString(reqProcessTemplateAddVO));
        ProcessTemplate processTemplateOld = this.getOne(new LambdaQueryWrapper<ProcessTemplate>().eq(ProcessTemplate::getTemplateName, reqProcessTemplateAddVO.getTemplateName()));
        if (processTemplateOld != null) {
            throw new CdkitCloudException("工艺模板【"+ reqProcessTemplateAddVO.getTemplateName()+"】已存在");
        }
        ProcessTemplate processTemplate = BeanUtil.toBean(reqProcessTemplateAddVO, ProcessTemplate.class);
        this.save(processTemplate);
    }

    /**
     * 编辑工艺模板
     *
     * @param reqProcessTemplateEditVO 工艺模板
     */
    @Override
    public void edit(ReqProcessTemplateEditVO reqProcessTemplateEditVO) {
        log.info("编辑工艺模板:{}", JSON.toJSONString(reqProcessTemplateEditVO));
        ProcessTemplate processTemplate = BeanUtil.toBean(reqProcessTemplateEditVO, ProcessTemplate.class);
        ProcessTemplate processTemplateOrg = this.getById(reqProcessTemplateEditVO.getId());
        if (!processTemplateOrg.getTemplateName().equals(reqProcessTemplateEditVO.getTemplateName())) {
            ProcessTemplate processTemplateOld = this.getOne(new LambdaQueryWrapper<ProcessTemplate>().eq(ProcessTemplate::getTemplateName, reqProcessTemplateEditVO.getTemplateName()));
            if (processTemplateOld != null) {
                throw new CdkitCloudException("工艺模板【"+ reqProcessTemplateEditVO.getTemplateName()+"】已存在");
            }
        }
        this.updateById(processTemplate);
    }
}
