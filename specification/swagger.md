Swagger 使用规范
===============

### Swagger 使用规范

#### 概述

Swagger 是一种开源的 API 规范和工具集，用于设计、构建、文档化和消费 RESTful Web 服务。遵循一致的 Swagger 使用规范可以提高团队的开发效率、减少沟通成本，并确保 API 的一致性和可读性。

#### 基本原则

1. **清晰明了**: API 描述必须清晰、简洁，易于理解和使用。
2. **一致性**: 遵循一致的命名、结构和格式。
3. **准确性**: API 描述必须准确地反映实际的接口行为和数据模型。
4. **完整性**: API 描述应该包含所有必要的信息，以便开发者能够快速上手。

#### 规范

1. **命名规范**：
    - 使用小写字母和下划线 `_` 分隔单词，例如：`user_profile`.
    - 路径参数使用`{param}`形式，例如：`/users/{user_id}`。
    - 使用清晰的、具有描述性的名称。

2. **HTTP 方法**：
    - 使用标准的 HTTP 方法，如 GET、POST、PUT、DELETE 等。
    - 各种操作的用途必须清晰明了。

3. **参数**：
    - 使用明确的参数命名，如 `user_id`、`email`。
    - 使用合适的数据类型，如 string、integer、boolean 等。
    - 使用 `required` 字段指示必填参数。
    - 提供描述性的参数说明。

4. **响应**：
    - 提供每个操作的预期响应码和响应体结构。
    - 详细描述每个响应的含义，包括成功和错误响应。

5. **模型**：
    - 使用 `definitions` 字段定义数据模型。
    - 每个模型必须有一个清晰的名称和描述。
    - 保持模型的简洁性和一致性。

6. **安全性**：
    - 对需要授权的操作使用合适的认证方式（如 OAuth、JWT 等）。
    - 提供明确的安全要求说明。

7. **版本管理**：
    - API 的版本应该反映在 Swagger 规范中。
    - 使用 `version` 字段明确指定 API 版本。

8. **标签和分组**：
    - 使用 `tags` 字段对 API 进行逻辑分组。
    - 每个标签应该有描述性的名称和说明。

#### 示例
```java
@RestController
@RequestMapping("/test/sftdSwaggerDemo")
@Tag(name = "智能工厂架构组Swagger测试接口")
public class SftdSwaggerDemoController {


    @Operation(summary = "智能工厂架构组Swagger测试接口", description = "智能工厂架构组Swagger测试接口")
    @Parameters({
            @Parameter(name = "param1", description = "参数1", required = true, example = "示例值1"),
            @Parameter(name = "param2", description = "参数2", required = true, example = "示例值2")
    })
    @PostMapping("/swagger/test")
    public void swaggerTest(String param1, String param2) {
        System.out.println("swaggerTest");
    }


    @Operation(summary = "智能工厂架构组Swagger测试接口-有请求体", description = "智能工厂架构组Swagger测试接口-有请求体")
    @PostMapping("/swagger/body")
    public void swaggerTestWithBody(@RequestBody SftdSwaggerDemo sftdSwaggerDemo) {
        System.out.println("swaggerTest");
    }
}
```

```java
@Data
@Schema(description="Swagger测试DEMO对象", name="测试DEMO")
public class SftdSwaggerDemo {

    @Schema(description = "参数1")
    private String param1;

    @Schema(description = "参数2")
    private String param2;
}

```

#### 结论

遵循本规范编写 Swagger 文档，可以使 API 文档更加清晰、准确，并提高开发效率和可维护性。确保所有团队成员都遵循相同的规范，并不断完善和更新文档，以保持 API 描述与实际接口行为的一致性。
