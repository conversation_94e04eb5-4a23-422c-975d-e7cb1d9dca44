

数据库规范
===========

表设计规范
---------------
1. 表名、字段名使用小写字母，单词之间使用下划线分隔。
2. 表名、字段名使用英文单词，不使用拼音。
3. 表名、字段名使用有意义的名字，不使用无意义的名字。
4. 表名、字段名使用复数形式，不使用单数形式。
5. 表名、字段名使用名词，不使用动词。
6. 表名、字段名使用简洁的名字，不使用冗长的名字。
7. 表名、字段名使用统一的命名规范，不使用混乱的命名规范。

1. 主键必须为：ID，类型 [Long(19)] 唯一索引，因为历史原因暂时用String(32)类型 
2. 外键字段命名：{【关联表名】去掉业务前缀}+“_”+ {关联字段名}，例如：order_main_id
3. 区分位: iz_* [String(1)] 1表示是 0表示否，（禁用 is_,代码生成实体有问题 )
4. 状态位: *_status [String(1-2)] 状态字段必须加注释说明每个值代表含义
5. 字段命名，多单词采用下划线分隔 例如：school_id
6. 索引命名： 主键索引命名为：pk_表名缩写_字段名（索引要求全库唯一，为兼容多数据库）；唯一索引命名为：uniq_表名缩写_字段名 或 uk_表名缩写_字段名；普通索引命令为： idx_表名缩写_字段名（表名缩写： 下划线分隔单词首字母组合）
7. 区分、状态、类型字段，尽量用String类型，避免数字类型的一些问题；如果需要考虑性能建议用int类型
   （禁用tinyint类型，需要兼容其他数据库）；
8. 字段默认值（字段尽量不设置默认值，采用编码方式加默认值） 因为在转库的过程中，不同数据库会有丢失默认值的情况


表业务前缀 和 建表标准字段
-----------------------
1. 表命名必须带上业务前缀：例如 sys_开头(系统表前缀)
2. 所有的表加字段：创建时间，创建者，最后更新时间，更新人
3. 逻辑删除字段，del_flag [int(1)]，1表示删除 0表示未删除 ，可选择加
4. 字符串类型字段，varchar类型长度不允许超过1000（过长转库会变类型）
5. 大文本尽量少用，字段类型采用text、longtext，禁用blob系列类型（必须用要确认）

脚本
--------------------------
```sql
ALTER TABLE `表名`
ADD COLUMN `create_by`  varchar(32) NULL COMMENT '创建人',
ADD COLUMN `create_time`  datetime NULL COMMENT '创建时间' AFTER `create_by`,
ADD COLUMN `update_by`  varchar(32) NULL COMMENT '修改人' AFTER `create_time`,
ADD COLUMN `update_time`  datetime NULL COMMENT '修改时间' AFTER `update_by`,
ADD COLUMN `del_flag`  int(1) NULL COMMENT '0表示未删除,1表示删除' AFTER `update_time`;
```

其他说明
-------------------
1. 表字段注释，每个字段必须设置注释说明;
2. 表字段注释，状态类型的字段必须说明取值规则（比如性别sex取值规则）
   比如：'性别 0男,1女'
3. 索引，查询频率高的字段加索引（单字段索引 、组合索引、唯一索引）;
4. 状态、类型字段，尽量用字符串varchar类型1-2长度，少用int类型，避免不必要的问题。