openGaussDb
===========


OpenGauss和GaussDB的区别
-----------------

- GaussDB 是华为基于 OpenGauss 开源社区版本开发的商业发行版数据库，面向政企客户提供高性能、高可靠、高安全、高智能的云原生数据库服务。

- OpenGauss 是华为捐赠给开源社区的企业级开源关系型数据库，由华为云数据库团队主导，致力于打造一个面向未来的数据库产品。

表格

| 特性    | GaussDB   | OpenGauss |
|-------|-----------|-----------|
| 商业模式  | 商业发行版     | 开源        |
| 支持    | 华为云       | 社区        |
| 分布式能力 | 强化        | 基础        |
| 安全性   | 全密态       | 加密        |
| 智能化   | AI调优、故障自愈 | 基础        |
| 生态    | 华为云生态     | 开源社区      |


异常问题
-----------------

- 日志报错 Failed to parse cgroup config file
  - 最终解决方案：疑似镜像问题，降级到3.0.0


docker-compose配置
-----------------

```yaml

gaussDB:
  image: enmotech/opengauss:3.0.0
  container_name: opengauss
  privileged: true
  # restart: always
  ports:
    - "5432:5432"
  environment:
    - GS_PASSWORD="Enmo@123"
  volumes:
    - D:\code\tool\docker\opengauss:/var/lib/opengauss


```

docker run
----------------------

```shell
$ docker run --name opengauss --privileged=true -d -e GS_PASSWORD=Enmo@123 -p 5432:5432 -v /opengauss:/var/lib/opengauss opengauss:3.0.0


```

环境变量
----------------

- **GS_PASSWORD**：使用openGauss镜像的时候，必须设置该参数。该参数值不能为空或者不定义。该参数设置了openGauss数据库的超级用户omm以及测试用户gaussdb的密码。openGauss安装时默认会创建omm超级用户，该用户名暂时无法修改。测试用户gaussdb是在entrypoint.sh中自定义创建的用户。
  - openGauss的密码有复杂度要求：密码长度8个字符以上，必须同时包含大写字母、小写字母、数字、以及特殊符号（特殊符号仅包含"#?!@$%^&*-"，并且"!$&“需要用转义符”\“进行转义）。
- **GS_NODENAME**：指定数据库节点名称，默认为gaussdb。
- **GS_USERNAME**：指定数据库连接用户名，默认为gaussdb。
- **GS_PORT**：指定数据库端口，默认为5432。



连接应用
--------------------------

```xml

<!--  postgresql-->
<dependency>
   <groupId>org.postgresql</groupId>
   <artifactId>postgresql</artifactId>
   <version>42.2.6</version>
</dependency>


```


```yaml


# 增加spring下的配置

spring:
  #postgresql 报错问题
  jpa:
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    properties:
      hibernate:
        temp:
          use_jdbc_metadata_defaults: false


# 修改druid配置
validationQuery: SELECT 1


url: *****************************************
username: postgres
password: root
driver-class-name: org.postgresql.Driver



```

