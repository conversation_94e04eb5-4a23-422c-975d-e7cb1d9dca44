异常抛出统一规范
======



为了代码的统一风格和可读性，对异常的抛出类别进行了约束，对抛出的异常码进行规范化。

异常类使用公共数据平台common 包中的定义类。包括ControllerException、ServiceException、InterceptorException、InfraException
前四位业务域标识，5-8位系统域标识，9-12位服务域标识，后四位用户自定义。例如 0001100122109999 ，0001 代表智能工厂，5-8位1001代表物流系统，2210代表调拨服务 ,9999代表用户自定义的系统错误。
描术语i18n对象如：
```json

{
  "code": "0001100122109999",
  "message": {
    "zh": "客户服务端错误 租户标识：%s",
    "en": "custom server err tenant_id :%s "
  }
}
```
方案：

1. 在代码层次中使用对应的异常类，例如 controller中使用ControllerException
2. 在公共数据平台申请 业务域异常码。
3. 在公共数据平台业务域下申请系统域异常码。
4. 在公共数据平台系统域申请服务域异常码，配置i18n描述。
5. 根据业务调整在平台中优化更新描述。
6. 系统中缓存配置描述并与平台同步。
7. 代码中通过替换描述中的占位符完成特定场景的业务参数表示。



异常类定义:
```java
class ControllerException extends Exception {
    private static final String ControllerExceptionDeclare = "the controller got an exception :";

    public ControllerException(String fmtStr, String[] args) {
        super(ControllerExceptionDeclare + String.format(fmtStr, args));
    }
}


class ServiceException extends Exception {
    private static final String ServiceExceptionDeclare = "the service got an exception :";

    public ServiceException(String fmtStr, String[] args) {
        super(ServiceExceptionDeclare + String.format(fmtStr, args));
    }
}

class InterceptorException extends Exception {
    private static final String InterceptorExceptionDeclare = "the interceptor got an exception :";

    public InterceptorException(String fmtStr, String[] args) {
        super(InterceptorExceptionDeclare + String.format(fmtStr, args));
    }
}

class InfraException extends Exception {
    private static final String InfraExceptionDeclare = "the infra got an exception :";

    public InfraException(String fmtStr, String[] args) {
        super(InfraExceptionDeclare + String.format(fmtStr, args));
    }
}


```

