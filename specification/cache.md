Redis缓存组件开发规范
==========

## 1. 说明

Redis是业界流行的缓存组件，为了规范Redis缓存的使用，避免落入各种问题陷阱，特此编写了此开发规范。本规范结合实际情况，描述了需要遵守的Redis最佳使用规约，及其供参考的最佳实践，供研发团队在项目开发中使用。



本文将出现如下规约术语，其中根据约束力强弱，规约分别有如下三级，
【强制】必须遵守的规约
【推荐】推荐遵守的规约，若无特殊情况，需要遵守该规约
【参考】参考遵守的规约，团队根据实际情况，可以选择性的遵守


每一个规约，根据情况，将有如下附加说明，

- 说明：对规约进一步的引申和解释
- 正例：提供一个正面遵守规约的样例
- 反例：提供一个规约的反面样例，以及真实的错误案例，提醒误区



## 2. 规约

### 2.1. 键值设计

1. 【强制】键的命名使用英文小写和冒号、下划线、数字，其中冒号和下划线不能作为键名的开始和结束，而冒号为命名空间分隔符。不要使用中文和特殊字符作为健名。
   - 正例："pphh:account:user1"
   - 反例1："pphh.账号-USER1"，该反例使用了点、中文、中划线、英文大写。
   - 反例2：":pphh:account:user1_"， 该反例使用了冒号和下划线作为开始和结束

2. 【推荐】键的命名尽量简单、清晰、易懂，长度尽量控制在32个字符以内，不要超过64个字符。
   - 说明：长的键名不仅消耗内存空间，而且会影响键的搜索查询速度。为了保证键的可读性，也不推荐太短的键名，比如"u1000flw"，可以使用"user:1000:followers"来表达。
   - 反例1："pphh:account:loooooooooooooooooooooooooooooooooooooooooog:name"

3. 【推荐】键的命名空间分为三级，格式为 {系统简称}:{应用名}:{业务健名}，命名空间通过冒号区分，公共键值以common表示。
    - 正例1："web:home_page:click"
    - 正例2："common:user_login:verification_code"
    - 反例3："web_home_page_click"

4. 【推荐】键的值存储空间大小尽量在10KB以内，不推荐存储大于1MB的值。
    - 说明：大于1MB的值会导致Redis的性能下降，甚至会导致Redis服务崩溃。
    - 反例1：存储大于1MB的图片、视频、音频等文件。
    - 反例2：存储大于1MB的JSON、XML等文本数据。
5. 【推荐】对于列表键（Hash、List、Set、Zset），尽量控制元素个数不超过千万数量级，若大于这个数量级，建议通过键值切分列表。
    - 说明：大于千万数量级的列表，会导致Redis的性能下降，甚至会导致Redis服务崩溃。
    - 反例1：存储大于千万数量级的用户关注列表。
    - 反例2：存储大于千万数量级的商品库存列表。

6. 【推荐】建议List当做队列来使用。
    - 说明：List是一个双向链表，可以通过lpush和rpop来实现队列的入队和出队操作。

### 2.2. 应用规约

1. 【推荐】业务应用尽量不在Redis存储持久状态，并且需要考虑和实现键值随时失效时的数据更新方法。
2. 【推荐】对于键值，建议设置随机失效时间，避免同一时间大量键值失效，从而导致的缓存雪崩问题。
   - 说明：设置合适的随机失效时间，以避免在同一时间大量key失效。
3. 【推荐】在使用缓存时，关注访问不存在键时导致的缓存穿透问题，关注热key导致的缓存击穿问题。
   - 说明：可以通过监控机制获取当前热key的访问情况，以便改进键值分布，详情见下文的监控告警。
4. 【强制】禁止使用全表搜索命令，比如keys等命令。
   - 说明：请使用scan实现游标式的遍历，建议通过rename-command将keys命令禁用。
5. 【推荐】使用scan实现游标式的遍历，实现全表搜索。
6. 【推荐】对于O(N)时间复杂度的Redis命令，在使用时要预估N的大小，建议N不大于1000。
   - 说明：对于O(N)的Redis命令，当N的数量级不可预知时，则应避免使用。对一个Hash数据执行HGETALL/HKEYS/HVALS命令，通常来说这些命令执行的很快，但如果这个Hash中的元素数量级增大后，耗时就会成倍增长。
7. 【推荐】键的排序、并集、交集等操作时间复杂度都在O(N)，建议这些排序、并集、交集操作放在应用代码里执行。
   - 说明：使用SUNION对两个Set执行并集操作，或使用SORT对List/Set执行排序操作等时，当列表元素很多时，容易导致Redis操作时间很长，形成阻塞。
8. 【强制】禁止如下命令的使用：flushall，flushdb
   - 说明：键值的删除建议通过scan和del命令相配合的方式删除，建议通过rename-command将flushall和flushdb命令禁用。
9. 【推荐】推荐使用Redis的批量操作命令，比如MSET/MGET/HMSET/HMGET等等，以取代多次单键操作。
   - 说明：一个MSET批量操作命令可以替代多次SET操作，这可以大大减少维护网络连接和传输数据所消耗的资源和时间。

   



