---
description: 
globs: *.sql
alwaysApply: false
---
# Cursor Rules - 数据库表设计规范

---

## 命名规范

### 1. 主键
- **字段名**：`id` 
- **类型**：优先 `Long(19)`，历史遗留允许 `String(32)`
- **索引名**：`pk_{表名缩写}_id`  
  （示例：表 `sys_user` → `pk_su_id`）

### 2. 外键
- **字段名**：`{关联表名（去掉业务前缀）}_{字段名}`  
  （示例：关联表 `sys_order` → `order_id`）

### 3. 状态/区分字段
- **布尔标识**：`iz_{状态}`，类型 `String(1)`，取值 `0/1`  
  （示例：`iz_enabled`，禁用 `is_` 前缀）
- **状态字段**：`{业务}_status`，类型 `String(1-2)`，注释需明确取值含义  
  （示例：`order_status` 注释：'0-未支付,1-已支付,2-已取消'）

### 4. 通用字段命名
- **多单词分隔**：下划线格式（如 `school_name`）

---

## 字段规范

### 1. 类型规范
- **状态/类型字段**：优先 `String`，长度 1-2（禁用 `Tinyint`）
- **字符串长度**：`VARCHAR ≤ 1000`，超长用 `TEXT/LONGTEXT`
- **大对象**：禁用 `BLOB` 系列

### 2. 默认值
- **不推荐**数据库默认值，建议代码层控制

### 3. 注释要求
- **所有字段**：必须添加注释
- **状态字段**：需明确枚举值含义（示例：`性别：0-男,1-女`）

---

## 表规范

### 1. 表名前缀
- **业务前缀**：强制添加（如 `sys_`、`order_`）

### 2. 标准字段
所有表需包含以下字段（可选字段按需添加）：
```sql
`create_by`    VARCHAR(32)  COMMENT '创建人',
`create_time`  DATETIME     COMMENT '创建时间',
`update_by`    VARCHAR(32)  COMMENT '更新人',
`update_time`  DATETIME     COMMENT '更新时间',
`del_flag`     INT(1)       COMMENT '0-未删除,1-删除',
`tenant_id` int DEFAULT NULL COMMENT '租户ID',

`update_count` INT          COMMENT '乐观锁版本号'  -- 可选