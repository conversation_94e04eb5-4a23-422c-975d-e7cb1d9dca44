# 项目上下文信息

- 用户需要创建一个Feign接口，根据物料编码查询配方数据并生成字典。入参：materialCode(物料编码)、formulaType(配方类别，默认值3)。查询product_formulation和product_formulation_detail表，返回Map<String,String>，key是boom_id，value第一行是material_code，后续行是material_code+fungible_material_name拼接
- 用户需要创建一个Feign接口，根据物料编码查询配方数据并生成字典。入参：materialCode(物料编码)、formulaType(配方类别，默认值3)。查询product_formulation和product_formulation_detail表，返回Map<String,String>，key是boom_id，value第一行是material_name，后续行是material_name+fungible_material_name拼接
