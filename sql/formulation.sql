ALTER TABLE `sftd-plm`.`product_process_detail` ADD COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注' ;
ALTER TABLE `sftd-plm`.`product_process_route` ADD COLUMN `formulation_id` varchar(36) DEFAULT NULL COMMENT '配方ID' ;


DROP TABLE IF EXISTS `process_material_binding`;
CREATE TABLE `process_material_binding`  (
                                             `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
                                             `material_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物料编码',
                                             `formulation_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配方ID',
                                             `formulation_detail_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配方明细ID',
                                             `process_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工序ID',
                                             `product_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品ID',
                                             `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                             `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
                                             `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
                                             `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
                                             `tenant_id` int(11) NULL DEFAULT NULL COMMENT '租户号',
                                             `del_flag` tinyint(4) NULL DEFAULT 0 COMMENT '是否删除',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工序与投入物绑定' ROW_FORMAT = Dynamic;
