package com.cdkit.plm;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.cdkit.SftdPlmCloudApplication;
import com.cdkit.common.config.TenantContext;
import com.cdkit.modules.plm.product.dto.WriteSignDTO;
import com.cdkit.modules.plm.product.service.CadHandleService;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author：zhao yang
 * @name：CadHandleServiceTest
 * @Date：2024/4/26 14:23
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = SftdPlmCloudApplication.class)
@ActiveProfiles(profiles = "local")
public class CadHandleServiceTest {
    private static final Logger log = LoggerFactory.getLogger(CadHandleServiceTest.class);
    @Autowired
    private CadHandleService cadHandleService;

    @SneakyThrows
    @Test
    public void getDwgFile() {
        TenantContext.setTenant("1004");
        MultipartFile dwgFile = cadHandleService.getDwgFile("1783428342997159937");
        log.info(dwgFile.getOriginalFilename());
        FileUtil.writeBytes(dwgFile.getBytes(), "./" + dwgFile.getOriginalFilename());
        assert dwgFile.getSize() > 0;
    }

    @SneakyThrows
    @Test
    public void transPdf() {
        TenantContext.setTenant("1004");
        MultipartFile multipartFile = cadHandleService.transPdf("1783428342997159937");
        FileUtil.writeBytes(multipartFile.getBytes(), "./test2.pdf");
    }

    /**
     * 写入机打信息
     */
    @Test
    public void writeJdSign() {
        TenantContext.setTenant("1004");
        List<WriteSignDTO> writeSignDTOList = new ArrayList<>();
        WriteSignDTO writeSignDTO = new WriteSignDTO();
        writeSignDTO.setSignKey("设计完成日期_机打信息");
        writeSignDTO.setValue(DateUtil.format(new Date(), "yy-MM-dd HH:mm:ss"));
        writeSignDTO.setSignType(2);
        writeSignDTOList.add(writeSignDTO);
        cadHandleService.writeCadSign("1872151797153841154", writeSignDTOList);
    }

    /**
     * 写入dwg签名信息
     */
    @Test
    public void writeDwgSign() {
        TenantContext.setTenant("1004");
        List<WriteSignDTO> writeSignDTOList = new ArrayList<>();
        WriteSignDTO writeSignDTO = new WriteSignDTO();
        writeSignDTO.setSignKey("设计_DWG签名");
        writeSignDTO.setValue("admin1226");
        writeSignDTO.setSignType(3);
        writeSignDTOList.add(writeSignDTO);
        cadHandleService.writeCadSign("1872189942358798338", writeSignDTOList);
    }

    @Test
    public void getDwgProperties() {
        TenantContext.setTenant("1004");
        ArrayList<String> strTags = new ArrayList<>();
        strTags.add("设计_机打信息");
        Map<String, Object> map = cadHandleService.parseCadProperties("1783428342997159937", strTags);
        log.info("dwg属性：{}", JSONUtil.toJsonStr(map));
        assert map != null;
    }

}
