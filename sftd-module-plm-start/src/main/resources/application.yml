spring:
  main:
    allow-bean-definition-overriding: true
#    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: sftd
        loginPassword: G6BqFaXFu4jWdnus
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,wall,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          url: *****************************************************************************************************************************************************************************
          username: root
          password: sftd@2024123456
          driver-class-name: com.mysql.cj.jdbc.Driver
  data:
    redis:
      database: 1
      host: **************
      password: 689M8TnXs6
      port: 32647
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:com/cdkit/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
   # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
Sftd:
  minio:
    minio_url: http://s3.sf-dev.io
    minio_name: admin
    minio_pass: sftd@2024123456
    bucketName: plm
    backendUrl: ""
  shiro:
    excludeUrls: /file/view
sftd:
  minio:
    minio_url: http://s3.sf-dev.io
    minio_name: admin
    minio_pass: sftd@2024123456
    bucketName: plm
    backendUrl: ""
cdkit:
  shiro:
    excludeUrls: /file/view,/signature/view
  elasticsearch:
    uri: https://es.sf-dev.io
    logs:
      # 启用 elasticsearch log
      enable: true

  #thirdparty:
  #  sys:
  #    url: http://192.168.100.81:30473/sftd-system
  zwcad:
    #    url: http://zwcad.zngc.com
    #    url: cad.sf-dev.io
    sync: true
    folderId: 0
  workflow:
    workFlowServerUrl: http://unitary.sf-dev.io/f2api
    workFlowServerSecret: af446d70-e2c1-11eb-a2a0-0242ac110002
    encryptKey: 'f2bpm$b;v20^uW1:'
    encryptIV: 'f2bpm$b;v20^uW1:'
    corpId: AppDefault
#knife4j:
#  production: true
logging:
  level:
    com.cdkit.modules: debug

#配置熔断的时间监听
hystrix:
  command:
    default:
      execution:
        timeout:
          isolation:
            thread:
              timeoutInMilliseconds: 5000
feign:
  okhttp:
    enabled: true
  httpclient:
    disable-ssl-validation: true
    enabled: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000 # 设置超时3s
  circuitbreaker:
    enabled: true
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: false
saas:
  config:
    ignoretables: did_gen_history,did_rule

