<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sftd-module-plm</artifactId>
        <groupId>com.cnooc.sftd</groupId>
        <version>1.0.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sftd-module-plm-start</artifactId>
    <dependencies>
        <!--引入微服务启动依赖 starter-->
        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>cdkit-boot-starter3-cloud</artifactId>
            <version>3.7.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--system cloud api-->
        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>cdkit-system-cloud-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>cdkit-boot-zngc-core</artifactId>
            <version>3.7.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.cdkitframework.boot</groupId>
            <artifactId>cdkit-boot-starter3-logs</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!--引入业务模块-->
        <dependency>
            <groupId>com.cnooc.sftd</groupId>
            <artifactId>sftd-module-plm-biz</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>spring-cloud-starter-huawei-service-engine</artifactId>
            <version>1.11.7-2022.0.x</version>
            <exclusions>
                <exclusion>
                    <groupId>com.huaweicloud</groupId>
                    <artifactId>spring-cloud-starter-huawei-swagger</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <!-- 打包名称 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
