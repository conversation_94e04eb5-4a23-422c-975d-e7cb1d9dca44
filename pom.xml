<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cdkitframework.boot</groupId>
        <artifactId>cdkit-boot-dependencies</artifactId>
        <version>3.7.0</version>
    </parent>


    <modelVersion>4.0.0</modelVersion>
    <artifactId>sftd-module-plm</artifactId>
    <groupId>com.cnooc.sftd</groupId>
    <version>1.0.1</version>
    <packaging>pom</packaging>

    <properties>
        <revision>1.0.1</revision>
    </properties>

    <!--项目子模块-->
    <modules>
        <module>sftd-module-plm-api</module>
        <module>sftd-module-plm-biz</module>
        <module>sftd-module-plm-start</module>
    </modules>

    <repositories>
        <repository>
            <id>maven-zngc-releases</id>
            <name>Releases</name>
            <url>http://nexus.zngc.com/repository/maven-releases/</url>
        </repository>
        <repository>
            <id>maven-zngc-snapshots</id>
            <name>maven-zngc-snapshots</name>
            <url>http://nexus.zngc.com/repository/maven-snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cdkitframework.boot</groupId>
                <artifactId>cdkit-boot-base-core</artifactId>
                <version>3.7.0</version>
            </dependency>
            <dependency>
                <groupId>com.cdkitframework.boot</groupId>
                <artifactId>cdkit-system-cloud-api</artifactId>
                <version>3.7.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>maven-zngc-releases</id>
            <name>Releases</name>
            <url>http://nexus.zngc.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-zngc-snapshots</id>
            <name>Snapshot</name>
            <url>http://nexus.zngc.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <!-- 打包名称 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>org.codehaus.mojo</groupId>-->
<!--                <artifactId>flatten-maven-plugin</artifactId>-->
<!--                <version>1.5.0</version>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash; 是否更新pom文件，此处还有更高级的用法 &ndash;&gt;-->
<!--                    <updatePomFile>true</updatePomFile>-->
<!--                    <flattenMode>resolveCiFriendliesOnly</flattenMode>-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>flatten</id>-->
<!--                        <phase>process-resources</phase>-->
<!--                        <goals>-->
<!--                            <goal>flatten</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                    <execution>-->
<!--                        <id>flatten.clean</id>-->
<!--                        <phase>clean</phase>-->
<!--                        <goals>-->
<!--                            <goal>clean</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->

        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.json</include>
                    <include>**/*.ftl</include>
                </includes>
            </resource>
        </resources>

    </build>


</project>

