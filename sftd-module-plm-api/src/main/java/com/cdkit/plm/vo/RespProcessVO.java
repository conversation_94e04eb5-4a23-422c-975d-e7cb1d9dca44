package com.cdkit.plm.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/6
 */
@Data
public class RespProcessVO {
    /**主键ID*/
    @Schema(description = "主键ID")
    private String id;
    /**工序编码*/
    @Schema(description = "工序编码")
    private String processCode;
    /**工序ID*/
    @Schema(description = "工序ID")
    private String processId;
    /**工序名称*/
    @Schema(description = "工序名称")
    private String processName;
    /**工序内容*/
    @Schema(description = "工序内容")
    private String processContent;
    /**工序对应步骤*/
    private List<RespProcessDetailVO> processDetail;



}
