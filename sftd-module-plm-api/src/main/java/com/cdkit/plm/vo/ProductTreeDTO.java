package com.cdkit.plm.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import com.cdkit.plm.entity.ProductPartExtend;
import com.cdkit.plm.entity.ProductTree;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author：<PERSON><PERSON> yang
 * @name：ProductDTO
 * @Date：2024/3/26 17:46
 */
@Schema(description = "产品树dto,树形结构")
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductTreeDTO extends ProductTree {

    @Schema(description = "材料")

    private String materialName;

    @Schema(description = "生产类型")

    private String manufactureType;

    @Schema(description = "零件类型")

    private String partType;

    @Schema(description = "处理类型")

    private String handleType;

    @Schema(description = "结构类型")

    private String structType;

    @Schema(description = "重量")

    private String weight;

    @Schema(description = "规格")

    private String specs;

    @Schema(description = "版本号，a、b、c、d增长")

    private String version;

    @Schema(description = "装配数量")

    private BigDecimal assembleQuantity;

    @Schema(description = "装配序号")

    private Integer assembleSort;

    @Schema(description = "装配单位")

    private String assembleUnit;

    /**
     * 标准用量
     */
    @Schema(description = "标准用量")
    private BigDecimal standardQuantity;
    /**
     * 父级工序ID
     */
    @Schema(description = "父级工序ID")
    private String parentProcessId;
    /**
     * 工序在工艺路线中的ID
     */
    @Schema(description = "工序在工艺路线中的ID")
    private String processRouteDetailId;

    /**
     * 工时
     */
    @Schema(description = "工时")
    private BigDecimal workHour;

    @Schema(description = "成品总损耗比例")
    private BigDecimal lossRatio;

    //设计文件部分

    @Schema(description = "文件id，根据文件id再去filecenter的minio的文件")

    private String filePath;

    /**
     * 文档分类，图纸/
     */
    @TableField(value = "category")

    @Schema(description = "文档分类，图纸/")
    private String category;

    /**
     * 文档后缀类型，exb
     */
    @TableField(value = "file_type")

    @Schema(description = "文档后缀类型，exb")
    private String fileType;

    /**
     * 是否已出库
     */
    @TableField(value = "outbound")

    @Schema(description = "是否已出库")
    private Boolean outbound;

    /**
     * 是否已红批
     */
    @TableField(value = "red_batch")

    @Schema(description = "是否已红批")
    private Boolean redBatch;

    /**
     * 是否已签名
     */
    @TableField(value = "sign")

    @Schema(description = "是否已签名")
    private Boolean sign;

    /**
     * 文件大小
     */
    @TableField(value = "file_size")

    @Schema(description = "文件大小")
    private Integer fileSize;

    /**
     * 差异对比
     */

    @Schema(description = "bom转换差异对比，1 橙色，当前层级下未找到相同零件 2黄色，零件相同，但属性不同 3蓝色，图纸有差异 4白色，零件一致/br" +
            "bom比较差异对比：1红色:两个对象的属性不同 2蓝色：新增的对象 3绿色：被删除的对象")
    private String diff;

    private List<ProductTreeDTO> children;

    @Schema(description = "是否是借用节点")
    private Boolean borrow;


    @Schema(description ="原件path")
    private String sourcePath;

    /**计划用量*/
    @Schema(description = "计划用量")
    @TableField(exist = false)
    private BigDecimal planNum;

    @Schema(description = "工序名称")
    @TableField(exist = false)
    private String processName;

    @Schema(description = "工序名id")
    @TableField(exist = false)
    private String processId;

    @Schema(description = "产品分类")
    @TableField(exist = false)
    private String productTypeName;

    @Schema(description = "料件类型")
    @TableField(exist = false)
    private String itemType;

    @Schema(description = "定制属性列表")
    private List<ProductPartExtend> customizedProperties;

    @Schema(description = "原材料线边仓存量")
    @TableField(exist = false)
    private BigDecimal rawMaterialLineStock;

    @Schema(description = "仓库存量")
    @TableField(exist = false)
    private Double warehouseStock;

    @Schema(description = "产品种类代码")
    private String productTypeCode;
    @Schema(description = "是否替代物:1-是 2-否")
    private Integer alternativeFlag;
    @Schema(description = "被替代物物料编码")
    private String beforeMaterialCode;


}
