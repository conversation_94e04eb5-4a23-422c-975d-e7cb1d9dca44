package com.cdkit.plm.vo;

import com.baomidou.mybatisplus.annotation.TableField;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/6
 */
@Data
public class ProductProcessRouteVO {
    /**ID*/
    @Excel(name = "ID", width = 15)
    @Schema(description = "ID")
    private String id;
    /**产品ID*/
    @Excel(name = "产品ID", width = 15)
    @Schema(description = "产品ID")
    private String productId;
    /**
     * 物料编码
     */
    @Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;
    /**通过对应节点导入工艺路线*/
    @Excel(name = "通过对应节点导入工艺路线", width = 15)
    @Schema(description = "通过对应节点导入工艺路线")
    private String currProductId;
    /**工艺路线编码*/
    @Excel(name = "工艺路线ID", width = 15)
    @Schema(description = "工艺路线ID")
    private String processRouteId;
    /**工序编码*/
    @Excel(name = "工序编码", width = 15)
    @Schema(description = "工序编码")
    private String processCode;
    /**工序ID*/
    @Excel(name = "工序ID", width = 15)
    @Schema(description = "工序ID")
    private String processId;
    /**工序名称*/
    @Excel(name = "工序名称", width = 15)
    @Schema(description = "工序名称")
    private String processName;
    /**工序序号*/
    @Excel(name = "工序序号", width = 15)
    @Schema(description = "工序序号")
    private String processNumber;
    /**工序内容*/
    @Excel(name = "工序内容", width = 15)
    @Schema(description = "工序内容")
    private String processContent;
    /**产出单位id*/
    @Excel(name = "产出单位id", width = 15)
    @Schema(description = "产出单位id")
    private String outputUnitId;
    /**产出副单位id*/
    @Excel(name = "产出副单位id", width = 15)
    @Schema(description = "产出副单位id")
    private String outputSecUnitId;
    /**
     * 装配数量
     */
    @Schema(description = "装配数量")
    private BigDecimal assembleQuantity;
    /**
     * 标准用量
     */
    @Schema(description = "标准用量")
    private BigDecimal standardQuantity;
    /**委外标记*/
    @Schema(description = "委外标记")
    private Integer outsourcedFlag;
    /**完工需质检*/
    @Schema(description = "完工需质检")
    private String isQuality;
    /**是否是关键工序*/
    @Schema(description = "是否是关键工序")
    private Integer isKey;
    /**开工前序报工比例*/
    @Excel(name = "开工前序报工比例", width = 15)
    @Schema(description = "开工前序报工比例")
    private BigDecimal reportRatio;
    @Schema(description = "工时")
    private BigDecimal workHour;
}
