package com.cdkit.plm.api;

import com.cdkit.plm.entity.*;
import com.cdkit.plm.vo.ProductFileVO;
import com.cdkit.plm.vo.RespProcessVO;
import com.cdkit.plm.vo.ScheduleProcessRouteResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * PLM产品档案相关接口
 * <AUTHOR>
 */
@Component
@FeignClient(contextId = "plmBaseApi", value = "sftd-plm",  url = "${thirdparty.sys.url:}")
public interface IPlmBaseApi {
    /**
     * 查询产品档案
     * @param code 产品编码
     * @return 返回结果
     */
    //todo 加启用禁用逻辑
    @GetMapping(value = "/plm/api/getProductFile")
    ProductFileVO getProductFile(@RequestParam(name="code") String code);

    /**
     * 查询产品档案
     * @param code BOM编码
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/getProductFileByCode")
    ProductFileVO getProductFileByCode(@RequestParam(name="code") String code);

    /**
     * 查询产品档案
     * @param id 产品BOM ID
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/getProductFileById")
    ProductFileVO getProductFileById(@RequestParam(name="id") String id);

    /**
     * 查询产品档案列表
     * @return 返回结果
     */
    //todo 加启用禁用逻辑
    @GetMapping(value = "/plm/api/getProductFileList")
    List<ProductTree> getProductFileList();

    /**
     * 查询工序列表
     * @param processIds 工序ID（可逗号分割）
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/mdProcess/list")
    List<MdProcess> queryMdProcessList(@RequestParam(name="processIds") String processIds);

    /**
     * 查询工序步骤
     * @param processIds 工序ID（可逗号分割）
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/mdProcessDetail/list")
    List<MdProcessDetail> queryMdProcessDetailList(@RequestParam(name="processIds") String processIds);

    /**
     * 查询工序步骤
     * @param productId 产品ID
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/getProcessDetail")
    RespProcessVO getProcessDetail(@RequestParam(name = "productId") String productId);

    /**
     * 查询工序详细信息
     * @param processSerialNum 工序序号
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/getProcess")
    MdProcess getProcess(@RequestParam(name = "processSerialNum") String processSerialNum);


    /**
     * 查询工艺路线明细
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/getProcessRouteDetail")
    MdProcessRouteDetail getProcess(@RequestBody MdProcessRouteDetail processRouteDetail);

    /**
     * 查询物料或工序实验项
     * @param refKeys 物料编码/工艺路线明细ID
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/experimentTerm/list")
    Map<String, List<ProcessExperimentTerm>> queryExperimentTermList(@RequestParam(name="refKeys") String refKeys);

    /**
     * 查询生产工时
     * @param processRouteDetailId 工序在工艺路线中的ID
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/getWorkHour")
    BigDecimal getWorkHour(@RequestParam(name="processRouteDetailId") String processRouteDetailId);


    /**
     * 查询工序信息
     * @param
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/queryProcess")
    MdProcess queryProcess(@RequestParam(name = "id") String id);

    /**
     * 通过产线编码查询物料编码
     * 智能排程调用
     * @param productionLineCode 产线编码
     * @return 返回结果
     */
    @GetMapping(value = "/plm/api/schedule/materialCode/list")
    List<ScheduleProcessRouteResp> getMaterialCodeByproductLineCode(@RequestParam(name = "productionLineCode") String productionLineCode);

    @GetMapping(value = "/plm/api/getExtendMaterialCode")
    String getExtendMaterialCode(@RequestParam(name = "id") String id);

    @GetMapping(value = "/plm/api/queryFungibleByCode")
     String queryFungibleByCode(@RequestParam(name = "boomDataId") String boomDataId,@RequestParam(name = "materialCode") String materialCode);

    @GetMapping(value = "/plm/api/queryPart")
     ProductPart queryPart(@RequestParam(name = "boomDataId") String boomDataId, @RequestParam(name = "materialCode") String materialCode);

    /**
     * 根据物料编码查询配方字典数据
     * @param materialCode 物料编码
     * @return 返回字典数据，key是material_code或fungible_material_codes，value是material_name或fungible_material_name
     */
    @GetMapping(value = "/plm/api/queryFormulationDictByMaterialCode")
    Map<String, String> queryFormulationDictByMaterialCode(@RequestParam(name = "materialCode") String materialCode);

}
