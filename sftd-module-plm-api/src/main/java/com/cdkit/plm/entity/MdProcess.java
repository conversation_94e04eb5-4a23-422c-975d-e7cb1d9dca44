package com.cdkit.plm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkit.common.aspect.annotation.Dict;
import com.cdkitframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 工序定义
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Schema(description="md_process对象", name="工序定义")
@Data
@TableName("md_process")
public class MdProcess implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    private String id;
	/**工序编码*/
	@Excel(name = "工序编码", width = 15)
    @Schema(description = "工序编码")
    private String processCode;
	/**工序名称*/
	@Excel(name = "工序名称", width = 15)
    @Schema(description = "工序名称")
    private String processName;
	/**产出单位id*/
	@Excel(name = "产出单位id", width = 15)
    @Schema(description = "产出单位id")
    private String outputUnitId;
	/**工序分类id*/
	@Excel(name = "工序分类id", width = 15)
    @Schema(description = "工序分类id")
    private String processTypeId;
	/**工艺类型*/
	@Excel(name = "工艺类型", width = 15)
    @Schema(description = "工艺类型")
    @Dict(dicCode = "process_kind")
    private String processKind;
	/**可委外*/
	@Excel(name = "可委外", width = 15)
    @Schema(description = "可委外")
    private Integer outsourced;
	/**是否排产*/
	@Excel(name = "是否排产", width = 15)
    @Schema(description = "是否排产")
    private Integer scheduling;
	/**是否追溯*/
	@Excel(name = "是否追溯", width = 15)
    @Schema(description = "是否追溯")
    private Integer traceable;
	/**启用状态*/
	@Excel(name = "启用状态", width = 15)
    @Schema(description = "启用状态")
    private Integer useStatus;
    /**是否是关键工序*/
    @Excel(name = "是否是关键工序", width = 15)
    @Schema(description = "是否是关键工序")
    private Integer isKey;
	/**序号*/
	@Excel(name = "序号", width = 15)
    @Schema(description = "序号")
    private String number;
	/**步骤名称*/
	@Excel(name = "步骤名称", width = 15)
    @Schema(description = "步骤名称")
    private String stepName;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
}
