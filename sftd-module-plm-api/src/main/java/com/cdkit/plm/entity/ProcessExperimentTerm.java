package com.cdkit.plm.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.cdkitframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 工艺实验项
 * @Author: cdkit-boot
 * @Date:   2024-09-06
 * @Version: V1.0
 */
@Data
@TableName("process_experiment_term")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="process_experiment_term对象", name="工艺实验项")
public class ProcessExperimentTerm implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
	/**物料编码/工艺路线明细ID*/
	@Excel(name = "物料编码/工艺路线明细ID", width = 15)
    @Schema(description = "物料编码/工艺路线明细ID")
    private String refKey;
	/**实验项ID*/
	@Excel(name = "实验项ID", width = 15)
    @Schema(description = "实验项ID")
    private String termId;
	/**实验项编码*/
	@Excel(name = "实验项编码", width = 15)
    @Schema(description = "实验项编码")
    private String code;
	/**实验项名称*/
	@Excel(name = "实验项名称", width = 15)
    @Schema(description = "实验项名称")
    private String name;
	/**实验项介绍*/
	@Excel(name = "实验项介绍", width = 15)
    @Schema(description = "实验项介绍")
    private String intro;

	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**是否删除标识*/
	@Excel(name = "是否删除标识", width = 15)
    @Schema(description = "是否删除标识")
    @TableLogic
    private Integer delFlag;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private String tenantId;
	/**部门编码*/
    @Schema(description = "部门编码")
    private String sysOrgCode;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;

    /**
     * 检验标准
     */
    @Excel(name = "检验标准名称", width = 15)
    @Schema(description = "检验标准名称")
    private String standardName;
    @Schema(description = "检验标准id")
    private String standardId;
    @Schema(description = "指标")
    private String targetValue;
}
