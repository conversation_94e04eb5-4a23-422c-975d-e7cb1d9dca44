package com.cdkit.plm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.cdkitframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 工艺路线明细
 * @Author: mc
 * @Date:   2024-07-31
 * @Version: V1.0
 */
@Schema(description="md_process_route_detail对象", name="工艺路线明细")
@Data
@TableName("md_process_route_detail")
public class MdProcessRouteDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "id")
    private String id;
	/**工艺路线id*/
    @Schema(description = "工艺路线id")
    private String processRouteId;
	/**工序序号*/
	@Excel(name = "工序序号", width = 15)
    @Schema(description = "工序序号")
    private String processNumber;
	/**工序id*/
	@Excel(name = "工序id", width = 15)
    @Schema(description = "工序id")
    private String processId;
	/**换算率*/
	@Excel(name = "换算率", width = 15)
    @Schema(description = "换算率")
    private Integer conversionRate;
	/**车间id*/
	@Excel(name = "车间id", width = 15)
    @Schema(description = "车间id")
    private String workshopId;
	/**设备id*/
	@Excel(name = "设备id", width = 15)
    @Schema(description = "设备id")
    private String deviceId;
    /**工序内容*/
    @Excel(name = "工序内容", width = 15)
    @Schema(description = "工序内容")
    private String processContent;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
	/**委外标记*/
	@Excel(name = "委外标记", width = 15)
    @Schema(description = "委外标记")
    private Integer outsourcedFlag;
    /**是否是关键工序*/
    @Excel(name = "是否是关键工序", width = 15)
    @Schema(description = "是否是关键工序")
    private Integer isKey;
    /**完工需质检*/
    @Excel(name = "完工需质检", width = 15)
    @Schema(description = "完工需质检")
    private String isQuality;
    /**开工前序报工比例*/
    @Excel(name = "开工前序报工比例", width = 15)
    @Schema(description = "开工前序报工比例")
    private BigDecimal reportRatio;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建日期")
    private Date createTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新日期")
    private Date updateTime;
	/**是否删除*/
	@Excel(name = "是否删除", width = 15)
    @Schema(description = "是否删除")
    @TableLogic
    private Integer delFlag;
	/**租户号*/
	@Excel(name = "租户号", width = 15)
    @Schema(description = "租户号")
    private Integer tenantId;
}
