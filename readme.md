
SFTD BOOT 开发说明
=====================

[Swagger标准](./specification/swagger.md)

[数据库标准](./specification/database.md)

[缓存规范（Key值...）](./specification/cache.md)

[东方通接入手册](./specification/Tlink-Q.md)

[openGauss接入手册](./specification/opengauss.md)

[异常码规范](./specification/exception.md)

代码层次结构
-----------------------------------
```
├─sftd-module-demo    示例代码
│  ├─sftd-demo-start  启动包
│  │  ├─application  
│  ├─sftd-demo-biz    demo系统管理权限等功能
│  │  ├─modules       APP层
│  │  │  ├─controller   │
│  │  │  ├─processor  Domiain层
│  │  │  │  ├─handler   │
│  │  │  ├─service      │
│  │  │  ├─mapper     infra层    
│  │  │  ├─client       │
│  │  │  ├─model        │
│  │  │  ├─entity       │
│  │  │  ├─vo           │
│  ├─sftd-demo-api
│  │  ├─modules       facade层 
│  │  │  ├─api          │ 
│  │  │  │  ├─client    │
│  │  │  │  ├─fallback  │
│  │  │  ├─bo           │
│  │  │  ├─dto          │
│  │  │  ├─entity       │
│  │  │  ├─vo           │
